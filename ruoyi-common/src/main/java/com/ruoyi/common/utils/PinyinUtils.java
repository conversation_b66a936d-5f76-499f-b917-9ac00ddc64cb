package com.ruoyi.common.utils;

import org.dromara.hutool.extra.pinyin.PinyinUtil;

public class PinyinUtils {

    private static final String NOT_LETTER = "[^A-Za-z]";


    public static String getSimplePinyin(String str, String separator, boolean onlyPinyin) {
        String result = PinyinUtil.getFirstLetter(str, separator);
        if (onlyPinyin) {
            return result.replaceAll(NOT_LETTER, "");
        } else {
            return result;
        }
    }

    public static String getFullPinyin(String str, String separator, boolean onlyPinyin) {
        String result = PinyinUtil.getPinyin(str, separator);
        if (onlyPinyin) {
            return result.replaceAll(NOT_LETTER, "");
        } else {
            return result;
        }
    }

}
