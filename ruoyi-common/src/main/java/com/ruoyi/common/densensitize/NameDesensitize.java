package com.ruoyi.common.densensitize;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.github.annotation.Desensitize;

import java.lang.annotation.*;

/**
 * 姓名脱敏
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@Desensitize(desensitization = NameDesensitization.class)
@Documented
public @interface NameDesensitize {
}
