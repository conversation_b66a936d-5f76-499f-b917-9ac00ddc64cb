package com.ruoyi.common.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import org.dromara.hutool.core.data.IdcardUtil;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Constraint(validatedBy = ValidIdCard.IdCardValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidIdCard {

    String message() default "无效的身份证号";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    class IdCardValidator implements ConstraintValidator<ValidIdCard, String> {

        @Override
        public boolean isValid(String idCardNo, ConstraintValidatorContext context) {
            return IdcardUtil.isValidCard(idCardNo);
        }

    }

}

