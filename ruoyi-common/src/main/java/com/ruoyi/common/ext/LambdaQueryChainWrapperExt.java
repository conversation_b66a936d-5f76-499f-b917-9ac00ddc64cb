package com.ruoyi.common.ext;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import org.dromara.hutool.core.array.ArrayUtil;
import org.dromara.hutool.core.text.StrValidator;

import java.util.Collection;

public record LambdaQueryChainWrapperExt<T>(LambdaQueryChainWrapper<T> wrapper) {

    public LambdaQueryChainWrapperExt<T> likeIfPresent(SFunction<T, ?> column, String value) {
        if (value != null && !value.isEmpty()) {
            wrapper.like(column, value);
        }
        return this;
    }

    public LambdaQueryChainWrapperExt<T> inIfPresent(SFunction<T, ?> column, Collection<?> values) {
        if (ArrayUtil.isAllNotEmpty(values) && !ArrayUtil.isEmpty(values)) {
            wrapper.in(column, values);
        }
        return this;
    }

    public LambdaQueryChainWrapperExt<T> inIfPresent(SFunction<T, ?> column, Object... values) {
        if (ArrayUtil.isAllNotEmpty(values) && !ArrayUtil.isEmpty(values)) {
            wrapper.in(column, values);
        }
        return this;
    }

    public LambdaQueryChainWrapperExt<T> eqIfPresent(SFunction<T, ?> column, Object val) {
        if (ArrayUtil.isNotEmpty(val)) {
            wrapper.eq(column, val);
        }
        return this;
    }

    public LambdaQueryChainWrapperExt<T> eqIfNotEmpty(SFunction<T, ?> column, Object val) {
        if (val instanceof String str) {
            if (StrValidator.isNotEmpty(str)) {
                wrapper.eq(column, val);
            }
            return this;
        }

        if (ArrayUtil.isNotEmpty(val)) {
            wrapper.eq(column, val);
        }
        return this;
    }

    public LambdaQueryChainWrapperExt<T> neIfPresent(SFunction<T, ?> column, Object val) {
        if (ArrayUtil.isNotEmpty(val)) {
            wrapper.ne(column, val);
        }
        return this;
    }

    public LambdaQueryChainWrapperExt<T> gtIfPresent(SFunction<T, ?> column, Object val) {
        if (val != null) {
            wrapper.gt(column, val);
        }
        return this;
    }

    public LambdaQueryChainWrapperExt<T> geIfPresent(SFunction<T, ?> column, Object val) {
        if (val != null) {
            wrapper.ge(column, val);
        }
        return this;
    }

    public LambdaQueryChainWrapperExt<T> ltIfPresent(SFunction<T, ?> column, Object val) {
        if (val != null) {
            wrapper.lt(column, val);
        }
        return this;
    }

    public LambdaQueryChainWrapperExt<T> leIfPresent(SFunction<T, ?> column, Object val) {
        if (val != null) {
            wrapper.le(column, val);
        }
        return this;
    }

    public LambdaQueryChainWrapperExt<T> betweenIfPresent(SFunction<T, ?> column, Object val1, Object val2) {
        if (val1 != null && val2 != null) {
            wrapper.between(column, val1, val2);
        } else if (val1 != null) {
            wrapper.ge(column, val1);
        } else if (val2 != null) {
            wrapper.le(column, val2);
        }
        return this;
    }
}
