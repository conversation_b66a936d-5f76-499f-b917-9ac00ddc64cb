package com.ruoyi.web.controller.system;

import org.dromara.hutool.core.data.IdcardUtil;
import org.dromara.hutool.core.text.StrUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.IpUtils;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.shiro.authc.PscNurseToken;
import com.ruoyi.framework.web.service.ConfigService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import space.lzhq.ph.domain.PscOrder;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@Controller
public class SysLoginController extends BaseController {

    /**
     * 是否开启记住我功能
     */
    @Value("${shiro.rememberMe.enabled: false}")
    private boolean rememberMe;

    private final ConfigService configService;

    public SysLoginController(ConfigService configService) {
        this.configService = configService;
    }

    @GetMapping("/login")
    public String login(HttpServletRequest request, HttpServletResponse response, ModelMap mmap) {
        // 如果是Ajax请求，返回Json字符串。
        if (ServletUtils.isAjaxRequest(request)) {
            return ServletUtils.renderString(response, "{\"code\":\"1\",\"msg\":\"未登录或登录超时。请重新登录\"}");
        }

        // 是否开启记住我
        mmap.put("isRemembered", rememberMe);
        // 是否开启用户注册
        mmap.put("isAllowRegister", Convert.toBool(configService.getKey("sys.account.registerUser"), false));
        return "login";
    }

    @GetMapping("/nurseLogin")
    public ModelAndView nurseLogin(
            @RequestParam String empno,
            @RequestParam String name,
            @RequestParam(required = false) String patientId,
            @RequestParam(required = false) String admissionNumber,
            @RequestParam(required = false) String patientName,
            @RequestParam(required = false) String patientMobile,
            @RequestParam(required = false) String patientDepartment,
            @RequestParam(required = false) String patientBedNumber,
            @RequestParam(required = false) String patientIdCardNo,
            HttpServletRequest request
    ) {
        PscOrder pscOrder = new PscOrder();
        pscOrder.setPatientId(patientId);
        pscOrder.setAdmissionNumber(admissionNumber);
        pscOrder.setPatientName(patientName);
        pscOrder.setPatientMobile(patientMobile);
        pscOrder.setPatientDepartment(patientDepartment);
        pscOrder.setPatientBedNumber(patientBedNumber);
        pscOrder.setPatientIdCardNo(patientIdCardNo);
        if (StrUtil.isNotBlank(patientIdCardNo) && IdcardUtil.isValidCard(patientIdCardNo)) {
            pscOrder.setPatientSex(String.valueOf(1 - IdcardUtil.getGender(patientIdCardNo)));
            pscOrder.setPatientBirthday(LocalDate.parse(IdcardUtil.getBirth(pscOrder.getPatientIdCardNo()),
                    DateTimeFormatter.ofPattern("yyyyMMdd")));
        }
        getSession().setAttribute("pscOrder", pscOrder);

        PscNurseToken token = new PscNurseToken(empno, name, IpUtils.getIpAddr(request));
        Subject subject = SecurityUtils.getSubject();
        try {
            subject.login(token);

            return new ModelAndView("redirect:/index");
        } catch (AuthenticationException e) {
            return new ModelAndView("redirect:/nurseLoginError");
        }
    }

    @GetMapping("/nurseLoginError")
    public String nurseLoginError() {
        return "nurseLoginError";
    }

    @PostMapping("/login")
    @ResponseBody
    public AjaxResult ajaxLogin(String username, String password, Boolean rememberMe) {
        UsernamePasswordToken token = new UsernamePasswordToken(username, password, rememberMe);
        Subject subject = SecurityUtils.getSubject();
        try {
            subject.login(token);
            return success();
        } catch (AuthenticationException e) {
            String msg = "用户或密码错误";
            if (StringUtils.isNotEmpty(e.getMessage())) {
                msg = e.getMessage();
            }
            return error(msg);
        }
    }

    @GetMapping("/unauth")
    public String unauth() {
        return "error/unauth";
    }
}
