### 部署环境

#### 掌医密码

admin
@Xj@bvSsvc7nHwheqGB7

developer
HpprK3oXDP#v367bi$Gr

#### MySQL

添加最小搜索 token 的配置。

```cnf
# /etc/mysql/mysql.conf.d/mysqld.cnf

ft_min_word_len = 2
ngram_token_size = 2
```

## 掌医端口映射情况

**************:8888（外网IP地址） 
→ *************:8888（掌医服务地址） 域名 zy.xj-etyy.com

## 院内服务情况

### HIS接口

- http://127.0.0.1:20001/doc.html
- 用户名：developer
- 密码：<EMAIL>

#### HIS服务网络情况

*************（掌医服务器）
→ ***************:8090(外侧网闸）
→ ***************:8090（内侧网闸）
→ ***************:8090（接口服务器）

### 医保移动支付
**************:8080
***************:8080

测试环境：
**************:8082
***************:8082

### 电子发票

单位代码：302032
单位名称：新疆维吾尔自治区儿童医院
开票点：001
接口地址：http://**************:8082/einv_forward/api/i
APPID：P999U2E8T252NHFISQDC3FET263481BU
APPKEY：XAHOFRI9MBM88KVM

测试数据：620821202108283118  史国锦

电子发票外网网闸地址
*************:8082 -> **************:8082

## 服务器登录

### 深信服堡垒机

https://**************:6677/
用户名：掌医
密码：8.4mw!FGi9un!sJ6qAzV
过期时间：永不过期

堡垒机
https://138.139.100.5/
用户名：掌医
密码:123456

登陆上堡垒机后别管下面的内容，也不要管堡垒机的 WEB、RDP 页面，直接打开电脑的 RDP 远程，远程掌医服务器 ************* ，密码和下面的一样。

服务器
192.168.250.160
***************
administrator
xjetvm_2021

时钟同步服务器：192.168.250.185

## 病历复印流程

用户填写

