# 诊间充值接口文档

## 📋 需求背景

### 问题描述
门诊部反馈，窗口操作人员在HIS系统中看不到患者缴费的项目是否已做，如果费用已缴，项目却没做，或者只做了一部分，需要给患者退款时非常麻烦。

### 原有方案的问题
1. **直接扣费风险**: 原来的逻辑是患者余额充足时直接调用HIS扣费，可能出现扣费成功但项目未完成的情况
2. **安全隐患**: 前端传入支付金额，存在被篡改的风险
3. **退款复杂**: 当项目未完成需要退款时，操作繁琐且容易出错

## 🎯 解决方案

### 核心思路
- **余额验证**: 仅验证患者余额是否充足，不进行实际扣费
- **充值机制**: 余额不足时，创建充值订单补足差额
- **安全保障**: 从HIS系统获取真实的费用金额，杜绝前端篡改

### 业务流程
```mermaid
flowchart TD
    A[患者选择缴费项目] --> B[调用充值接口]
    B --> C{检查余额是否充足}
    C -->|余额充足| D[返回无需充值]
    C -->|余额不足| E[创建充值订单]
    E --> F[拉起支付收银台]
    F --> G[用户完成充值]
    G --> H[HIS系统后续处理扣费]
```

## 🔧 新接口介绍

我们新增了两个充值接口，分别对应微信和支付宝平台：

### 1. 微信充值接口
**接口名称**: `checkBalanceAndCreateWeixinRechargeOrder`

### 2. 支付宝充值接口  
**接口名称**: `checkBalanceAndCreateAlipayRechargeOrder`

---

## 📘 API接口文档

### 微信充值接口

#### 基本信息
- **URL**: `POST /api/zhenjian/checkBalanceAndCreateWeixinRechargeOrder`
- **限制**: 仅微信小程序可调用
- **描述**: 检查患者余额并创建微信充值订单

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `settlementIds` | String | ✅ | 结算单号，多个用逗号分隔 |

#### 请求示例
```javascript
// 单个结算单
{
    "settlementIds": "CF202312010001"
}

// 多个结算单
{
    "settlementIds": "CF202312010001,YJ202312010002"
}
```

#### 响应格式

**余额充足时**:
```json
{
    "code": 200,
    "msg": "余额充足，无需充值",
    "data": {
        "needRecharge": 0,
        "totalAmount": 150.00,
        "balance": 200.00,
        "rechargeAmount": 0
    }
}
```

**余额不足时**:
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "appId": "wx1234567890",
        "timeStamp": "1703123456",
        "nonceStr": "abc123def456",
        "package": "prepay_id=wx123456789",
        "signType": "RSA",
        "paySign": "signature123",
        "zyOrderNo": "MZ20231201123456",
        "needRecharge": 1,
        "totalAmount": 150.00,
        "balance": 50.00,
        "rechargeAmount": 100.00
    }
}
```

---

### 支付宝充值接口

#### 基本信息
- **URL**: `POST /api/zhenjian/checkBalanceAndCreateAlipayRechargeOrder`
- **限制**: 仅支付宝小程序可调用
- **描述**: 检查患者余额并创建支付宝充值订单

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `settlementIds` | String | ✅ | 结算单号，多个用逗号分隔 |

#### 请求示例
```javascript
{
    "settlementIds": "CF202312010001,YJ202312010002"
}
```

#### 响应格式

**余额充足时**:
```json
{
    "code": 200,
    "msg": "余额充足，无需充值",
    "data": {
        "needRecharge": 0,
        "totalAmount": 150.00,
        "balance": 200.00,
        "rechargeAmount": 0
    }
}
```

**余额不足时**:
```json
{
    "code": 200,
    "msg": "操作成功", 
    "data": {
        "alipayResponse": {
            "tradeNo": "2023120122001234567890",
            "outTradeNo": "MZ20231201123456"
        },
        "zyOrderNo": "MZ20231201123456",
        "needRecharge": 1,
        "totalAmount": 150.00,
        "balance": 50.00,
        "rechargeAmount": 100.00
    }
}
```

---

## 💻 前端集成示例

### 微信小程序示例

```javascript
/**
 * 诊间缴费充值
 * @param {string} settlementIds 结算单号，逗号分隔
 */
async function handleZhenjianRecharge(settlementIds) {
    try {
        const response = await wx.request({
            url: '/api/zhenjian/checkBalanceAndCreateWeixinRechargeOrder',
            method: 'POST',
            data: {
                settlementIds: settlementIds
            }
        });

        const { code, msg, data } = response.data;
        
        if (code !== 200) {
            wx.showToast({ title: msg, icon: 'error' });
            return;
        }

        // 检查是否需要充值
        if (data.needRecharge === 0) {
            // 余额充足，无需充值
            wx.showToast({ title: '余额充足，无需充值', icon: 'success' });
            return;
        }

        // 余额不足，拉起微信支付
        const payResult = await wx.requestPayment({
            appId: data.appId,
            timeStamp: data.timeStamp,
            nonceStr: data.nonceStr,
            package: data.package,
            signType: data.signType,
            paySign: data.paySign
        });

        // 支付成功后的处理
        if (payResult.errMsg === 'requestPayment:ok') {
            wx.showToast({ title: '充值成功', icon: 'success' });
            // 刷新页面或更新余额显示
            refreshBalance();
        }

    } catch (error) {
        console.error('诊间充值失败:', error);
        wx.showToast({ title: '操作失败，请重试', icon: 'error' });
    }
}
```

### 支付宝小程序示例

```javascript
/**
 * 诊间缴费充值
 * @param {string} settlementIds 结算单号，逗号分隔  
 */
async function handleZhenjianRecharge(settlementIds) {
    try {
        const response = await my.request({
            url: '/api/zhenjian/checkBalanceAndCreateAlipayRechargeOrder',
            method: 'POST',
            data: {
                settlementIds: settlementIds
            }
        });

        const { code, msg, data } = response.data;
        
        if (code !== 200) {
            my.showToast({ content: msg, type: 'fail' });
            return;
        }

        // 检查是否需要充值
        if (data.needRecharge === 0) {
            // 余额充足，无需充值
            my.showToast({ content: '余额充足，无需充值', type: 'success' });
            return;
        }

        // 余额不足，拉起支付宝支付
        const payResult = await my.tradePay({
            tradeNO: data.alipayResponse.tradeNo
        });

        // 支付成功后的处理
        if (payResult.resultCode === '9000') {
            my.showToast({ content: '充值成功', type: 'success' });
            // 刷新页面或更新余额显示
            refreshBalance();
        }

    } catch (error) {
        console.error('诊间充值失败:', error);
        my.showToast({ content: '操作失败，请重试', type: 'fail' });
    }
}
```

---

## 🔄 迁移指南

### 废弃接口列表
以下接口已标记为废弃，建议尽快迁移：

| 废弃接口 | 替代接口 | 迁移截止时间 |
|----------|----------|--------------|
| `createWxpayOrder` | `checkBalanceAndCreateWeixinRechargeOrder` | 待定 |
| `createAlipayOrder` | `checkBalanceAndCreateAlipayRechargeOrder` | 待定 |
| `refund` | 无（将被删除） | 待定 |

### 迁移步骤

#### 1. 更新接口调用
```javascript
// 废弃的调用方式 ❌
wx.request({
    url: '/api/zhenjian/createWxpayOrder',
    data: {
        settlementIds: 'CF202312010001',
        amount: 150.00  // 不再需要传入金额
    }
});

// 新的调用方式 ✅
wx.request({
    url: '/api/zhenjian/checkBalanceAndCreateWeixinRechargeOrder',
    data: {
        settlementIds: 'CF202312010001'  // 仅需要结算单号
    }
});
```

#### 2. 更新响应处理逻辑
```javascript
// 新增余额检查逻辑
if (data.needRecharge === 0) {
    // 余额充足的处理
    handleSufficientBalance();
} else {
    // 余额不足，需要充值的处理
    handleRecharge(data);
}
```

---

## 🔒 安全性改进

### 1. 金额安全
- **问题**: 前端传入的金额可能被篡改
- **解决**: 服务端从HIS系统获取真实金额

### 2. 数据一致性
- **问题**: 前端金额与HIS系统可能不一致
- **解决**: 实时查询HIS系统，确保数据准确

### 3. 业务安全
- **问题**: 直接扣费可能导致项目未完成
- **解决**: 仅充值不扣费，由HIS系统后续处理

---

## 📊 响应字段说明

### 通用字段
| 字段名 | 类型 | 描述 |
|--------|------|------|
| `needRecharge` | Number | 是否需要充值 (0: 不需要, 1: 需要) |
| `totalAmount` | Number | 待支付总金额 |
| `balance` | Number | 当前余额 |
| `rechargeAmount` | Number | 需要充值的金额 |

### 微信支付特有字段
| 字段名 | 类型 | 描述 |
|--------|------|------|
| `appId` | String | 微信小程序AppId |
| `timeStamp` | String | 时间戳 |
| `nonceStr` | String | 随机字符串 |
| `package` | String | 预支付交易会话标识 |
| `signType` | String | 签名类型 |
| `paySign` | String | 签名 |
| `zyOrderNo` | String | 系统订单号 |

### 支付宝支付特有字段
| 字段名 | 类型 | 描述 |
|--------|------|------|
| `alipayResponse` | Object | 支付宝响应对象 |
| `zyOrderNo` | String | 系统订单号 |

---

## ❓ 常见问题

### Q1: 为什么不再需要传入金额参数？
**A**: 为了提升安全性，系统现在直接从HIS获取真实的费用金额，避免前端传入的金额被篡改。

### Q2: 余额充足时为什么返回成功而不是错误？
**A**: 新的设计统一返回成功状态，通过`needRecharge`字段区分是否需要充值，便于前端统一处理。

### Q3: 充值成功后费用会自动扣除吗？
**A**: 不会。充值只是补足余额，实际的费用扣除由HIS系统后续处理，避免了扣费成功但项目未完成的问题。

### Q4: 原有的废弃接口什么时候会删除？
**A**: 废弃接口会保留一段时间以确保兼容性，具体删除时间会另行通知。建议尽早迁移到新接口。

---

*最后更新时间: 2025年06月16日* 