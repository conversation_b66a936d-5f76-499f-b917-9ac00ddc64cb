# 🚀 部署脚本备份功能说明

## 📋 概述

基于Git Bash的部署脚本系统，提供自动备份功能，确保在部署新版本前安全备份旧版本，并提供快速回退机制。该系统采用现代化的Shell脚本设计，具有彩色输出、模块化架构和强大的错误处理能力。

## 💻 环境要求

### 🎯 目标平台
- **操作系统**：Windows 10/11
- **运行环境**：Git Bash / Git for Windows

### 🛠️ 必需工具
- **Git**：版本控制和代码拉取
- **Maven**：项目构建和打包
- **date命令**：时间戳生成（Git Bash内置）
- **sc命令**：Windows服务控制（Windows内置）

### 📂 路径格式
脚本使用Git Bash兼容的Unix风格路径：
- **Windows路径**：`d:\zyxcx\backup\`
- **Git Bash路径**：`/d/zyxcx/backup/`
- **脚本执行**：`./deploy.sh` （需要执行权限）

### ⚡ 技术特性
- **时间戳生成**：使用bash内置的`date`命令，无需外部依赖
- **跨平台兼容**：Git Bash提供统一的Unix环境
- **智能服务控制**：自动检测服务状态，避免不必要的操作
- **容错设计**：服务不存在或已停止时不影响部署流程

## 🚀 快速开始

### 1️⃣ 准备环境
```bash
# 打开Git Bash终端，进入项目目录
cd /path/to/zhangyi-xjetyy

# 为所有脚本添加执行权限
chmod +x *.sh
```

### 2️⃣ 运行部署
```bash
# 查看帮助信息
./deploy.sh --help

# 执行完整部署
./deploy.sh

# 或跳过mospital构建
./deploy.sh --skip-mospital
```

### 3️⃣ 回退操作（如需要）
```bash
# 启动交互式回退工具
./rollback.sh
```

## ✨ 新增功能

### 🔄 自动备份
- **备份时机**：在复制新jar文件之前自动备份当前版本
- **备份位置**：`/d/zyxcx/backup/` 目录
- **命名规则**：`zyxcx_YYYYMMDD_HHMMSS.jar`（包含时间戳）
- **自动清理**：保留最近10个版本的备份，自动删除更早的备份

### ⚡ 交互式回退
- **独立脚本**：`rollback.sh` 专用回退工具
- **交互选择**：列出所有可用备份，用户可自由选择任意版本
- **时间显示**：友好的时间格式显示，方便识别版本
- **安全确认**：回退前需要用户二次确认操作

### 🎨 现代化特性
Git Bash脚本系统提供以下高级特性：

- 🎨 **彩色输出**：美观的彩色日志显示，提升用户体验
- 🛡️ **错误处理**：强大的错误处理机制，确保操作安全
- 📖 **帮助系统**：内置详细帮助信息，支持 `--help` 参数
- 🔧 **模块化设计**：函数化编程架构，易于维护和扩展
- 🚀 **环境检查**：启动前自动检查必要工具的可用性
- 📝 **详细日志**：丰富的操作反馈信息，过程透明可见
- 🔄 **智能清理**：可靠的备份文件管理和自动清理
- 🎯 **参数验证**：完整的命令行参数解析和验证

## 🎯 使用方法

### 正常部署

```bash
# 完整部署（包含mospital构建）
./deploy.sh

# 跳过mospital构建
./deploy.sh --skip-mospital

# 查看帮助信息
./deploy.sh --help
```

### 版本回退

```bash
# 启动交互式回退工具
./rollback.sh
```

## 📁 文件结构

### 应用文件结构
```
/d/zyxcx/
├── zyxcx.jar                    # 当前运行版本
└── backup/                      # 备份目录
    ├── zyxcx_20241201_143020.jar   # 备份文件（时间戳命名）
    ├── zyxcx_20241201_120015.jar
    ├── zyxcx_20241130_165432.jar
    ├── zyxcx_20241130_094521.jar
    └── zyxcx_20241129_173018.jar
```

### 脚本文件列表
```
项目根目录/
├── deploy.sh                    # 部署脚本（Git Bash）
├── rollback.sh                  # 回退脚本（Git Bash）
└── docs/
    └── deployment-backup-guide.md  # 本文档
```

## 🔧 配置说明

### 关键配置变量
```bash
readonly BACKUP_DIR="/d/zyxcx/backup"    # 备份目录
readonly MAX_BACKUPS=10                  # 保留备份数量
```

### 可自定义选项
- **备份目录**：修改脚本中的 `BACKUP_DIR` 常量来改变备份存储位置
- **保留数量**：修改脚本中的 `MAX_BACKUPS` 常量来调整保留的备份文件数量（当前为10个）

## 🚨 错误处理

### 部署失败自动提示
当新版本部署后服务启动失败时，脚本会：
1. 显示错误信息
2. 提示可能的问题原因
3. 自动显示回退命令供使用

### 备份失败处理
- 如果备份目录创建失败，部署会停止
- 如果备份操作失败，部署会停止
- 确保在备份成功后才进行新版本部署

### 智能服务管理
脚本现在具备智能的服务状态检测和管理能力：

#### 🔍 服务状态检测
- **自动检测**：在执行操作前自动检查服务当前状态
- **状态分类**：准确识别RUNNING、STOPPED、或未知状态
- **容错处理**：服务不存在时不影响部署流程

#### 🚦 停止服务逻辑
- **RUNNING状态**：执行正常停止流程并等待完成
- **STOPPED状态**：跳过停止操作，直接继续部署
- **服务不存在**：记录警告但允许继续部署
- **未知状态**：尝试发送停止命令后继续流程

#### ▶️ 启动服务逻辑
- **STOPPED状态**：执行正常启动流程
- **RUNNING状态**：跳过启动操作，服务已在运行
- **服务不存在**：返回错误，需要检查服务配置
- **未知状态**：尝试启动命令

## 📝 操作流程

### 正常部署流程
1. **准备阶段**：检查参数，决定是否构建mospital
2. **构建阶段**：
   - 拉取最新代码
   - Maven构建打包
3. **部署阶段**：
   - 停止服务
   - **🆕 备份当前版本**
   - 复制新版本jar
   - 启动服务
4. **完成提示**：显示部署成功信息和回退命令

### 回退流程
1. **启动工具**：运行 `./rollback.sh` 脚本
2. **扫描备份**：自动扫描并列出所有可用备份
3. **交互选择**：用户从列表中选择要回退的版本
4. **确认操作**：显示选择信息，用户二次确认
5. **执行回退**：
   - 停止服务
   - 恢复选定的备份文件
   - 启动服务
6. **完成提示**：显示回退成功信息和当前版本

## 🎮 回退工具详细说明

### 交互式界面
`rollback.sh` 提供了用户友好的交互界面：

```
========================================
           🔄 jar包回退工具
========================================

[INFO] 在备份目录中找到 3 个备份文件：
[INFO] 备份目录: /d/zyxcx/backup

[1] zyxcx_20241201_143020.jar (2024-12-01 14:30:20)
[2] zyxcx_20241201_120015.jar (2024-12-01 12:00:15)
[3] zyxcx_20241130_165432.jar (2024-11-30 16:54:32)

[0] 退出回退操作

请选择要回退的备份版本 (0-3):
```

### 主要特性
- **📋 完整列表**：显示所有可用的备份文件
- **🕒 时间格式化**：友好的日期时间显示格式
- **🔢 数字选择**：简单的数字输入选择方式
- **🛡️ 双重确认**：选择后需要再次确认操作
- **❌ 安全退出**：可随时选择0退出操作
- **📝 详细反馈**：操作过程中提供详细的状态信息

### 使用示例
1. 运行 `./rollback.sh`
2. 查看备份列表，选择要回退的版本号
3. 确认选择的备份文件信息
4. 输入 Y 确认执行回退
5. 等待回退完成，验证系统功能

## ⚠️ 注意事项

1. **磁盘空间**：确保备份目录有足够空间存储jar文件
2. **权限要求**：确保脚本有权限操作备份目录和Windows服务
3. **备份清理**：脚本会自动清理超出数量限制的旧备份
4. **回退确认**：回退操作需要用户手动确认，避免误操作
5. **路径转换**：Git Bash自动处理Windows路径转换，无需手动修改
6. **执行权限**：首次使用需要为脚本添加执行权限（`chmod +x *.sh`）

## 🔍 故障排除

### 常见问题
- **备份目录创建失败**：检查磁盘权限和空间
- **服务停止超时**：检查服务状态和依赖
- **jar文件复制失败**：检查文件权限和磁盘空间
- **没有找到备份文件**：确认之前是否有过成功的部署

### 日志信息
脚本会输出详细的操作日志，包括：
- 备份文件路径
- 清理的旧备份文件
- 回退操作的详细步骤
- 错误信息和建议

## 🎉 优势

1. **安全性**：每次部署前自动备份，降低部署风险
2. **灵活性**：交互式回退，可选择任意历史版本
3. **智能性**：自动管理备份文件，避免磁盘空间浪费
4. **易用性**：友好的界面设计，操作简单直观
5. **可视性**：详细的日志输出，操作过程透明可见
6. **轻量级**：使用bash内置命令，减少外部依赖，提高可靠性
