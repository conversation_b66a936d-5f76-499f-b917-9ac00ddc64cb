package space.lzhq.ph.common

enum class ServiceType(val code: Int, val description: String) {
    /**
     * 门诊预交金
     */
    MZ(1, "门诊预交金"),

    /**
     * 住院预交金
     */
    ZY(2, "住院预交金"),

    /**
     * 诊间缴费
     */
    ZJ(3, "诊间缴费"),

    /**
     * 病历邮寄
     */
    BL(4, "病历邮寄"),

    /**
     * 微信商家转账
     */
    TF(5, "微信商家转账")
    ;

    companion object {
        private val map = values().associateBy { it.code }
        fun fromCode(code: Int): ServiceType? = map[code]
    }

}
