package space.lzhq.ph.common

import org.dromara.hutool.core.math.Money
import org.dromara.hutool.core.util.RandomUtil
import org.mospital.jackson.DateTimeFormatters
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.concurrent.atomic.AtomicInteger

object PaymentKit {
    // 微信支付统一下单接口的商户订单号，最大长度为32，参见：https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_1
    fun newOrderId(type: ServiceType): String =
        type.name + LocalDateTime.now().format(DateTimeFormatters.PURE_DATETIME_FORMATTER) + RandomUtil.randomNumbers(4)

    fun fenToYuan(fen: Long): BigDecimal {
        return Money(BigDecimal.valueOf(fen / 100.0)).amount
    }

    fun yuanToFen(yuan: BigDecimal): Long {
        return Money(yuan.setScale(2)).cent
    }

    fun yuanToFen(yuan: Double): Long {
        return yuanToFen(yuan.toBigDecimal())
    }

    fun diffInFen(yuan1: BigDecimal, yuan2: BigDecimal): Long {
        return yuanToFen(yuan1) - yuanToFen(yuan2)
    }

    /**
     * HIS充值退款服务连续失败的次数
     */
    val CONTINUOUS_HIS_FAILURES: AtomicInteger = AtomicInteger(0)

}