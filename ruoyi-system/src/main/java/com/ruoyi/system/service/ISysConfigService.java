package com.ruoyi.system.service;

import com.ruoyi.system.domain.SysConfig;

import java.util.List;

/**
 * 参数配置 服务层
 *
 * <AUTHOR>
 */
public interface ISysConfigService {
    /**
     * 查询参数配置信息
     *
     * @param configId 参数配置ID
     * @return 参数配置信息
     */
    SysConfig selectConfigById(Long configId);

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数键名
     * @return 参数键值
     */
    String selectConfigByKey(String configKey);

    boolean getBoolean(String attrName, boolean defaultValue);

    void updateBoolean(String attrName, boolean value);

    /**
     * 查询参数配置列表
     *
     * @param config 参数配置信息
     * @return 参数配置集合
     */
    List<SysConfig> selectConfigList(SysConfig config);

    /**
     * 新增参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    int insertConfig(SysConfig config);

    /**
     * 修改参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    int updateConfig(SysConfig config);

    /**
     * 批量删除参数配置信息
     *
     * @param ids 需要删除的数据ID
     */
    void deleteConfigByIds(String ids);

    /**
     * 加载参数缓存数据
     */
    void loadingConfigCache();

    /**
     * 清空参数缓存数据
     */
    void clearConfigCache();

    /**
     * 重置参数缓存数据
     */
    void resetConfigCache();

    /**
     * 每个用户最多绑定几个就诊人
     *
     * @return
     */
    int getMaxPatientPerClient();

    /**
     * 预约挂号是否被禁用了
     */
    boolean isYyghDisabled();

    /**
     * 核酸检测是否被禁用了
     */
    boolean isCovidDisabled();

    /**
     * 获取是否将核检样品推送到LIS
     *
     * @return 是否将核检样品推送到LIS
     */
    boolean isNatPushLisEnabled();

    /**
     * 获取应用域名
     *
     * @return 应用域名
     */
    String getAppHost();

    /**
     * 获取核检采样认证码
     *
     * @return 核检采样认证码
     */
    String getNatAuthCode();

    /**
     * 获取核检混样码长度
     *
     * @return 核检混样码长度
     */
    int getNatMixCodeLength();

    /**
     * 校验参数键名是否唯一
     *
     * @param config 参数信息
     * @return 结果
     */
    boolean checkConfigKeyUnique(SysConfig config);

    /**
     * 根据键名查询公开配置信息
     *
     * @param configKey 参数键名
     * @return 配置信息，如果不存在或不公开则返回null
     */
    SysConfig selectPublicConfigByKey(String configKey);

    /**
     * 根据键名查询公开配置的字符串值
     *
     * @param configKey 参数键名
     * @return 配置值，如果不存在或不公开则返回null
     */
    String selectPublicConfigValueByKey(String configKey);

    /**
     * 根据键名查询公开配置的布尔值
     *
     * @param configKey 参数键名
     * @param defaultValue 默认值
     * @return 配置值，如果不存在或不公开则返回null
     */
    Boolean getPublicBoolean(String configKey, Boolean defaultValue);
}
