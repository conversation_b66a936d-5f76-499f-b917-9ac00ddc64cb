<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.ZhuyuanPatientMapper">

    <resultMap type="ZhuyuanPatient" id="ZhuyuanPatientResult">
        <result property="id" column="id"/>
        <result property="idCardNo" column="id_card_no"/>
        <result property="patientNo" column="patient_no"/>
        <result property="zhuyuanNo" column="zhuyuan_no"/>
        <result property="name" column="name"/>
        <result property="gender" column="gender"/>
        <result property="departmentCode" column="department_code"/>
        <result property="departmentName" column="department_name"/>
        <result property="bedNo" column="bed_no"/>
        <result property="comeTime" column="come_time"/>
        <result property="leaveTime" column="leave_time"/>
        <result property="state" column="state"/>
        <result property="openId" column="open_id"/>
        <result property="unionId" column="union_id"/>
        <result property="admissionNumber" column="admission_number"/>
    </resultMap>

    <sql id="selectZhuyuanPatientVo">
        select id,
               id_card_no,
               patient_no,
               zhuyuan_no,
               name,
               gender,
               department_code,
               department_name,
               bed_no,
               come_time,
               leave_time,
               state,
               open_id,
               union_id,
               admission_number
        from ph_zhuyuan_patient
    </sql>

    <select id="selectZhuyuanPatientList" parameterType="ZhuyuanPatient" resultMap="ZhuyuanPatientResult">
        <include refid="selectZhuyuanPatientVo"/>
        <where>
            <if test="idCardNo != null  and idCardNo != ''">and id_card_no = #{idCardNo}</if>
            <if test="patientNo != null  and patientNo != ''">and patient_no = #{patientNo}</if>
            <if test="zhuyuanNo != null  and zhuyuanNo != ''">and zhuyuan_no = #{zhuyuanNo}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="gender != null ">and gender = #{gender}</if>
            <if test="departmentCode != null  and departmentCode != ''">and department_code = #{departmentCode}</if>
            <if test="departmentName != null  and departmentName != ''">and department_name like concat('%',
                #{departmentName}, '%')
            </if>
            <if test="bedNo != null  and bedNo != ''">and bed_no = #{bedNo}</if>
            <if test="comeTime != null ">and come_time = #{comeTime}</if>
            <if test="leaveTime != null ">and leave_time = #{leaveTime}</if>
            <if test="state != null  and state != ''">and state = #{state}</if>
            <if test="openId != null  and openId != ''">and open_id = #{openId}</if>
            <if test="unionId != null  and unionId != ''">and union_id = #{unionId}</if>
            <if test="admissionNumber != null  and admissionNumber != ''">and admission_number = #{admissionNumber}</if>
        </where>
    </select>

    <select id="selectZhuyuanPatientById" parameterType="Long" resultMap="ZhuyuanPatientResult">
        <include refid="selectZhuyuanPatientVo"/>
        where id = #{id}
    </select>

    <insert id="insertZhuyuanPatient" parameterType="ZhuyuanPatient" useGeneratedKeys="true" keyProperty="id">
        insert into ph_zhuyuan_patient
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="idCardNo != null and idCardNo != ''">id_card_no,</if>
            <if test="patientNo != null and patientNo != ''">patient_no,</if>
            <if test="zhuyuanNo != null">zhuyuan_no,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="gender != null">gender,</if>
            <if test="departmentCode != null and departmentCode != ''">department_code,</if>
            <if test="departmentName != null and departmentName != ''">department_name,</if>
            <if test="bedNo != null and bedNo != ''">bed_no,</if>
            <if test="comeTime != null">come_time,</if>
            <if test="leaveTime != null">leave_time,</if>
            <if test="state != null and state != ''">state,</if>
            <if test="openId != null">open_id,</if>
            <if test="unionId != null">union_id,</if>
            <if test="admissionNumber != null">admission_number,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="idCardNo != null and idCardNo != ''">#{idCardNo},</if>
            <if test="patientNo != null and patientNo != ''">#{patientNo},</if>
            <if test="zhuyuanNo != null">#{zhuyuanNo},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="gender != null">#{gender},</if>
            <if test="departmentCode != null and departmentCode != ''">#{departmentCode},</if>
            <if test="departmentName != null and departmentName != ''">#{departmentName},</if>
            <if test="bedNo != null and bedNo != ''">#{bedNo},</if>
            <if test="comeTime != null">#{comeTime},</if>
            <if test="leaveTime != null">#{leaveTime},</if>
            <if test="state != null and state != ''">#{state},</if>
            <if test="openId != null">#{openId},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="admissionNumber != null">#{admissionNumber},</if>
        </trim>
    </insert>

    <update id="updateZhuyuanPatient" parameterType="ZhuyuanPatient">
        update ph_zhuyuan_patient
        <trim prefix="SET" suffixOverrides=",">
            <if test="idCardNo != null and idCardNo != ''">id_card_no = #{idCardNo},</if>
            <if test="patientNo != null and patientNo != ''">patient_no = #{patientNo},</if>
            <if test="zhuyuanNo != null">zhuyuan_no = #{zhuyuanNo},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="departmentCode != null and departmentCode != ''">department_code = #{departmentCode},</if>
            <if test="departmentName != null and departmentName != ''">department_name = #{departmentName},</if>
            <if test="bedNo != null and bedNo != ''">bed_no = #{bedNo},</if>
            <if test="comeTime != null">come_time = #{comeTime},</if>
            <if test="leaveTime != null">leave_time = #{leaveTime},</if>
            <if test="state != null and state != ''">state = #{state},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="unionId != null">union_id = #{unionId},</if>
            <if test="admissionNumber != null">admission_number = #{admissionNumber},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteZhuyuanPatientById" parameterType="Long">
        delete
        from ph_zhuyuan_patient
        where id = #{id}
    </delete>

    <delete id="deleteZhuyuanPatientByIds" parameterType="String">
        delete from ph_zhuyuan_patient where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectZhuyuanPatientByOpenId" parameterType="String" resultMap="ZhuyuanPatientResult">
        <include refid="selectZhuyuanPatientVo"/>
        where open_id = #{openId}
    </select>
</mapper>