<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.MedicalRecordMailingMapper">

    <resultMap type="MedicalRecordMailing" id="MedicalRecordMailingResult">
        <result property="id" column="id"/>
        <result property="patientNo" column="patient_no"/>
        <result property="jzCardNo" column="jz_card_no"/>
        <result property="zhuyuanNo" column="zhuyuan_no"/>
        <result property="name" column="name"/>
        <result property="mobile" column="mobile"/>
        <result property="idCardNo" column="id_card_no"/>
        <result property="openid" column="openid"/>
        <result property="address" column="address"/>
        <result property="status" column="status"/>
        <result property="medicalRecordPages" column="medical_record_pages"/>
        <result property="copyingFee" column="copying_fee"/>
        <result property="expressFee" column="express_fee"/>
        <result property="zyPayNo" column="zy_pay_no"/>
        <result property="wxPayNo" column="wx_pay_no"/>
        <result property="courierNumber" column="courier_number"/>
        <result property="shutdownReason" column="shutdown_reason"/>
        <result property="operatorId" column="operator_id"/>
        <result property="operatorName" column="operator_name"/>
        <result property="confirmedTime" column="confirmed_time"/>
        <result property="mailedTime" column="mailed_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectMedicalRecordMailingVo">
        select id,
               patient_no,
               jz_card_no,
               zhuyuan_no,
               name,
               mobile,
               id_card_no,
               openid,
               address,
               status,
               medical_record_pages,
               copying_fee,
               express_fee,
               zy_pay_no,
               wx_pay_no,
               courier_number,
               shutdown_reason,
               operator_id,
               operator_name,
               confirmed_time,
               mailed_time,
               create_time,
               update_time
        from ph_medical_record_mailing
    </sql>

    <select id="selectMedicalRecordMailingList" parameterType="MedicalRecordMailing"
            resultMap="MedicalRecordMailingResult">
        <include refid="selectMedicalRecordMailingVo"/>
        <where>
            <if test="patientNo != null  and patientNo != ''">and patient_no = #{patientNo}</if>
            <if test="jzCardNo != null  and jzCardNo != ''">and jz_card_no = #{jzCardNo}</if>
            <if test="zhuyuanNo != null  and zhuyuanNo != ''">and zhuyuan_no = #{zhuyuanNo}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="mobile != null  and mobile != ''">and mobile = #{mobile}</if>
            <if test="idCardNo != null  and idCardNo != ''">and id_card_no = #{idCardNo}</if>
            <if test="openid != null  and openid != ''">and openid = #{openid}</if>
            <if test="address != null  and address != ''">and address = #{address}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="zyPayNo != null  and zyPayNo != ''">and zy_pay_no = #{zyPayNo}</if>
            <if test="wxPayNo != null  and wxPayNo != ''">and wx_pay_no = #{wxPayNo}</if>
            <if test="courierNumber != null  and courierNumber != ''">and courier_number = #{courierNumber}</if>
            <if test="operatorName != null  and operatorName != ''">and operator_name like concat('%', #{operatorName},
                '%')
            </if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''">
                and create_time between #{params.beginCreateTime} and #{params.endCreateTime}
            </if>
        </where>
    </select>

    <select id="selectMedicalRecordMailingById" parameterType="Long" resultMap="MedicalRecordMailingResult">
        <include refid="selectMedicalRecordMailingVo"/>
        where id = #{id}
    </select>

    <insert id="insertMedicalRecordMailing" parameterType="MedicalRecordMailing" useGeneratedKeys="true"
            keyProperty="id">
        insert into ph_medical_record_mailing
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="patientNo != null and patientNo != ''">patient_no,</if>
            <if test="jzCardNo != null and jzCardNo != ''">jz_card_no,</if>
            <if test="zhuyuanNo != null and zhuyuanNo != ''">zhuyuan_no,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="idCardNo != null and idCardNo != ''">id_card_no,</if>
            <if test="openid != null and openid != ''">openid,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="status != null">status,</if>
            <if test="medicalRecordPages != null">medical_record_pages,</if>
            <if test="copyingFee != null">copying_fee,</if>
            <if test="expressFee != null">express_fee,</if>
            <if test="zyPayNo != null">zy_pay_no,</if>
            <if test="wxPayNo != null">wx_pay_no,</if>
            <if test="courierNumber != null">courier_number,</if>
            <if test="shutdownReason != null">shutdown_reason,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="operatorName != null">operator_name,</if>
            <if test="confirmedTime != null">confirmed_time,</if>
            <if test="mailedTime != null">mailed_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="patientNo != null and patientNo != ''">#{patientNo},</if>
            <if test="jzCardNo != null and jzCardNo != ''">#{jzCardNo},</if>
            <if test="zhuyuanNo != null and zhuyuanNo != ''">#{zhuyuanNo},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="idCardNo != null and idCardNo != ''">#{idCardNo},</if>
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="status != null">#{status},</if>
            <if test="medicalRecordPages != null">#{medicalRecordPages},</if>
            <if test="copyingFee != null">#{copyingFee},</if>
            <if test="expressFee != null">#{expressFee},</if>
            <if test="zyPayNo != null">#{zyPayNo},</if>
            <if test="wxPayNo != null">#{wxPayNo},</if>
            <if test="courierNumber != null">#{courierNumber},</if>
            <if test="shutdownReason != null">#{shutdownReason},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null">#{operatorName},</if>
            <if test="confirmedTime != null">#{confirmedTime},</if>
            <if test="mailedTime != null">#{mailedTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateMedicalRecordMailing" parameterType="MedicalRecordMailing">
        update ph_medical_record_mailing
        <trim prefix="SET" suffixOverrides=",">
            <if test="patientNo != null and patientNo != ''">patient_no = #{patientNo},</if>
            <if test="jzCardNo != null and jzCardNo != ''">jz_card_no = #{jzCardNo},</if>
            <if test="zhuyuanNo != null and zhuyuanNo != ''">zhuyuan_no = #{zhuyuanNo},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="idCardNo != null and idCardNo != ''">id_card_no = #{idCardNo},</if>
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="status != null">status = #{status},</if>
            <if test="medicalRecordPages != null">medical_record_pages = #{medicalRecordPages},</if>
            <if test="copyingFee != null">copying_fee = #{copyingFee},</if>
            <if test="expressFee != null">express_fee = #{expressFee},</if>
            <if test="zyPayNo != null">zy_pay_no = #{zyPayNo},</if>
            <if test="wxPayNo != null">wx_pay_no = #{wxPayNo},</if>
            <if test="courierNumber != null">courier_number = #{courierNumber},</if>
            <if test="shutdownReason != null">shutdown_reason = #{shutdownReason},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="operatorName != null">operator_name = #{operatorName},</if>
            <if test="confirmedTime != null">confirmed_time = #{confirmedTime},</if>
            <if test="mailedTime != null">mailed_time = #{mailedTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMedicalRecordMailingById" parameterType="Long">
        delete
        from ph_medical_record_mailing
        where id = #{id}
    </delete>

    <delete id="deleteMedicalRecordMailingByIds" parameterType="String">
        delete from ph_medical_record_mailing where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>