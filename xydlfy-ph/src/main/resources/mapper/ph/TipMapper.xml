<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.TipMapper">
    
    <resultMap type="Tip" id="TipResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="style"    column="style"    />
        <result property="effectiveTime"    column="effective_time"    />
        <result property="expireTime"    column="expire_time"    />
    </resultMap>

    <sql id="selectTipVo">
        select id, code, title, content, style, effective_time, expire_time from tip
    </sql>

    <select id="selectTipList" parameterType="Tip" resultMap="TipResult">
        <include refid="selectTipVo"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="params.beginEffectiveTime != null and params.beginEffectiveTime != '' and params.endEffectiveTime != null and params.endEffectiveTime != ''"> and effective_time between #{params.beginEffectiveTime} and #{params.endEffectiveTime}</if>
            <if test="params.beginExpireTime != null and params.beginExpireTime != '' and params.endExpireTime != null and params.endExpireTime != ''"> and expire_time between #{params.beginExpireTime} and #{params.endExpireTime}</if>
        </where>
    </select>
    
    <select id="selectTipById" parameterType="Long" resultMap="TipResult">
        <include refid="selectTipVo"/>
        where id = #{id}
    </select>
    <select id="selectByCodeList" resultType="space.lzhq.ph.domain.Tip">
        SELECT * FROM tip
        <where>
            <if test="codeList != null and codeList.size() > 0">
                AND code IN
                <foreach item="code" collection="codeList" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="insertTip" parameterType="Tip" useGeneratedKeys="true" keyProperty="id">
        insert into tip
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="style != null and style != ''">style,</if>
            <if test="effectiveTime != null">effective_time,</if>
            <if test="expireTime != null">expire_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="style != null and style != ''">#{style},</if>
            <if test="effectiveTime != null">#{effectiveTime},</if>
            <if test="expireTime != null">#{expireTime},</if>
         </trim>
    </insert>

    <update id="updateTip" parameterType="Tip">
        update tip
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="style != null and style != ''">style = #{style},</if>
            <if test="effectiveTime != null">effective_time = #{effectiveTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTipById" parameterType="Long">
        delete from tip where id = #{id}
    </delete>

    <delete id="deleteTipByIds" parameterType="String">
        delete from tip where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>