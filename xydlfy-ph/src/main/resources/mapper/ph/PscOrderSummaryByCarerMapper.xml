<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.PscOrderSummaryByCarerMapper">
    <select id="summary" resultType="space.lzhq.ph.domain.PscOrderSummaryByCarer">
        select carer_employee_no                              as carerEmployee<PERSON><PERSON>,
               carer_name                                     as carer<PERSON><PERSON>,
               carer_mobile                                   as carerMobile,
               sum(if(service_type = 1, 1, 0))                as wheelchairOrderCount,
               sum(if(service_type = 2, 1, 0))                as sickbedOrderCount,
               sum(if(service_type = 3, 1, 0))                as guideOrderCount,
               sum(if(service_type = 4, exam_items_count, 0)) as surgeryOrderCount,
               sum(if(service_type = 1, exam_items_count, 0)) as wheelchairItemCount,
               sum(if(service_type = 2, exam_items_count, 0)) as sickbedItemCount,
               sum(if(service_type = 3, exam_items_count, 0)) as guideItemCount,
               sum(if(service_type = 4, exam_items_count, 0)) as surgeryItemCount,
               sum(if(service_type != 4, 1, 0))               as totalOrderCount,
               sum(exam_items_count)                          as totalItemCount
        from psc_order
        where confirm_time between #{minConfirmTime} and #{maxConfirmTime}
          and status = 99
        group by carer_employee_no
    </select>

    <select id="summaryByCarerAndDepartment" resultType="space.lzhq.ph.domain.PscOrderSummaryByCarerAndDepartment">
        select carer_employee_no                              as carerEmployeeNo,
               carer_name                                     as carerName,
               carer_mobile                                   as carerMobile,
               #{minConfirmTime}                              as minConfirmTime,
               #{maxConfirmTime}                              as maxConfirmTime,
               patient_department                             as patientDepartment,
               sum(if(service_type = 1, 1, 0))                as wheelchairOrderCount,
               sum(if(service_type = 2, 1, 0))                as sickbedOrderCount,
               sum(if(service_type = 3, 1, 0))                as guideOrderCount,
               sum(if(service_type = 4, exam_items_count, 0)) as surgeryOrderCount,
               sum(if(service_type = 1, exam_items_count, 0)) as wheelchairItemCount,
               sum(if(service_type = 2, exam_items_count, 0)) as sickbedItemCount,
               sum(if(service_type = 3, exam_items_count, 0)) as guideItemCount,
               sum(if(service_type = 4, exam_items_count, 0)) as surgeryItemCount,
               sum(if(service_type != 4, 1, 0))               as totalOrderCount,
               sum(exam_items_count)                          as totalItemCount
        from psc_order
        where confirm_time between #{minConfirmTime} and #{maxConfirmTime}
          and carer_employee_no = #{carerEmployeeNo}
          and status = 99
        group by patient_department
        union
        select carer_employee_no                              as carerEmployeeNo,
               carer_name                                     as carerName,
               carer_mobile                                   as carerMobile,
               #{minConfirmTime}                              as minConfirmTime,
               #{maxConfirmTime}                              as maxConfirmTime,
               '合计'                                         as patientDepartment,
               sum(if(service_type = 1, 1, 0))                as wheelchairOrderCount,
               sum(if(service_type = 2, 1, 0))                as sickbedOrderCount,
               sum(if(service_type = 3, 1, 0))                as guideOrderCount,
               sum(if(service_type = 4, exam_items_count, 0)) as surgeryOrderCount,
               sum(if(service_type = 1, exam_items_count, 0)) as wheelchairItemCount,
               sum(if(service_type = 2, exam_items_count, 0)) as sickbedItemCount,
               sum(if(service_type = 3, exam_items_count, 0)) as guideItemCount,
               sum(if(service_type = 4, exam_items_count, 0)) as surgeryItemCount,
               sum(if(service_type != 4, 1, 0))               as totalOrderCount,
               sum(exam_items_count)                          as totalItemCount
        from psc_order
        where confirm_time between #{minConfirmTime} and #{maxConfirmTime}
          and carer_employee_no = #{carerEmployeeNo}
          and status = 99
    </select>
</mapper>
