<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.WikiCategoryMapper">

    <resultMap type="WikiCategory" id="WikiCategoryResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="icon" column="icon"/>
        <result property="sortNo" column="sort_no"/>
    </resultMap>

    <sql id="selectWikiCategoryVo">
        select id, name, icon, sort_no
        from ph_wiki_category
    </sql>

    <select id="selectWikiCategoryList" parameterType="WikiCategory" resultMap="WikiCategoryResult">
        <include refid="selectWikiCategoryVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="icon != null  and icon != ''">and icon = #{icon}</if>
            <if test="sortNo != null ">and sort_no = #{sortNo}</if>
        </where>
        order by sort_no desc, id desc
    </select>

    <select id="selectAll" resultMap="WikiCategoryResult">
        <include refid="selectWikiCategoryVo"/>
        order by sort_no desc, id desc
    </select>

    <select id="selectWikiCategoryById" parameterType="Long" resultMap="WikiCategoryResult">
        <include refid="selectWikiCategoryVo"/>
        where id = #{id}
    </select>

    <insert id="insertWikiCategory" parameterType="WikiCategory" useGeneratedKeys="true" keyProperty="id">
        insert into ph_wiki_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="icon != null">icon,</if>
            <if test="sortNo != null">sort_no,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="icon != null">#{icon},</if>
            <if test="sortNo != null">#{sortNo},</if>
        </trim>
    </insert>

    <update id="updateWikiCategory" parameterType="WikiCategory">
        update ph_wiki_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="sortNo != null">sort_no = #{sortNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWikiCategoryById" parameterType="Long">
        delete
        from ph_wiki_category
        where id = #{id}
    </delete>

    <delete id="deleteWikiCategoryByIds" parameterType="String">
        delete from ph_wiki_category where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>