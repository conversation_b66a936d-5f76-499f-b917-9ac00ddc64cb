<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="space.lzhq.ph.mapper.AlipayUserInfoMapper">

    <resultMap type="AlipayUserInfo" id="AlipayUserInfoResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="isCertified" column="is_certified"/>
        <result property="certType" column="cert_type"/>
        <result property="certNo" column="cert_no"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="avatar" column="avatar"/>
        <result property="email" column="email"/>
        <result property="mobile" column="mobile"/>
        <result property="gender" column="gender"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="area" column="area"/>
        <result property="address" column="address"/>
        <result property="status" column="status"/>
    </resultMap>

    <sql id="selectAlipayUserInfoVo">
        select id,
               type,
               is_certified,
               cert_type,
               cert_no,
               user_name,
               nick_name,
               avatar,
               email,
               mobile,
               gender,
               province,
               city,
               area,
               address,
               status
        from alipay_user_info
    </sql>

    <select id="selectAlipayUserInfoList" parameterType="AlipayUserInfo" resultMap="AlipayUserInfoResult">
        <include refid="selectAlipayUserInfoVo"/>
        <where>
            <if test="id != null  and id != ''">and id = #{id}</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="isCertified != null  and isCertified != ''">and is_certified = #{isCertified}</if>
            <if test="certType != null  and certType != ''">and cert_type = #{certType}</if>
            <if test="certNo != null  and certNo != ''">and cert_no = #{certNo}</if>
            <if test="userName != null  and userName != ''">and user_name like concat('%', #{userName}, '%')</if>
            <if test="email != null  and email != ''">and email = #{email}</if>
            <if test="mobile != null  and mobile != ''">and mobile = #{mobile}</if>
            <if test="gender != null  and gender != ''">and gender = #{gender}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
        </where>
    </select>

    <select id="selectAlipayUserInfoById" parameterType="String" resultMap="AlipayUserInfoResult">
        <include refid="selectAlipayUserInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertAlipayUserInfo" parameterType="AlipayUserInfo">
        insert into alipay_user_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="isCertified != null and isCertified != ''">is_certified,</if>
            <if test="certType != null and certType != ''">cert_type,</if>
            <if test="certNo != null and certNo != ''">cert_no,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="email != null and email != ''">email,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="gender != null and gender != ''">gender,</if>
            <if test="province != null and province != ''">province,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="area != null and area != ''">area,</if>
            <if test="address != null and address != ''">address,</if>
            <if test="status != null and status != ''">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="isCertified != null and isCertified != ''">#{isCertified},</if>
            <if test="certType != null and certType != ''">#{certType},</if>
            <if test="certNo != null and certNo != ''">#{certNo},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="email != null and email != ''">#{email},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="gender != null and gender != ''">#{gender},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="area != null and area != ''">#{area},</if>
            <if test="address != null and address != ''">#{address},</if>
            <if test="status != null and status != ''">#{status},</if>
        </trim>
    </insert>

    <update id="updateAlipayUserInfo" parameterType="AlipayUserInfo">
        update alipay_user_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="isCertified != null and isCertified != ''">is_certified = #{isCertified},</if>
            <if test="certType != null and certType != ''">cert_type = #{certType},</if>
            <if test="certNo != null and certNo != ''">cert_no = #{certNo},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="avatar != null and avatar != ''">avatar = #{avatar},</if>
            <if test="email != null and email != ''">email = #{email},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="gender != null and gender != ''">gender = #{gender},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="area != null and area != ''">area = #{area},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="status != null and status != ''">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAlipayUserInfoById" parameterType="String">
        delete
        from alipay_user_info
        where id = #{id}
    </delete>

    <delete id="deleteAlipayUserInfoByIds" parameterType="String">
        delete from alipay_user_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
