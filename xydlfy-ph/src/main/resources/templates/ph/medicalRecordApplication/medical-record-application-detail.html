<!DOCTYPE html>
<html
        lang="zh"
        xmlns:th="http://www.thymeleaf.org"
>
<head>
    <th:block th:include="include :: header('病历复印申请详情')"/>
    <title>病历复印申请详情</title>
    <style>
        .container-div .row {
          height: auto;
        }
        .detail-card {
          background: #fff;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .detail-title {
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 20px;
          border-bottom: 2px solid #007bff;
          padding-bottom: 10px;
        }
        .info-row {
          margin-bottom: 15px;
        }
        .info-label {
          font-weight: bold;
          color: #666;
          width: 140px;
          display: inline-block;
        }
        .info-value {
          color: #333;
        }

        /* 状态样式 */
        .status-badge {
          padding: 8px 16px;
          border-radius: 20px;
          font-size: 14px;
          font-weight: bold;
          display: inline-block;
        }
        .status-draft {
          background-color: #ffc107;
          color: #856404;
        }
        .status-submitted {
          background-color: #17a2b8;
          color: #fff;
        }
        .status-approved {
          background-color: #28a745;
          color: #fff;
        }
        .status-rejected {
          background-color: #dc3545;
          color: #fff;
        }
        .status-paid {
          background-color: #6f42c1;
          color: #fff;
        }
        .status-delivered {
          background-color: #20c997;
          color: #fff;
        }

        /* 费用信息样式 */
        .fee-card {
          background: #fff;
          border: 2px solid #e3f2fd;
          border-left: 4px solid #2196f3;
        }
        .fee-card .detail-title {
          color: #1976d2;
          border-bottom-color: #e3f2fd;
        }

        /* 费用表格样式 */
        .fee-table {
          background: #f8fffe;
          border: 1px solid #e3f2fd;
          border-radius: 6px;
          overflow: hidden;
        }
        .fee-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          border-bottom: 1px solid #e3f2fd;
          transition: background-color 0.2s ease;
        }
        .fee-row:last-child {
          border-bottom: none;
        }
        .fee-row:hover {
          background: #e8f5e8;
        }
        .fee-label {
          font-weight: 500;
          color: #666;
          font-size: 14px;
        }
        .fee-amount {
          font-size: 16px;
          font-weight: bold;
          color: #1976d2;
        }
        .total-fee-row {
          background: #e3f2fd;
          border-top: 2px solid #2196f3;
        }
        .total-fee-row:hover {
          background: #d1ecf1;
        }
        .total-amount {
          font-size: 18px;
          color: #1976d2;
        }

        /* 支付信息样式 */
        .payment-info {
          margin-top: 12px;
          padding-top: 12px;
          border-top: 1px solid #e3f2fd;
        }
        .payment-time {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 13px;
          color: #666;
          margin-bottom: 8px;
        }
        .payment-time i {
          color: #28a745;
        }

        /* 订单号信息样式 */
        .order-numbers {
          background: #f8f9fa;
          border-radius: 4px;
          padding: 8px 10px;
        }
        .order-row {
          display: flex;
          align-items: center;
          font-size: 12px;
          margin-bottom: 4px;
        }
        .order-row:last-child {
          margin-bottom: 0;
        }
        .order-label {
          color: #666;
          min-width: 80px;
          font-weight: 500;
        }
        .order-value {
          color: #333;
          font-family: 'Courier New', monospace;
          font-size: 11px;
          background: #fff;
          padding: 2px 6px;
          border-radius: 3px;
          border: 1px solid #dee2e6;
          word-break: break-all;
        }

        /* 快递信息样式 */
        .shipping-info {
          background: #f8f9fa;
          border-left: 4px solid #28a745;
          padding: 15px;
          border-radius: 0 8px 8px 0;
          margin-top: 15px;
        }
        .shipping-tracking {
          font-size: 16px;
          font-weight: bold;
          color: #28a745;
          margin-bottom: 8px;
        }

        /* 附件样式 */
        .attachment-item {
          background: #f8f9fa;
          border: 1px solid #dee2e6;
          border-radius: 5px;
          padding: 10px;
          margin: 5px 0;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        .attachment-info {
          flex: 1;
          min-width: 0;
        }
        .attachment-info > div {
          word-break: break-all;
          overflow-wrap: break-word;
          line-height: 1.4;
        }
        .attachment-actions {
          margin-left: 10px;
          flex-shrink: 0;
        }

        /* 时间线样式 */
        .timeline {
          position: relative;
          padding-left: 35px;
          margin-top: 10px;
        }
        .timeline::before {
          content: "";
          position: absolute;
          left: 18px;
          top: 0;
          height: calc(100% - 10px);
          width: 3px;
          background: linear-gradient(to bottom, #007bff, #28a745);
          border-radius: 2px;
        }
        .timeline-item {
          position: relative;
          margin-bottom: 25px;
          opacity: 0.7;
          transition: all 0.3s ease;
        }
        .timeline-item.active {
          opacity: 1;
        }
        .timeline-item.completed {
          opacity: 1;
        }
        .timeline-item:hover {
          opacity: 1;
        }
        .timeline-item::before {
          content: "";
          position: absolute;
          left: -22px;
          top: 20px;
          width: 14px;
          height: 14px;
          border-radius: 50%;
          background: #e9ecef;
          border: 3px solid #fff;
          box-shadow: 0 0 0 2px #e9ecef;
          z-index: 2;
          transition: all 0.3s ease;
        }
        .timeline-item:hover::before {
          width: 18px;
          height: 18px;
          left: -24px;
          top: 18px;
          box-shadow: 0 0 0 3px #e9ecef, 0 0 8px rgba(0, 0, 0, 0.1);
        }
        .timeline-item.active::before {
          background: #007bff;
          box-shadow: 0 0 0 2px #007bff, 0 0 10px rgba(0, 123, 255, 0.3);
          animation: pulse 1.5s infinite;
        }
        .timeline-item.completed::before {
          background: #28a745;
          box-shadow: 0 0 0 2px #28a745;
        }
        .timeline-item.rejected::before {
          background: #dc3545;
          box-shadow: 0 0 0 2px #dc3545;
        }
        @keyframes pulse {
          0% {
            box-shadow: 0 0 0 2px #007bff, 0 0 0 rgba(0, 123, 255, 0.8);
          }
          50% {
            box-shadow: 0 0 0 2px #007bff, 0 0 25px rgba(0, 123, 255, 0.6);
          }
          100% {
            box-shadow: 0 0 0 2px #007bff, 0 0 0 rgba(0, 123, 255, 0.8);
          }
        }
        .timeline-content {
          background: #fff;
          border: 1px solid #e9ecef;
          border-radius: 8px;
          padding: 16px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;
        }
        .timeline-item:hover .timeline-content {
          border-color: #007bff;
          box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        }
        .timeline-content strong {
          color: #333;
          font-size: 15px;
          margin-bottom: 8px;
          display: block;
        }
        .timeline-time {
          color: #666;
          font-size: 13px;
          margin: 6px 0;
          font-weight: 500;
        }
        .timeline-content small {
          color: #888;
          font-size: 12px;
          line-height: 1.4;
        }
        .timeline-item.completed .timeline-content strong {
          color: #28a745;
        }
        .timeline-item.rejected .timeline-content strong {
          color: #dc3545;
        }
        .timeline-item.active .timeline-content strong {
          color: #007bff;
        }
        .empty-state {
          text-align: center;
          color: #999;
          padding: 40px;
          font-style: italic;
        }

        /* 时间线开关样式 */
        .switch-container {
          display: inline-flex;
          align-items: center;
          gap: 8px;
          font-size: 13px;
          color: #666;
          cursor: pointer;
        }
        .switch-container input {
          position: relative;
          width: 40px;
          height: 20px;
          appearance: none;
          background: #ccc;
          border-radius: 20px;
          outline: none;
          transition: all 0.3s;
        }
        .switch-container input:checked {
          background: #007bff;
        }
        .switch-container input::before {
          content: "";
          position: absolute;
          top: 2px;
          left: 2px;
          width: 16px;
          height: 16px;
          background: white;
          border-radius: 50%;
          transition: all 0.3s;
        }
        .switch-container input:checked::before {
          transform: translateX(20px);
        }

        /* 审批操作样式 */
        .approval-options {
          display: flex;
          gap: 20px;
          margin-bottom: 20px;
        }
        .approval-option {
          flex: 1;
          text-align: center;
          padding: 20px;
          border: 2px solid #dee2e6;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s;
          background: #fff;
          font-family: inherit;
          font-size: inherit;
          line-height: inherit;
          outline: none;
          width: 100%;
          display: block;
        }
        .approval-option:hover,
        .approval-option:focus {
          border-color: #007bff;
          box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
        }
        .approval-option.selected {
          border-color: #28a745;
          background-color: #d4edda;
        }
        .approval-option.reject.selected {
          border-color: #dc3545;
          background-color: #f8d7da;
        }
        .approval-option i {
          font-size: 36px;
          margin-bottom: 10px;
          display: block;
        }
        .approval-option.approve i {
          color: #28a745;
        }
        .approval-option.reject i {
          color: #dc3545;
        }
        .approval-details {
          display: none;
          margin-top: 20px;
          padding: 20px;
          background: #fff;
          border-radius: 5px;
          border: 1px solid #dee2e6;
        }
        .required-field {
          border-left: 4px solid #dc3545;
          padding-left: 15px;
        }
        .char-count {
          font-size: 12px;
          color: #666;
          float: right;
          margin-top: 5px;
        }

        /* 审批面板样式 */
        .approval-panel {
          display: none;
          opacity: 0;
          transform: translateY(-10px);
          transition: all 0.3s ease;
        }
        .approval-panel.show {
          display: block;
          opacity: 1;
          transform: translateY(0);
        }
        .approval-operation-panel {
          border-left: 4px solid #007bff;
          position: relative;
        }
        .approval-tips-panel {
          border-left: 4px solid #17a2b8;
          background-color: #f8f9fa;
          margin-top: 15px;
        }

        /* 邮寄操作样式 */
        .shipping-panel {
          display: none;
          opacity: 0;
          transform: translateY(-10px);
          transition: all 0.3s ease;
        }
        .shipping-panel.show {
          display: block;
          opacity: 1;
          transform: translateY(0);
        }
        .shipping-operation-panel {
          border-left: 4px solid #28a745;
          position: relative;
        }

        /* 费用输入样式 */
        .fee-input-group {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 15px;
        }
        .fee-input-group label {
          min-width: 80px;
          font-weight: bold;
          color: #333;
        }
        .fee-input-group input {
          flex: 1;
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
        }
        .fee-input-group .currency {
          color: #666;
          font-weight: normal;
        }
        .total-fee-display {
          background: #f8f9fa;
          border: 2px solid #28a745;
          border-radius: 8px;
          padding: 15px;
          text-align: center;
          margin-top: 15px;
        }
        .total-fee-display .amount {
          font-size: 24px;
          font-weight: bold;
          color: #28a745;
        }

        /* Grid布局 */
        @media (min-width: 768px) {
          .container-div > .row {
            display: grid;
            grid-template-columns: 2fr 1fr;
            grid-template-rows: auto auto;
            gap: 20px;
            align-items: start;
          }
          .top-left-section {
            grid-column: 1;
            grid-row: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
          }
          .top-right-section {
            grid-column: 2;
            grid-row: 1;
            display: flex;
            flex-direction: column;
            height: fit-content;
          }
          .bottom-left-section {
            grid-column: 1;
            grid-row: 2;
            display: flex;
            flex-direction: column;
            gap: 20px;
          }
          .bottom-right-section {
            grid-column: 2;
            grid-row: 2;
            display: flex;
            flex-direction: column;
            gap: 20px;
          }
          .detail-card.timeline-progress-card {
            overflow-y: auto;
          }
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
          .container-div > .row {
            display: block;
          }
          .top-left-section,
          .top-right-section,
          .bottom-left-section,
          .bottom-right-section {
            display: block;
            width: 100%;
            margin-bottom: 20px;
          }
          .detail-card {
            padding: 15px;
            margin-bottom: 15px;
          }
          .info-label {
            width: 100px;
            font-size: 13px;
          }
          .info-value {
            font-size: 13px;
            word-break: break-all;
            overflow-wrap: break-word;
          }
          .approval-options {
            flex-direction: column;
            gap: 15px;
          }
          .approval-option {
            padding: 15px;
          }
          .approval-option i {
            font-size: 28px;
          }
          .fee-input-group {
            flex-direction: column;
            align-items: stretch;
            gap: 5px;
          }
          .fee-input-group label {
            min-width: auto;
          }
        }

        /* 文件预览模态框样式 */
        #filePreviewModal .modal-dialog {
          margin: 30px auto;
        }
        #filePreviewModal .modal-content {
          border-radius: 8px;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        #filePreviewModal .modal-header {
          background-color: #f8f9fa;
          border-bottom: 1px solid #dee2e6;
          border-radius: 8px 8px 0 0;
        }
        #filePreviewModal .modal-title {
          display: inline-block;
          font-weight: 600;
          color: #333;
        }
        #filePreviewModal .modal-body {
          background-color: #fff;
        }
        #filePreviewModal .modal-footer {
          background-color: #f8f9fa;
          border-top: 1px solid #dee2e6;
          border-radius: 0 0 8px 8px;
        }
        #previewContainer img {
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        #previewContainer iframe {
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .fa-spinner {
          animation: spin 1s linear infinite;
        }
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }

        /* 滚动条样式 */
        .detail-card.timeline-progress-card::-webkit-scrollbar {
          width: 8px;
        }
        .detail-card.timeline-progress-card::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }
        .detail-card.timeline-progress-card::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 4px;
        }
        .detail-card.timeline-progress-card::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }
    </style>
</head>
<body class="gray-bg">
<div
        class="container-div"
        th:classappend="${detail.agentFlag == 1 ? 'has-agent' : 'no-agent'}"
>
    <div class="row">
        <!-- 第一行左侧：患者信息 + 代办人信息 -->
        <div class="top-left-section">
            <!-- 患者基本信息 -->
            <div class="detail-card patient-card">
                <div class="detail-title"><i class="fa fa-user"></i> 患者信息</div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-row">
                            <span class="info-label">姓名：</span>
                            <span class="info-value" th:text="${detail.patientName}">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">身份证号：</span>
                            <span class="info-value" th:text="${detail.patientIdCardNo}">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">手机号：</span>
                            <span class="info-value" th:text="${detail.patientPhone}">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">住院号：</span>
                            <span class="info-value" th:text="${detail.admissionId}">-</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-row">
                            <span class="info-label">性别：</span>
                            <span class="info-value" th:text="${detail.patientGender}">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">出生日期：</span>
                            <span
                                    class="info-value"
                                    th:text="${detail.patientBirthDate != null ? #temporals.format(detail.patientBirthDate, 'yyyy-MM-dd') : '-'}"
                            >-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">病区：</span>
                            <span class="info-value" th:text="${detail.ward}">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">复印用途：</span>
                            <span class="info-value" th:text="${detail.copyPurpose?.description}">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">复印份数：</span>
                            <span class="info-value" th:text="${detail.copyCount}">-</span>
                        </div>
                    </div>
                </div>

                <!-- 住院时间信息 -->
                <div class="row" style="margin-top: 20px">
                    <div class="col-md-6">
                        <div class="info-row">
                            <span class="info-label">入院时间：</span>
                            <span
                                    class="info-value"
                                    th:text="${detail.admissionTime != null ? #temporals.format(detail.admissionTime, 'yyyy-MM-dd HH:mm') : '-'}"
                            >-</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-row">
                            <span class="info-label">出院时间：</span>
                            <span
                                    class="info-value"
                                    th:text="${detail.dischargeTime != null ? #temporals.format(detail.dischargeTime, 'yyyy-MM-dd HH:mm') : '-'}"
                            >-</span>
                        </div>
                    </div>
                </div>

                <!-- 患者附件 -->
                <div class="row" style="margin-top: 20px">
                    <div class="col-md-6" th:if="${detail.patientIdCardAttachment}">
                        <h6><strong>身份证附件</strong></h6>
                        <div class="attachment-item">
                            <div class="attachment-info">
                                <div th:text="${detail.patientIdCardAttachment?.fileName ?: '-'}">-</div>
                                <small
                                        class="text-muted"
                                        th:text="${detail.patientIdCardAttachment?.createTime ?: '-'}"
                                >-</small>
                            </div>
                            <div class="attachment-actions">
                                <button
                                        class="btn btn-primary btn-xs"
                                        onclick="downloadFile(this)"
                                        th:data-url="${detail.patientIdCardAttachment?.fileUrl ?: ''}"
                                        th:data-filename="${detail.patientIdCardAttachment?.fileName ?: ''}"
                                        title="下载"
                                >
                                    <i class="fa fa-download"></i>
                                </button>
                                <button
                                        class="btn btn-success btn-xs"
                                        onclick="previewFile(this)"
                                        th:data-url="${detail.patientIdCardAttachment?.fileUrl ?: ''}"
                                        th:data-filename="${detail.patientIdCardAttachment?.fileName ?: ''}"
                                        title="预览"
                                >
                                    <i class="fa fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6" th:if="${detail.patientBirthCertificateAttachment}">
                        <h6><strong>出生证附件</strong></h6>
                        <div class="attachment-item">
                            <div class="attachment-info">
                                <div th:text="${detail.patientBirthCertificateAttachment?.fileName ?: '-'}">-</div>
                                <small
                                        class="text-muted"
                                        th:text="${detail.patientBirthCertificateAttachment?.createTime ?: '-'}"
                                >-</small>
                            </div>
                            <div class="attachment-actions">
                                <button
                                        class="btn btn-primary btn-xs"
                                        onclick="downloadFile(this)"
                                        th:data-url="${detail.patientBirthCertificateAttachment?.fileUrl ?: ''}"
                                        th:data-filename="${detail.patientBirthCertificateAttachment?.fileName ?: ''}"
                                        title="下载"
                                >
                                    <i class="fa fa-download"></i>
                                </button>
                                <button
                                        class="btn btn-success btn-xs"
                                        onclick="previewFile(this)"
                                        th:data-url="${detail.patientBirthCertificateAttachment?.fileUrl ?: ''}"
                                        th:data-filename="${detail.patientBirthCertificateAttachment?.fileName ?: ''}"
                                        title="预览"
                                >
                                    <i class="fa fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 代办人信息 -->
            <div class="detail-card agent-card" th:if="${detail.agentFlag == 1}">
                <div class="detail-title">
                    <i class="fa fa-users"></i> 代办人信息
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-row">
                            <span class="info-label">代办人姓名：</span>
                            <span class="info-value" th:text="${detail.agentName}">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">身份证号：</span>
                            <span class="info-value" th:text="${detail.agentIdCardNo}">-</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-row">
                            <span class="info-label">手机号：</span>
                            <span class="info-value" th:text="${detail.agentMobile}">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">与患者关系：</span>
                            <span class="info-value" th:text="${detail.agentRelation?.description ?: '-'}">-</span>
                        </div>
                    </div>
                </div>

                <!-- 代办人附件 -->
                <div class="row" style="margin-top: 20px" th:if="${detail.agentIdCardAttachment != null}">
                    <div class="col-md-6">
                        <h6><strong>身份证附件</strong></h6>
                        <div class="attachment-item">
                            <div class="attachment-info">
                                <div th:text="${detail.agentIdCardAttachment?.fileName ?: '-'}">-</div>
                                <small
                                        class="text-muted"
                                        th:text="${detail.agentIdCardAttachment?.createTime ?: '-'}"
                                >-</small>
                            </div>
                            <div class="attachment-actions">
                                <button
                                        class="btn btn-primary btn-xs"
                                        onclick="downloadFile(this)"
                                        th:data-url="${detail.agentIdCardAttachment?.fileUrl ?: ''}"
                                        th:data-filename="${detail.agentIdCardAttachment?.fileName ?: ''}"
                                        title="下载"
                                >
                                    <i class="fa fa-download"></i>
                                </button>
                                <button
                                        class="btn btn-success btn-xs"
                                        onclick="previewFile(this)"
                                        th:data-url="${detail.agentIdCardAttachment?.fileUrl ?: ''}"
                                        th:data-filename="${detail.agentIdCardAttachment?.fileName ?: ''}"
                                        title="预览"
                                >
                                    <i class="fa fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 收件人信息 -->
            <div class="detail-card recipient-card">
                <div class="detail-title">
                    <i class="fa fa-truck"></i> 收件人信息
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-row">
                            <span class="info-label">收件人姓名：</span>
                            <span class="info-value" th:text="${detail.recipientName}">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">收件人手机：</span>
                            <span class="info-value" th:text="${detail.recipientMobile}">-</span>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="info-row">
                            <span class="info-label">收件地址：</span>
                            <span class="info-value" th:text="${detail.recipientAddress}">-</span>
                        </div>
                    </div>
                </div>

                <!-- 快递信息 -->
                <div class="shipping-info" th:if="${detail.trackingNumber}">
                    <div class="shipping-tracking">
                        <i class="fa fa-truck"></i> 快递单号：<span th:text="${detail.trackingNumber}">-</span>
                    </div>
                    <div class="info-row" th:if="${detail.shippingTime}">
                        <span class="info-label">邮寄时间：</span>
                        <span
                                class="info-value"
                                th:text="${detail.shippingTime != null ? #temporals.format(detail.shippingTime, 'yyyy-MM-dd HH:mm:ss') : '-'}"
                        >-</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第一行右侧：申报进度 -->
        <div class="top-right-section">
            <div class="detail-card timeline-progress-card">
                <div class="detail-title">
                    <i class="fa fa-tasks"></i> 申报进度
                    <div class="pull-right">
                        <label class="switch-container">
                            <input type="checkbox" id="timelineDetailSwitch"/>
                            <span class="slider"></span>
                            <span class="switch-label">详细模式</span>
                        </label>
                    </div>
                </div>
                <div class="timeline" id="timelineContainer">
                    <!-- 时间线内容将通过AJAX动态加载 -->
                    <div class="timeline-loading text-center">
                        <i class="fa fa-spinner fa-spin"></i> 加载时间线中...
                    </div>
                </div>
            </div>
        </div>

        <!-- 第二行左侧：费用信息 + 申报状态 -->
        <div class="bottom-left-section">
            <!-- 费用信息 -->
            <div class="detail-card fee-card" th:if="${detail.copyFee != null}">
                <div class="detail-title">
                    <i class="fa fa-calculator"></i> 费用信息
                </div>

                <!-- 费用明细表格 -->
                <div class="fee-table">
                    <div class="fee-row">
                        <span class="fee-label">复印费用</span>
                        <span class="fee-amount">¥<span th:text="${detail.copyFee}">-</span></span>
                    </div>
                    <div class="fee-row">
                        <span class="fee-label">快递费用</span>
                        <span class="fee-amount">¥<span th:text="${detail.shippingFee}">-</span></span>
                    </div>
                    <div class="fee-row total-fee-row">
                        <span class="fee-label">总费用</span>
                        <span class="fee-amount total-amount">¥<span th:text="${detail.totalFee}">-</span></span>
                    </div>
                </div>

                <!-- 支付信息 -->
                <div class="payment-info" th:if="${detail.payTime}">
                    <div class="payment-time">
                        <i class="fa fa-clock-o"></i>
                        <span>支付时间：</span>
                        <span th:text="${detail.payTime != null ? #temporals.format(detail.payTime, 'yyyy-MM-dd HH:mm:ss') : '-'}">2025-07-28 10:27:05</span>
                    </div>

                    <!-- 订单号信息 -->
                    <div class="order-numbers" th:if="${detail.zyPayNo != null or detail.wxPayNo != null}">
                        <div class="order-row" th:if="${detail.zyPayNo != null}">
                            <span class="order-label">商户订单号：</span>
                            <span class="order-value" th:text="${detail.zyPayNo}">-</span>
                        </div>
                        <div class="order-row" th:if="${detail.wxPayNo != null}">
                            <span class="order-label">微信订单号：</span>
                            <span class="order-value" th:text="${detail.wxPayNo}">-</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 申报状态信息 -->
            <div class="detail-card status-card">
                <div class="detail-title">
                    <i class="fa fa-info-circle"></i> 申报状态
                </div>
                <div class="info-row">
                    <span class="info-label">当前状态：</span>
                    <span
                            class="status-badge"
                            th:classappend="${'status-' + #strings.toLowerCase(detail.status?.name())}"
                            th:text="${detail.status?.description}"
                    >-</span>
                </div>

                <div class="info-row" th:if="${detail.submitTime}">
                    <span class="info-label">提交时间：</span>
                    <span
                            class="info-value"
                            th:text="${detail.submitTime != null ? #temporals.format(detail.submitTime, 'yyyy-MM-dd HH:mm:ss') : '-'}"
                    >-</span>
                </div>

                <div class="info-row" th:if="${detail.approveTime}">
                    <span class="info-label">审批时间：</span>
                    <span
                            class="info-value"
                            th:text="${detail.approveTime != null ? #temporals.format(detail.approveTime, 'yyyy-MM-dd HH:mm:ss') : '-'}"
                    >-</span>
                </div>

                <!-- 驳回信息 -->
                <div th:if="${detail.status?.name() == 'REJECTED'}" class="alert alert-warning"
                     style="margin-top: 15px">
                    <h6><i class="fa fa-exclamation-triangle"></i> 驳回信息</h6>
                    <div class="info-row" th:if="${detail.rejectReason}">
                        <span class="info-label">驳回原因：</span>
                        <span class="info-value" th:text="${detail.rejectReason}">-</span>
                    </div>
                    <div class="info-row" th:if="${detail.correctionAdvice}">
                        <span class="info-label">整改意见：</span>
                        <span class="info-value" th:text="${detail.correctionAdvice}">-</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第二行右侧：操作面板 -->
        <div class="bottom-right-section">
            <!-- 操作按钮 -->
            <div class="detail-card operations-card">
                <div class="detail-title"><i class="fa fa-cogs"></i> 操作</div>
                <div class="btn-group-vertical" style="width: 100%">
                    <!-- 审核按钮 -->
                    <button
                            type="button"
                            class="btn btn-primary"
                            id="toggleApproval"
                            th:if="${detail.canApprove}"
                            shiro:hasPermission="ph:medical-record-application:approve"
                    >
                        <i class="fa fa-gavel"></i> 审核
                    </button>

                    <!-- 邮寄按钮 -->
                    <button
                            type="button"
                            class="btn btn-info"
                            id="toggleShipping"
                            th:if="${detail.canShip}"
                            shiro:hasPermission="ph:medical-record-application:ship"
                    >
                        <i class="fa fa-truck"></i> 邮寄
                    </button>

                    <!-- 删除按钮 -->
                    <button
                            type="button"
                            class="btn btn-danger"
                            onclick="deleteRecord()"
                            th:if="${detail.canDelete}"
                            shiro:hasPermission="ph:medical-record-application:remove"
                    >
                        <i class="fa fa-trash"></i> 删除
                    </button>
                </div>
            </div>

            <!-- 审核操作面板 -->
            <div class="detail-card approval-panel approval-operation-panel" id="approvalPanel">
                <div class="detail-title">
                    <i class="fa fa-gavel"></i> 审核操作
                    <button
                            type="button"
                            class="btn btn-sm btn-secondary pull-right"
                            id="cancelApproval"
                    >
                        <i class="fa fa-times"></i> 取消
                    </button>
                </div>

                <form id="approvalForm">
                    <input type="hidden" id="applicationId" th:value="${detail?.id ?: 0}"/>

                    <!-- 审核选项 -->
                    <div class="approval-options">
                        <button type="button" class="approval-option approve" data-status="APPROVED">
                            <i class="fa fa-check-circle"></i>
                            <strong>审核通过</strong>
                            <p>申报材料齐全，符合要求</p>
                        </button>
                        <button type="button" class="approval-option reject" data-status="REJECTED">
                            <i class="fa fa-times-circle"></i>
                            <strong>审核驳回</strong>
                            <p>申报材料不符合要求</p>
                        </button>
                    </div>

                    <!-- 审核通过详情（费用设置） -->
                    <div id="approveDetails" class="approval-details">
                        <div class="alert alert-info">
                            <h6><i class="fa fa-info-circle"></i> 费用设置</h6>
                            <p>审核通过后需要设置复印费用和快递费用，系统将自动计算总费用。</p>
                        </div>

                        <div class="fee-input-group">
                            <label for="copyFee">复印费用：</label>
                            <input type="number" id="copyFee" step="0.01" min="0" max="9999.99" placeholder="0.00"/>
                            <span class="currency">元</span>
                        </div>

                        <div class="fee-input-group">
                            <label for="shippingFee">快递费用：</label>
                            <input type="number" id="shippingFee" step="0.01" min="0" max="999.99" placeholder="0.00"/>
                            <span class="currency">元</span>
                        </div>

                        <div class="total-fee-display" id="totalFeeDisplay" style="display: none;">
                            <div>总费用：<span class="amount" id="totalFeeAmount">¥0.00</span></div>
                        </div>
                    </div>

                    <!-- 驳回详情 -->
                    <div id="rejectDetails" class="approval-details">
                        <div class="required-field">
                            <h6>
                                <strong>驳回原因</strong>
                                <span class="text-danger">*</span>
                            </h6>
                            <textarea
                                    id="rejectReason"
                                    class="form-control"
                                    rows="4"
                                    placeholder="请详细说明驳回原因，帮助申请人了解问题所在"
                                    maxlength="500"
                                    required
                            ></textarea>
                            <div class="char-count">
                                <span id="rejectReasonCount">0</span>/500
                            </div>
                        </div>

                        <div style="margin-top: 20px">
                            <h6>
                                <strong>整改意见</strong>
                                <span class="text-muted">(可选)</span>
                            </h6>
                            <textarea
                                    id="correctionAdvice"
                                    class="form-control"
                                    rows="4"
                                    placeholder="请提供具体的整改建议，指导申请人如何修正申报材料"
                                    maxlength="1000"
                            ></textarea>
                            <div class="char-count">
                                <span id="correctionAdviceCount">0</span>/1000
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="text-center" style="margin-top: 20px">
                        <button type="button" id="submitApproval" class="btn btn-primary" disabled>
                            <i class="fa fa-gavel"></i> 提交审核
                        </button>
                    </div>
                </form>
            </div>

            <!-- 邮寄操作面板 -->
            <div class="detail-card shipping-panel shipping-operation-panel" id="shippingPanel">
                <div class="detail-title">
                    <i class="fa fa-truck"></i> 邮寄操作
                    <button
                            type="button"
                            class="btn btn-sm btn-secondary pull-right"
                            id="cancelShipping"
                    >
                        <i class="fa fa-times"></i> 取消
                    </button>
                </div>

                <form id="shippingForm">
                    <div class="alert alert-info">
                        <h6><i class="fa fa-info-circle"></i> 邮寄信息</h6>
                        <p>请填写快递单号，确认邮寄信息。邮寄完成后申请状态将变更为"邮寄完成"。</p>
                    </div>

                    <div class="form-group">
                        <label for="trackingNumber">
                            <strong>快递单号</strong>
                            <span class="text-danger">*</span>
                        </label>
                        <input
                                type="text"
                                id="trackingNumber"
                                class="form-control"
                                placeholder="请输入快递单号"
                                minlength="8"
                                maxlength="50"
                                required
                        />
                    </div>

                    <div class="form-group">
                        <label for="shippingNote">
                            <strong>邮寄备注</strong>
                            <span class="text-muted">(可选)</span>
                        </label>
                        <textarea
                                id="shippingNote"
                                class="form-control"
                                rows="3"
                                placeholder="邮寄备注信息"
                                maxlength="200"
                        ></textarea>
                        <div class="char-count">
                            <span id="shippingNoteCount">0</span>/200
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="text-center" style="margin-top: 20px">
                        <button type="button" id="submitShipping" class="btn btn-success">
                            <i class="fa fa-truck"></i> 确认邮寄
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 文件预览模态框 -->
<div
        class="modal fade"
        id="filePreviewModal"
        tabindex="-1"
        aria-labelledby="filePreviewModalLabel"
        aria-hidden="true"
>
    <div class="modal-dialog modal-lg" style="max-width: 90%; width: 90%">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="filePreviewModalLabel">文件预览</h5>
                <button
                        type="button"
                        class="close"
                        data-dismiss="modal"
                        aria-label="关闭"
                >
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div
                    class="modal-body"
                    id="filePreviewBody"
                    style="padding: 0; max-height: 70vh; overflow: auto"
            >
                <div id="previewContainer" style="text-align: center">
                    <!-- 预览内容将在这里动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    关闭
                </button>
                <button type="button" class="btn btn-primary" id="downloadFromPreview">
                    下载文件
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 预定义模板 -->
<script type="text/template" id="office-hint-template">
    <div class="alert alert-info text-center" style="margin: 50px auto;">
        <i class="fa fa-file-text-o fa-3x" style="display: block; margin-bottom: 15px; color: #007bff;"></i>
        <h5>Office文档预览</h5>
        <p>该文件类型需要下载后使用相应软件打开查看</p>
        <button type="button" class="btn btn-primary download-btn">
            <i class="fa fa-download"></i> 立即下载
        </button>
    </div>
</script>

<script type="text/template" id="unsupported-hint-template">
    <div class="alert alert-warning text-center" style="margin: 50px auto;">
        <i class="fa fa-file-o fa-3x" style="display: block; margin-bottom: 15px; color: #f39c12;"></i>
        <h5>暂不支持预览</h5>
        <p>该文件类型暂不支持在线预览，请下载后查看</p>
        <button type="button" class="btn btn-primary download-btn">
            <i class="fa fa-download"></i> 立即下载
        </button>
    </div>
</script>

<script type="text/template" id="timeline-item-template">
    <div class="timeline-item" data-event-type="">
        <div class="timeline-content">
            <strong>
                <i class="timeline-icon"></i>
                <span class="timeline-description"></span>
            </strong>
            <div class="timeline-time"></div>
            <small class="text-muted timeline-status-description"></small>
        </div>
    </div>
</script>

<script type="text/template" id="timeline-empty-template">
    <div class="empty-state">
        <i class="fa fa-file-o fa-2x"></i>
        <p>暂无时间线记录</p>
    </div>
</script>

<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    /*<![CDATA[*/
    const prefix = ctx + "ph/medical-record-application";
    const applicationId = /*[[${detail?.id ?: 0}]]*/ 0;

    // 删除记录
    function deleteRecord() {
      $.modal.confirm(
        "确认要删除这条申请记录吗？删除后无法恢复！",
        function () {
          $.ajax({
            url: prefix + "/remove/" + applicationId,
            type: "POST",
            success: function (result) {
              if (result.code === 0) {
                top.layer.msg(
                  "删除成功",
                  {
                    icon: $.modal.icon(modal_status.SUCCESS),
                    time: 1000,
                    shade: [0.1, "#8F8F8F"],
                  },
                  function () {
                    $.modal.closeTab();
                  }
                );
              } else {
                $.modal.alertError(result.msg);
              }
            },
          });
        }
      );
    }

    // 核心下载逻辑
    function startFileDownload(fileUrl, fileName) {
      if (!fileUrl || !fileName) {
        $.modal.alertWarning("文件信息不完整，无法下载");
        return;
      }

      const link = document.createElement("a");
      link.href = fileUrl;
      link.download = fileName;
      link.style.display = "none";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    // 下载文件（通过按钮触发）
    function downloadFile(button) {
      const fileUrl = button.getAttribute("data-url");
      const fileName = button.getAttribute("data-filename");
      startFileDownload(fileUrl, fileName);
    }

    // 预览文件
    function previewFile(button) {
      const fileUrl = button.getAttribute("data-url");
      const fileName = button.getAttribute("data-filename");
      $("#filePreviewModalLabel").text(fileName);
      const extension = fileName.toLowerCase().split(".").pop();
      const previewContainer = $("#previewContainer");
      previewContainer.empty();

      if (["jpg", "jpeg", "png", "gif", "bmp", "webp"].includes(extension)) {
        const img = $("<img>").attr({
          src: fileUrl,
          style: "max-width: 100%; max-height: 60vh; object-fit: contain;",
          alt: fileName,
        });
        previewContainer.append(img);
        img.on("error", function () {
          previewContainer.html(
            '<div class="alert alert-warning"><i class="fa fa-exclamation-triangle"></i> 图片加载失败</div>'
          );
        });
      } else if (extension === "pdf") {
        const iframe = $("<iframe>").attr({
          src: fileUrl,
          style: "width: 100%; height: 60vh; border: none;",
          title: fileName,
        });
        previewContainer.append(iframe);
      } else if (["txt", "log", "md", "json", "xml", "csv"].includes(extension)) {
        previewContainer.html(
          '<div class="text-center"><i class="fa fa-spinner fa-spin"></i> 加载中...</div>'
        );
        $.get(fileUrl)
          .done(function (data) {
            const pre = $("<pre>")
              .css({
                "text-align": "left",
                "max-height": "60vh",
                overflow: "auto",
                background: "#f8f9fa",
                padding: "15px",
                "border-radius": "4px",
              })
              .text(data);
            previewContainer.html(pre);
          })
          .fail(function () {
            previewContainer.html(
              '<div class="alert alert-warning"><i class="fa fa-exclamation-triangle"></i> 文本文件加载失败</div>'
            );
          });
      } else if (["doc", "docx", "xls", "xlsx", "ppt", "pptx"].includes(extension)) {
        const template = $("#office-hint-template").html();
        const element = $(template);
        element.find(".download-btn").on("click", function () {
          startFileDownload(fileUrl, fileName);
        });
        previewContainer.append(element);
      } else {
        const template = $("#unsupported-hint-template").html();
        const element = $(template);
        element.find(".download-btn").on("click", function () {
          startFileDownload(fileUrl, fileName);
        });
        previewContainer.append(element);
      }

      $("#downloadFromPreview")
        .off("click")
        .on("click", function () {
          startFileDownload(fileUrl, fileName);
        });

      $("#filePreviewModal").modal("show");
    }

    // 加载时间线数据
    function loadTimeline(includeDetails = false) {
      const container = $("#timelineContainer");
      container.html(
        '<div class="timeline-loading text-center"><i class="fa fa-spinner fa-spin"></i> 加载时间线中...</div>'
      );

      $.ajax({
        url: prefix + "/timeline/" + applicationId,
        type: "GET",
        data: { includeDetails: includeDetails },
        success: function (result) {
          if (result.code === 0) {
            renderTimeline(result.data);
          } else {
            container.html(
              '<div class="alert alert-warning text-center"><i class="fa fa-exclamation-triangle"></i> 加载时间线失败：' +
                result.msg +
                "</div>"
            );
          }
        },
        error: function () {
          container.html(
            '<div class="alert alert-danger text-center"><i class="fa fa-times-circle"></i> 时间线加载失败，请刷新页面重试</div>'
          );
        },
      });
    }

    // 渲染时间线
    function renderTimeline(timelineData) {
      const container = $("#timelineContainer");
      if (!timelineData || timelineData.length === 0) {
        const emptyTemplate = $("#timeline-empty-template").html();
        container.html(emptyTemplate);
        return;
      }

      container.empty();
      const sortedData = timelineData.reverse();

      sortedData.forEach(function (event, index) {
        let statusClass = "";
        if (event.isImportant) {
          if (event.eventType.code === "APPLICATION_APPROVED") {
            statusClass = "completed";
          } else if (event.eventType.code === "APPLICATION_REJECTED") {
            statusClass = "rejected";
          } else if (index === sortedData.length - 1) {
            statusClass = "active";
          } else {
            statusClass = "completed";
          }
        } else {
          statusClass = "completed";
        }

        const template = $("#timeline-item-template").html();
        const timelineItem = $(template);
        timelineItem.addClass(statusClass);
        timelineItem.attr("data-event-type", event.eventType.code || "");

        const iconClass = getEventIconClass(event);
        timelineItem.find(".timeline-icon").attr("class", "timeline-icon " + iconClass);
        timelineItem.find(".timeline-description").text(event.eventDescription || "未知事件");
        timelineItem.find(".timeline-time").text(formatDateTime(event.eventTime));
        timelineItem.find(".timeline-status-description").text(generateStatusDescription(event));

        container.append(timelineItem);
      });
    }

    // 获取事件图标
    function getEventIconClass(event) {
      const iconClassMap = {
        APPLICATION_CREATED: "fa fa-plus-circle",
        APPLICATION_SUBMITTED: "fa fa-paper-plane",
        APPLICATION_APPROVED: "fa fa-check-circle",
        APPLICATION_REJECTED: "fa fa-times-circle",
        APPLICATION_PAID: "fa fa-money",
        APPLICATION_DELIVERED: "fa fa-truck",
        PATIENT_INFO_UPDATED: "fa fa-edit",
        AGENT_INFO_UPDATED: "fa fa-edit",
        AGENT_INFO_SET: "fa fa-user-plus",
        AGENT_INFO_CLEARED: "fa fa-user-times",
        MAILING_INFO_SET: "fa fa-truck",
        ATTACHMENT_UPLOADED: "fa fa-upload",
      };
      return iconClassMap[event.eventType.code] || "fa fa-info-circle";
    }

    // 生成状态描述
    function generateStatusDescription(event) {
      const descriptionMap = {
        APPLICATION_CREATED: "申请记录创建完成",
        APPLICATION_SUBMITTED: "申请已提交，等待审核",
        APPLICATION_APPROVED: "审核通过，等待支付",
        APPLICATION_REJECTED: "申请被驳回，需要整改后重新提交",
        APPLICATION_PAID: "支付完成，准备邮寄",
        APPLICATION_DELIVERED: "已邮寄，申请流程完成",
        PATIENT_INFO_UPDATED: "患者信息已更新",
        AGENT_INFO_UPDATED: "代办人信息已更新",
        AGENT_INFO_SET: "代办人信息已设置",
        AGENT_INFO_CLEARED: "代办人信息已清除",
        MAILING_INFO_SET: "邮寄信息已设置",
        ATTACHMENT_UPLOADED: "附件已上传",
      };
      return descriptionMap[event.eventType.code] || "操作完成";
    }

    // 格式化日期时间
    function formatDateTime(dateTimeString) {
      if (!dateTimeString) return "-";
      const date = new Date(dateTimeString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    // 审核功能
    function initApprovalFunctions() {
      let isApprovalMode = false;

      $("#toggleApproval").click(function () {
        isApprovalMode = true;
        $("#approvalPanel").addClass("show");
        $(this).prop("disabled", true);
        $("html, body").animate({
          scrollTop: $("#approvalPanel").offset().top - 20,
        }, 500);
      });

      $("#cancelApproval").click(function () {
        isApprovalMode = false;
        $("#approvalPanel").removeClass("show");
        $("#toggleApproval").prop("disabled", false);
        resetApprovalForm();
      });

      $(".approval-option").click(function () {
        const status = $(this).data("status");
        const isApprove = status === "APPROVED";

        $(".approval-option").removeClass("selected");
        $(this).addClass("selected");

        if (isApprove) {
          $("#rejectDetails").slideUp(200, function () {
            $("#approveDetails").slideDown(300);
          });
          $("#rejectReason").removeAttr("required");
        } else {
          $("#approveDetails").slideUp(200, function () {
            $("#rejectDetails").slideDown(300);
          });
          $("#rejectReason").attr("required", true);
        }

        updateSubmitButtonState(status, isApprove);
      });

      function updateSubmitButtonState(status, isApprove) {
        const $submitBtn = $("#submitApproval");
        const actionText = isApprove ? "通过" : "驳回";
        const buttonClass = isApprove ? "btn-success" : "btn-warning";

        $submitBtn
          .prop("disabled", false)
          .attr("data-status", status)
          .removeClass("btn-success btn-warning btn-primary")
          .addClass(buttonClass)
          .html(`<i class="fa fa-gavel"></i> 提交${actionText}`);
      }

      // 费用计算
      $("#copyFee, #shippingFee").on("input", function () {
        const copyFee = parseFloat($("#copyFee").val()) || 0;
        const shippingFee = parseFloat($("#shippingFee").val()) || 0;
        const totalFee = copyFee + shippingFee;

        if (totalFee > 0) {
          $("#totalFeeAmount").text("¥" + totalFee.toFixed(2));
          $("#totalFeeDisplay").slideDown();
        } else {
          $("#totalFeeDisplay").slideUp();
        }
      });

      // 字符计数
      $("#rejectReason").on("input", function () {
        const count = $(this).val().length;
        $("#rejectReasonCount").text(count);
        if (count > 500) {
          $(this).val($(this).val().substring(0, 500));
          $("#rejectReasonCount").text(500);
        }
      });

      $("#correctionAdvice").on("input", function () {
        const count = $(this).val().length;
        $("#correctionAdviceCount").text(count);
        if (count > 1000) {
          $(this).val($(this).val().substring(0, 1000));
          $("#correctionAdviceCount").text(1000);
        }
      });

      // 提交审核
      $("#submitApproval").click(function () {
        const status = $(this).attr("data-status");
        const rejectReason = $("#rejectReason").val().trim();
        const correctionAdvice = $("#correctionAdvice").val().trim();
        const copyFee = parseFloat($("#copyFee").val()) || null;
        const shippingFee = parseFloat($("#shippingFee").val()) || null;

        if (status === "REJECTED" && !rejectReason) {
          $.modal.alertWarning("驳回时必须填写驳回原因");
          return;
        }

        if (status === "APPROVED" && (copyFee === null || shippingFee === null)) {
          $.modal.alertWarning("审核通过时必须设置复印费用和快递费用");
          return;
        }

        const statusText = status === "APPROVED" ? "通过" : "驳回";
        const message = `确认要${statusText}这条申请记录吗？`;

        $.modal.confirm(message, function () {
          const data = {
            status: status,
            rejectReason: rejectReason,
            correctionAdvice: correctionAdvice,
            copyFee: copyFee,
            shippingFee: shippingFee,
          };

          $("#submitApproval")
            .prop("disabled", true)
            .html('<i class="fa fa-spinner fa-spin"></i> 提交中...');

          $.ajax({
            url: prefix + "/approve/" + applicationId,
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify(data),
            success: function (result) {
              if (result.code === 0) {
                top.layer.msg(
                  "审核成功",
                  {
                    icon: $.modal.icon(modal_status.SUCCESS),
                    time: 1000,
                    shade: [0.1, "#8F8F8F"],
                  },
                  function () {
                    window.location.reload();
                  }
                );
              } else {
                $.modal.alertError(result.msg || "审核操作失败");
                restoreSubmitButton();
              }
            },
            error: function () {
              $.modal.alertError("网络连接失败，请检查网络后重试");
              restoreSubmitButton();
            },
          });
        });
      });

      function restoreSubmitButton() {
        const $submitBtn = $("#submitApproval");
        const currentStatus = $submitBtn.attr("data-status");
        const isApprove = currentStatus === "APPROVED";
        updateSubmitButtonState(currentStatus, isApprove);
      }

      function resetApprovalForm() {
        $(".approval-option").removeClass("selected");
        $("#rejectDetails, #approveDetails").hide();
        $("#rejectReason, #correctionAdvice, #copyFee, #shippingFee").val("");
        $("#rejectReasonCount, #correctionAdviceCount").text("0");
        $("#totalFeeDisplay").hide();
        $("#submitApproval")
          .prop("disabled", true)
          .attr("data-status", "")
          .removeClass("btn-success btn-warning")
          .addClass("btn-primary")
          .html('<i class="fa fa-gavel"></i> 提交审核');
      }
    }

    // 邮寄功能
    function initShippingFunctions() {
      $("#toggleShipping").click(function () {
        $("#shippingPanel").addClass("show");
        $(this).prop("disabled", true);
        $("html, body").animate({
          scrollTop: $("#shippingPanel").offset().top - 20,
        }, 500);
      });

      $("#cancelShipping").click(function () {
        $("#shippingPanel").removeClass("show");
        $("#toggleShipping").prop("disabled", false);
        resetShippingForm();
      });

      $("#shippingNote").on("input", function () {
        const count = $(this).val().length;
        $("#shippingNoteCount").text(count);
        if (count > 200) {
          $(this).val($(this).val().substring(0, 200));
          $("#shippingNoteCount").text(200);
        }
      });

      $("#submitShipping").click(function () {
        const trackingNumber = $("#trackingNumber").val().trim();
        const shippingNote = $("#shippingNote").val().trim();

        if (!trackingNumber) {
          $.modal.alertWarning("请输入快递单号");
          return;
        }

        if (trackingNumber.length < 8) {
          $.modal.alertWarning("快递单号长度不能少于8位");
          return;
        }

        $.modal.confirm("确认邮寄这条申请记录吗？", function () {
          const data = {
            trackingNumber: trackingNumber,
            shippingNote: shippingNote,
          };

          $("#submitShipping")
            .prop("disabled", true)
            .html('<i class="fa fa-spinner fa-spin"></i> 提交中...');

          $.ajax({
            url: prefix + "/ship/" + applicationId,
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify(data),
            success: function (result) {
              if (result.code === 0) {
                top.layer.msg(
                  "邮寄成功",
                  {
                    icon: $.modal.icon(modal_status.SUCCESS),
                    time: 1000,
                    shade: [0.1, "#8F8F8F"],
                  },
                  function () {
                    window.location.reload();
                  }
                );
              } else {
                $.modal.alertError(result.msg || "邮寄操作失败");
                $("#submitShipping")
                  .prop("disabled", false)
                  .html('<i class="fa fa-truck"></i> 确认邮寄');
              }
            },
            error: function () {
              $.modal.alertError("网络连接失败，请检查网络后重试");
              $("#submitShipping")
                .prop("disabled", false)
                .html('<i class="fa fa-truck"></i> 确认邮寄');
            },
          });
        });
      });

      function resetShippingForm() {
        $("#trackingNumber, #shippingNote").val("");
        $("#shippingNoteCount").text("0");
      }
    }

    // 页面初始化
    $(document).ready(function () {
      // 加载时间线
      loadTimeline($("#timelineDetailSwitch").is(":checked"));
      $("#timelineDetailSwitch").change(function () {
        loadTimeline($(this).is(":checked"));
      });

      // 初始化功能
      initApprovalFunctions();
      initShippingFunctions();
    });
    /*]]>*/
</script>
</body>
</html>