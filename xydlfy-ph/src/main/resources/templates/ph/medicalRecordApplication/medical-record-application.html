<!DOCTYPE html>
<html
        lang="zh"
        xmlns:th="http://www.thymeleaf.org"
>
<head>
    <th:block th:include="include :: header('病历复印申请管理')"/>
    <title>病历复印申请管理</title>
    <style>
        /* 美化搜索和重置按钮 */
        .btn-custom {
          border-radius: 4px;
          border: none;
          padding: 6px 15px;
          transition: all 0.3s;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
          font-weight: 500;
        }

        .btn-search {
          background-color: #1890ff;
          color: white;
        }

        .btn-search:hover {
          background-color: #40a9ff;
        }

        .btn-reset {
          background-color: #faad14;
          color: white;
        }

        .btn-reset:hover {
          background-color: #ffc53d;
        }

        /* 工具栏按钮美化 */
        .btn-toolbar {
          padding: 8px 16px;
          margin-right: 10px;
          border-radius: 4px;
          border: none;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
          font-weight: 500;
          transition: all 0.3s;
        }

        /* 刷新统计按钮 */
        .btn-refresh {
          background-color: #1890ff;
          color: white;
        }

        .btn-refresh:hover {
          background-color: #40a9ff;
        }

        /* 禁用状态样式 */
        .btn-toolbar.disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .btn-toolbar.disabled:hover {
          opacity: 0.6;
        }

        /* 调整搜索区域样式 */
        .search-collapse {
          padding: 15px;
          background: #f9f9f9;
          border-radius: 4px;
          margin-bottom: 15px;
        }

        .select-list ul li {
          margin-bottom: 5px;
        }

        /* 调整按钮内的图标间距 */
        .btn-custom i,
        .btn-toolbar i {
          margin-right: 5px;
        }

        .status-badge {
          display: inline-block;
          min-width: 60px;
          padding: 5px 10px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: bold;
        }
        .status-draft {
          background-color: #f39c12;
          color: white;
        }
        .status-submitted {
          background-color: #3498db;
          color: white;
        }
        .status-approved {
          background-color: #27ae60;
          color: white;
        }
        .status-rejected {
          background-color: #e74c3c;
          color: white;
        }
        .status-paid {
          background-color: #9b59b6;
          color: white;
        }
        .status-delivered {
          background-color: #2c3e50;
          color: white;
        }

        .statistics-card {
          height: auto !important;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 20px;
          border-radius: 4px;
          margin-top: 15px;
          margin-bottom: 15px;
        }
        .stat-item {
          display: inline-block;
          margin-right: 30px;
        }
        .stat-number {
          font-size: 24px;
          font-weight: bold;
        }
        .stat-label {
          font-size: 14px;
          opacity: 0.9;
        }

        .datetime {
          display: inline-block;
          min-width: 80px;
          text-align: center;
        }

        /* 费用信息样式 */
        .fee-info {
          font-weight: bold;
          color: #e67e22;
        }

        .fee-total {
          font-weight: bold;
          color: #27ae60;
          font-size: 14px;
        }

        /* 快递信息样式 */
        .tracking-number {
          font-family: 'Courier New', monospace;
          background-color: #f8f9fa;
          padding: 2px 6px;
          border-radius: 3px;
          border: 1px solid #dee2e6;
          font-size: 12px;
        }

        /* 操作列按钮美化 */
        .action-btn {
          border-radius: 4px;
          border: none;
          padding: 4px 8px;
          margin: 0 2px;
          transition: all 0.3s;
          font-size: 12px;
          font-weight: 500;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          min-width: 60px;
          text-decoration: none !important;
        }

        .action-btn:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
          text-decoration: none !important;
        }

        .action-btn:active {
          transform: translateY(0);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .action-btn i {
          margin-right: 4px;
        }

        /* 查看按钮 */
        .action-btn.btn-view {
          background-color: #52c41a;
          color: white;
        }

        .action-btn.btn-view:hover {
          background-color: #73d13d;
          color: white;
        }

        /* 审批按钮 */
        .action-btn.btn-approve {
          background-color: #1890ff;
          color: white;
        }

        .action-btn.btn-approve:hover {
          background-color: #40a9ff;
          color: white;
        }

        /* 邮寄按钮 */
        .action-btn.btn-ship {
          background-color: #fa8c16;
          color: white;
        }

        .action-btn.btn-ship:hover {
          background-color: #ffa940;
          color: white;
        }

        /* 支付按钮 */
        .action-btn.btn-pay {
          background-color: #722ed1;
          color: white;
        }

        .action-btn.btn-pay:hover {
          background-color: #9254de;
          color: white;
        }

        /* 操作列容器 */
        .action-buttons {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          gap: 4px;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
          .action-buttons {
            flex-direction: column;
            align-items: center;
          }

          .action-btn {
            min-width: 80px;
            margin: 2px 0;
          }

          .stat-item {
            margin-right: 20px;
            margin-bottom: 10px;
          }
        }

        /* 收入统计特殊样式 */
        .revenue-stat {
          margin-top: 10px;
        }

        .revenue-stat .stat-number {
          font-size: 20px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <!-- 统计信息卡片 -->
    <div class="row statistics-card">
        <div class="col-md-12">
            <h5><i class="fa fa-bar-chart"></i> 申请统计概况</h5>
            <div id="statisticsContent">
                <div class="stat-item">
                    <div class="stat-number" id="totalCount">-</div>
                    <div class="stat-label">总申请数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="draftCount">-</div>
                    <div class="stat-label">草稿</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="pendingCount">-</div>
                    <div class="stat-label">待审核</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="approvedCount">-</div>
                    <div class="stat-label">已通过</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="paidCount">-</div>
                    <div class="stat-label">已支付</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="deliveredCount">-</div>
                    <div class="stat-label">已邮寄</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="todayCount">-</div>
                    <div class="stat-label">今日新增</div>
                </div>
            </div>
            <!-- 收入统计 -->
            <div class="revenue-stat">
                <div class="stat-item">
                    <div class="stat-number" id="totalRevenue">¥0.00</div>
                    <div class="stat-label">总收入</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="averageFee">¥0.00</div>
                    <div class="stat-label">平均费用</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="pendingPaymentCount">-</div>
                    <div class="stat-label">待支付</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="pendingShippingCount">-</div>
                    <div class="stat-label">待邮寄</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label for="patientName">患者姓名：</label>
                            <input
                                    type="text"
                                    id="patientName"
                                    name="patientName"
                                    placeholder="请输入患者姓名"
                            />
                        </li>
                        <li>
                            <label for="patientIdCardNo">身份证号：</label>
                            <input
                                    type="text"
                                    id="patientIdCardNo"
                                    name="patientIdCardNo"
                                    placeholder="请输入身份证号"
                            />
                        </li>
                        <li>
                            <label for="admissionId">住院号：</label>
                            <input
                                    type="text"
                                    id="admissionId"
                                    name="admissionId"
                                    placeholder="请输入住院号"
                            />
                        </li>
                        <li>
                            <label for="status">申请状态：</label>
                            <select id="status" name="status">
                                <option value="">全部</option>
                                <option value="DRAFT">草稿</option>
                                <option value="SUBMITTED">待审核</option>
                                <option value="APPROVED">已通过</option>
                                <option value="REJECTED">已驳回</option>
                                <option value="PAID">已支付</option>
                                <option value="DELIVERED">已邮寄</option>
                            </select>
                        </li>
                        <li>
                            <label for="recipientName">收件人：</label>
                            <input
                                    type="text"
                                    id="recipientName"
                                    name="recipientName"
                                    placeholder="请输入收件人姓名"
                            />
                        </li>
                        <li class="select-time">
                            <label for="submitDateStart">提交时间：</label>
                            <input
                                    type="text"
                                    class="time-input"
                                    id="submitDateStart"
                                    placeholder="开始时间"
                                    name="submitDateStart"
                            />
                            <span>-</span>
                            <input
                                    type="text"
                                    class="time-input"
                                    id="submitDateEnd"
                                    placeholder="结束时间"
                                    name="submitDateEnd"
                            />
                        </li>
                        <li>
                            <button
                                    type="button"
                                    class="btn btn-custom btn-search"
                                    onclick="$.table.search()"
                            >
                                <i class="fa fa-search"></i>&nbsp;搜索
                            </button>
                            <button
                                    type="button"
                                    class="btn btn-custom btn-reset"
                                    onclick="$.form.reset()"
                            >
                                <i class="fa fa-refresh"></i>&nbsp;重置
                            </button>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <fieldset class="btn-group-sm" id="toolbar">
            <button
                    type="button"
                    class="btn btn-toolbar btn-refresh"
                    onclick="refreshStatistics()"
            >
                <i class="fa fa-refresh"></i> 刷新统计
            </button>
        </fieldset>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script src="//cdn.jsdelivr.net/npm/dayjs@1.11.13/dayjs.min.js"></script>
<script th:inline="javascript">
    const prefix = ctx + "ph/medical-record-application";
    /*<![CDATA[*/
    const queryFlag = /*[[${@permission.isPermitted('ph:medical-record-application:query')}]]*/ false;
    const approveFlag = /*[[${@permission.isPermitted('ph:medical-record-application:approve')}]]*/ false;
    const shipFlag = /*[[${@permission.isPermitted('ph:medical-record-application:ship')}]]*/ false;
    /*]]>*/

    $(function () {
      // 设置默认日期：开始时间为一个月前，结束时间为明天
      const startDate = dayjs().subtract(1, "month").format("YYYY-MM-DD");
      const endDate = dayjs().add(1, "day").format("YYYY-MM-DD");
      $("#submitDateStart").val(startDate);
      $("#submitDateEnd").val(endDate);

      const options = {
        url: prefix + "/list",
        exportUrl: prefix + "/export",
        modalName: "病历复印申请记录",
        showSearch: false,
        showColumns: false,
        showToggle: false,
        columns: [
          {
            checkbox: true,
          },
          {
            field: "id",
            title: "ID",
            visible: false,
          },
          {
            field: "status.code",
            title: "状态",
            width: "80px",
            align: "center",
            formatter: function (value, row, index) {
              let badgeClass = "";
              let statusText = "";
              switch (value) {
                case "DRAFT":
                  badgeClass = "status-draft";
                  statusText = "草稿";
                  break;
                case "SUBMITTED":
                  badgeClass = "status-submitted";
                  statusText = "待审核";
                  break;
                case "APPROVED":
                  badgeClass = "status-approved";
                  statusText = "已通过";
                  break;
                case "REJECTED":
                  badgeClass = "status-rejected";
                  statusText = "已驳回";
                  break;
                case "PAID":
                  badgeClass = "status-paid";
                  statusText = "已支付";
                  break;
                case "DELIVERED":
                  badgeClass = "status-delivered";
                  statusText = "已邮寄";
                  break;
                default:
                  statusText = value;
              }
              return `<span class="status-badge ${badgeClass}">${statusText}</span>`;
            },
          },
          {
            field: "patientName",
            title: "患者姓名",
            width: "100px",
            align: "center",
          },
          {
            field: "agentName",
            title: "代办人姓名",
            width: "100px",
            align: "center",
          },
          {
            field: "recipientName",
            title: "收件人",
            width: "100px",
            align: "center",
          },
          {
            field: "admissionId",
            title: "住院号",
            width: "100px",
            align: "center",
          },
          {
            field: "copyPurposeName",
            title: "复印用途",
            width: "120px",
            align: "center",
          },
          {
            field: "totalFee",
            title: "总费用",
            width: "100px",
            align: "center",
            formatter: function (value, row, index) {
              if (value) {
                return `<span class="fee-total">¥${parseFloat(value).toFixed(2)}</span>`;
              }
              return "-";
            },
          },
          {
            field: "trackingNumber",
            title: "快递单号",
            width: "150px",
            align: "center",
            formatter: function (value, row, index) {
              if (value) {
                return `<span class="tracking-number">${value}</span>`;
              }
              return "-";
            },
          },
          {
            field: "submitTime",
            title: "提交时间",
            width: "150px",
            align: "center",
            formatter: function (value, row, index) {
              const content = value ? value.replace(" ", "<br>") : "-";
              return `<div class="datetime">${content}</div>`;
            },
          },
          {
            field: "approveTime",
            title: "审批时间",
            width: "150px",
            align: "center",
            formatter: function (value, row, index) {
              const content = value ? value.replace(" ", "<br>") : "-";
              return `<div class="datetime">${content}</div>`;
            },
          },
          {
            field: "payTime",
            title: "支付时间",
            width: "150px",
            align: "center",
            formatter: function (value, row, index) {
              const content = value ? value.replace(" ", "<br>") : "-";
              return `<div class="datetime">${content}</div>`;
            },
          },
          {
            field: "shippingTime",
            title: "邮寄时间",
            width: "150px",
            align: "center",
            formatter: function (value, row, index) {
              const content = value ? value.replace(" ", "<br>") : "-";
              return `<div class="datetime">${content}</div>`;
            },
          },
          {
            title: "操作",
            width: "200px",
            align: "center",
            formatter: function (value, row, index) {
              const actions = [];

              if (queryFlag) {
                actions.push(
                  `<a class="action-btn btn-view" href="javascript:void(0)" onclick="viewDetail('${row.id}', '申请详情 - ${row.patientName}')" title="查看详情"><i class="fa fa-eye"></i>查看</a>`
                );
              }

              if (approveFlag && row.canApprove) {
                actions.push(
                  `<a class="action-btn btn-approve" href="javascript:void(0)" onclick="approveApplication('${row.id}')" title="审批"><i class="fa fa-gavel"></i>审批</a>`
                );
              }

              if (shipFlag && row.canShip) {
                actions.push(
                  `<a class="action-btn btn-ship" href="javascript:void(0)" onclick="shipApplication('${row.id}')" title="邮寄"><i class="fa fa-truck"></i>邮寄</a>`
                );
              }

              return `<div class="action-buttons">${actions.join("")}</div>`;
            },
          },
        ],
      };
      $.table.init(options);

      // 加载统计信息
      loadStatistics();
    });

    // 加载统计信息
    function loadStatistics() {
      $.ajax({
        url: prefix + "/statistics",
        type: "GET",
        success: function (result) {
          if (result.code === 0) {
            const data = result.data;
            $("#totalCount").text(data.totalCount || 0);
            $("#draftCount").text(data.draftCount || 0);
            $("#pendingCount").text(data.pendingApprovalCount || 0);
            $("#approvedCount").text(data.approvedCount || 0);
            $("#paidCount").text(data.paidCount || 0);
            $("#deliveredCount").text(data.deliveredCount || 0);
            $("#todayCount").text(data.todayCount || 0);

            // 收入统计
            $("#totalRevenue").text("¥" + (data.totalRevenue ? parseFloat(data.totalRevenue).toFixed(2) : "0.00"));
            $("#averageFee").text("¥" + (data.averageFee ? parseFloat(data.averageFee).toFixed(2) : "0.00"));
            $("#pendingPaymentCount").text(data.pendingPaymentCount || 0);
            $("#pendingShippingCount").text(data.pendingShippingCount || 0);
          }
        },
      });
    }

    // 刷新统计信息
    function refreshStatistics() {
      loadStatistics();
      $.modal.msgSuccess("统计信息已刷新");
    }

    // 查看详情
    function viewDetail(id, title) {
      const url = prefix + "/detail/" + id;
      $.modal.openTab(title, url);
    }

    // 单个审批
    function approveApplication(id) {
      viewDetail(id, "病历邮寄申请 - 审批");
    }

    // 邮寄申请
    function shipApplication(id) {
      viewDetail(id, "病历邮寄申请 - 邮寄");
    }

    // 重置表单后刷新统计
    $.form.reset = function () {
      $("#formId")[0].reset();

      // 重置默认日期
      const startDate = dayjs().subtract(1, "month").format("YYYY-MM-DD");
      const endDate = dayjs().add(1, "day").format("YYYY-MM-DD");
      $("#submitDateStart").val(startDate);
      $("#submitDateEnd").val(endDate);

      $.table.search();
      loadStatistics();
    };
</script>
</body>
</html> 