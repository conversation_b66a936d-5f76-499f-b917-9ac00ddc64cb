<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增提示信息')" />
    <th:block th:include="include :: datetimepicker-css" />
    <th:block th:include="include :: summernote-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-tip-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">编码：</label>
                <div class="col-sm-8">
                    <input name="code" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">标题：</label>
                <div class="col-sm-8">
                    <input name="title" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">内容：</label>
                <div class="col-sm-8">
                    <input type="hidden" class="form-control" name="content">
                    <div class="summernote" id="content"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">样式：</label>
                <div class="col-sm-8">
                    <textarea name="style" class="form-control" required></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">生效时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="effectiveTime" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">过期时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="expireTime" class="form-control" placeholder="yyyy-MM-dd" type="text" required>
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <th:block th:include="include :: summernote-js" />
    <script th:inline="javascript">
        var prefix = ctx + "ph/tip"
        $("#form-tip-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-tip-add').serialize());
            }
        }

        $("input[name='effectiveTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='expireTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $(function() {
            $('.summernote').summernote({
                lang: 'zh-CN',
                dialogsInBody: true,
                callbacks: {
                    onChange: function(contents, $edittable) {
                        $("input[name='" + this.id + "']").val(contents);
                    },
                    onImageUpload: function(files) {
                        var obj = this;
                    	var data = new FormData();
                    	data.append("file", files[0]);
                    	$.ajax({
                            type: "post",
                            url: ctx + "common/upload",
                    		data: data,
                    		cache: false,
                    		contentType: false,
                    		processData: false,
                    		dataType: 'json',
                    		success: function(result) {
                    		    if (result.code == web_status.SUCCESS) {
                    		        $('#' + obj.id).summernote('insertImage', result.url);
                    		    } else {
                    		        $.modal.alertError(result.msg);
                    		    }
                    		},
                    		error: function(error) {
                    		    $.modal.alertWarning("图片上传失败。");
                    		}
                    	});
                    }
                }
            });
        });
    </script>
</body>
</html>