<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:insert="~{include :: header('医生列表')}"/>
    <th:block th:include="include :: bootstrap-editable-css" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
<!--                        <li>-->
<!--                            <label>科室：</label>-->
<!--                            <select name="departmentId">-->
<!--                                <option value="">所有</option>-->
<!--                                &lt;!&ndash;/*@thymesVar id="departments" type="java.util.List<space.lzhq.ph.domain.Department>"*/&ndash;&gt;-->
<!--                                <option th:each="department:${departments}"-->
<!--                                        th:value="${department.id}"-->
<!--                                        th:text="${department.name}"-->
<!--                                ></option>-->
<!--                            </select>-->
<!--                        </li>-->
                        <li>
                            <label>姓名：</label>
                            <input type="text" name="name"/>
                        </li>
                        <li>
                            <label>性别：</label>
                            <select name="sex" th:with="type=${@dict.getType('sys_user_sex')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>简拼：</label>
                            <input type="text" name="simplePinyin"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i> 搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i> 重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ph:doctor:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="ph:doctor:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="ph:doctor:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-danger" onclick="syncDoctors()" shiro:hasPermission="ph:doctor:sync">
                <i class="fa fa-refresh"></i> 同步
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:doctor:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('ph:doctor:edit')}]];
    var removeFlag = [[${@permission.hasPermi('ph:doctor:remove')}]];
    var sexDatas = [[${@dict.getType('sys_user_sex')}]];
    var prefix = ctx + "ph/doctor";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            onEditableSave: updateCell,
            modalName: "医生",
            uniqueId: "id",
            columns: [{
                checkbox: true
            },
                {
                    field: 'departmentId',
                    title: '科室编码'
                },
                {
                    field: 'department.name',
                    title: '科室名称'
                },
                {
                    field: 'id',
                    title: '编码',
                    visible: true
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'sex',
                    title: '性别',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(sexDatas, value);
                    }
                },
                {
                    field: 'title',
                    title: '职称'
                },
                {
                    field: 'position',
                    title: '职务'
                },
                {
                    field: 'regType',
                    title: '挂号类型'
                },
                {
                    field: 'regFee',
                    title: '挂号费用'
                },
                {
                    field: 'expertise',
                    title: '擅长'
                },
                {
                    field: 'intro',
                    title: '简介'
                },
                {
                    field: 'photo',
                    title: '照片',
                    formatter: function (value, row, index) {
                        return $.table.imageView(value, '', '', 'blank');
                    }
                },
                {
                    field: 'sortNo',
                    title: '排序号',
                    editable: function (value, row, index) {
                        if ([[${@permission.hasPermi('ph:doctor:edit')}]] === "") {
                            return {
                                type : 'text',
                                // title : '排序号',
                                validate : function(value) {
                                    if (!value) {
                                        return '排序号不能为空';
                                    }
                                    var v = Number(value)
                                    if (typeof v !== 'number') {
                                        return '排序号必须是数字';
                                    }
                                    if ((v | 0) !== v) {
                                        return '排序号必须是整数';
                                    }
                                    if (v < 0) {
                                        return '排序号必须大于等于0';
                                    }
                                }
                            }
                        } else {
                            return false
                        }
                    }
                },
                {
                    field: 'simplePinyin',
                    title: '简拼'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function syncDoctors() {
        $.operate.post(prefix + "/sync");
    }

    function updateCell(field, row) {
        var form = {};
        form['id'] = row['id'];
        form[field] = row[field];
        $.operate.post(prefix + '/edit', form);
    }

    /**
     * 从 doctorCategory.html 选择医生过来，用于关联医生分类
     */
    function submitHandler() {
        var rows = $.table.selectFirstColumns();
        if (rows.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        // 开始关联医生
        alert(rows.join());
    }
</script>
</body>
</html>