<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:insert="~{include :: header('举报列表')}"/>
    <style>
        .select-list li p, .select-list li label:not(.radio-box) {
            width: 105px !important;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>举报类型：</label>
                            <select name="reporterType" th:with="type=${@dict.getType('reporter_type')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>举报人姓名：</label>
                            <input type="text" name="reporterName"/>
                        </li>
                        <li>
                            <label>举报人身份证号：</label>
                            <input type="text" name="reporterIdCardNo"/>
                        </li>
                        <li>
                            <label>举报人手机号：</label>
                            <input type="text" name="reporterMobile"/>
                        </li>
                        <li>
                            <label>举报人级别：</label>
                            <select name="reporterLevel" th:with="type=${@dict.getType('reporterLevel')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>被举报人姓名：</label>
                            <input type="text" name="reportedName"/>
                        </li>
                        <li>
                            <label>被举报人级别：</label>
                            <select name="reportedLevel" th:with="type=${@dict.getType('reportedLevel')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>标题：</label>
                            <input type="text" name="title"/>
                        </li>
                        <li>
                            <label>状态：</label>
                            <select name="status" th:with="type=${@dict.getType('jubao_status')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li class="select-time">
                            <label>创建时间：</label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                   name="params[beginCreateTime]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                   name="params[endCreateTime]"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ph:jubaoCase:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()"
               shiro:hasPermission="ph:jubaoCase:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-primary single disabled" onclick="view()">
                <i class="fa fa-eye"></i> 查看
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="ph:jubaoCase:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-success multiple disabled" onclick="acceptSelected()"
               shiro:hasPermission="ph:jubaoCase:accept">
                <i class="fa fa-play"></i> 受理
            </a>
            <a class="btn btn-success multiple disabled" onclick="finishSelected()"
               shiro:hasPermission="ph:jubaoCase:finish">
                <i class="fa fa-flag"></i> 办结
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:jubaoCase:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table">
            <table id="bootstrap-table" data-mobile-responsive="true" class="table-bordered table-striped"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    const editFlag = [[${@permission.hasPermi('ph:jubaoCase:edit')}]];
    const removeFlag = [[${@permission.hasPermi('ph:jubaoCase:remove')}]];
    const acceptFlag = [[${@permission.hasPermi('ph:jubaoCase:accept')}]];
    const finishFlag = [[${@permission.hasPermi('ph:jubaoCase:finish')}]];
    const reporterTypeDatas = [[${@dict.getType('reporter_type')}]];
    const reporterLevelDatas = [[${@dict.getType('reporterLevel')}]];
    const reportedLevelDatas = [[${@dict.getType('reportedLevel')}]];
    const statusDatas = [[${@dict.getType('jubao_status')}]];
    const prefix = ctx + "ph/jubaoCase";

    $(function () {
        const options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            uniqueId: "id",
            modalName: "举报",
            columns: [
                [
                    {
                        checkbox: true,
                        rowspan: 2,
                    },
                    {
                        field: 'title',
                        title: '标题',
                        rowspan: 2,
                        formatter: function (value, row, index) {
                            return `<a href="javascript:void(0)" onclick="view('${row.id}', '${row.title}')">${value}</a>`;
                        }
                    },
                    {
                        field: 'status',
                        title: '状态',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(statusDatas, value);
                        },
                        rowspan: 2,
                    },
                    {
                        field: 'reporterType',
                        title: '举报类型',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(reporterTypeDatas, value);
                        },
                        rowspan: 2,
                    },
                    {
                        title: '举报人信息',
                        align: 'center',
                        valign: 'middle',
                        colspan: 4,
                    },
                    {
                        title: '被举报人信息',
                        align: 'center',
                        valign: 'middle',
                        colspan: 3,
                    },
                    {
                        title: '操作时间',
                        align: 'center',
                        valign: 'middle',
                        colspan: 3,
                    },
                    {
                        title: '操作',
                        rowspan: 2,
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            const actions = [];
                            actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                            actions.push(`<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="view('${row.id}', '${row.title}')"><i class="fa fa-eye"></i>查看</a> `);
                            actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a> ');
                            if (row.status === '0') {
                                actions.push('<a class="btn btn-success btn-xs ' + acceptFlag + '" href="javascript:void(0)" onclick="accept(\'' + row.id + '\')"><i class="fa fa-play"></i>受理</a> ');
                            }
                            if (row.status === '1') {
                                actions.push('<a class="btn btn-success btn-xs ' + finishFlag + '" href="javascript:void(0)" onclick="finish(\'' + row.id + '\')"><i class="fa fa-flag"></i>办结</a> ');
                            }
                            return actions.join('');
                        }
                    }
                ],
                [
                    {
                        field: 'reporterName',
                        title: '姓名',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'reporterIdCardNo',
                        title: '身份证号',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'reporterMobile',
                        title: '手机号',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'reporterLevel',
                        title: '级别',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(reporterLevelDatas, value);
                        },
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'reportedName',
                        title: '姓名',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'reportedPosition',
                        title: '职务',
                        align: 'center',
                        valign: 'middle',
                    },
                    {
                        field: 'reportedLevel',
                        title: '级别',
                        align: 'center',
                        valign: 'middle',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(reportedLevelDatas, value);
                        }
                    },
                    {
                        field: 'createTime',
                        title: '举报时间',
                        align: 'center',
                        valign: 'middle'
                    },
                    {
                        field: 'acceptTime',
                        title: '受理时间',
                        align: 'center',
                        valign: 'middle'
                    },
                    {
                        field: 'finishTime',
                        title: '办结时间',
                        align: 'center',
                        valign: 'middle'
                    }
                ]
            ]
        };
        $.table.init(options);
    });

    function view(id, title) {
        table.set();

        const row = $("#" + table.options.id).bootstrapTable('getSelections')[0];
        if ($.common.isEmpty(id)) {
            if ($.common.isEmpty(row)) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            id = row['id'];
            title = row['title'];
        }

        $.modal.openFull("举报详情 - " + title, prefix + "/view/" + id);
    }

    function acceptSelected() {
        table.set();
        const rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认受理选中的" + rows.length + "条举报吗?", function () {
            const url = prefix + "/accept";
            const data = {"ids": rows.join()};
            $.operate.submit(url, "post", "json", data);
        });
    }

    function accept(id) {
        table.set();
        $.modal.confirm("确认受理该举报吗?", function () {
            const url = prefix + "/accept";
            const data = {"ids": id};
            $.operate.submit(url, "post", "json", data);
        });
    }

    function finishSelected() {
        table.set();
        const rows = $.common.isEmpty(table.options.uniqueId) ? $.table.selectFirstColumns() : $.table.selectColumns(table.options.uniqueId);
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $.modal.confirm("确认办结选中的" + rows.length + "条举报吗?", function () {
            const url = prefix + "/finish";
            const data = {"ids": rows.join()};
            $.operate.submit(url, "post", "json", data);
        });
    }

    function finish(id) {
        table.set();
        $.modal.confirm("确认办结该举报吗?", function () {
            const url = prefix + "/finish";
            const data = {"ids": id};
            $.operate.submit(url, "post", "json", data);
        });
    }
</script>
</body>
</html>