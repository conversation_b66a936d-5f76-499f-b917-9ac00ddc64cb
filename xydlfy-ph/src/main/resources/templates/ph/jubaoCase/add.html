<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('新增举报')}"/>
    <th:block th:insert="~{include :: datetimepicker-css}"/>
    <th:block th:insert="~{include :: summernote-css}"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-jubaoCase-add">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">客户端类型：</label>
            <div class="col-sm-8">
                <select name="clientType" class="form-control m-b" th:with="type=${@dict.getType('client_type')}"
                        required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">用户ID：</label>
            <div class="col-sm-8">
                <input name="openId" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">举报人类型：</label>
            <div class="col-sm-8">
                <select name="reporterType" class="form-control m-b" th:with="type=${@dict.getType('reporter_type')}"
                        required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">举报人姓名：</label>
            <div class="col-sm-8">
                <input name="reporterName" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">举报人身份证号：</label>
            <div class="col-sm-8">
                <input name="reporterIdCardNo" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">举报人手机号：</label>
            <div class="col-sm-8">
                <input name="reporterMobile" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">举报人现居住地址：</label>
            <div class="col-sm-8">
                <input name="reporterAddress" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">举报人政治面貌：</label>
            <div class="col-sm-8">
                <select name="reporterPoliticalStatus" class="form-control m-b"
                        th:with="type=${@dict.getType('politicalStatus')}">
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">举报人级别：</label>
            <div class="col-sm-8">
                <select name="reporterLevel" class="form-control m-b" th:with="type=${@dict.getType('reporterLevel')}">
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">被举报人姓名：</label>
            <div class="col-sm-8">
                <input name="reportedName" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">被举报人工作单位：</label>
            <div class="col-sm-8">
                <input name="reportedEmployer" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">被举报人职务：</label>
            <div class="col-sm-8">
                <input name="reportedPosition" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">被举报人级别：</label>
            <div class="col-sm-8">
                <select name="reportedLevel" class="form-control m-b" th:with="type=${@dict.getType('reportedLevel')}">
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">标题：</label>
            <div class="col-sm-8">
                <input name="title" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">问题类别：</label>
            <div class="col-sm-8">
                <input name="category1" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">问题细类：</label>
            <div class="col-sm-8">
                <input name="category2" class="form-control" type="text">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">主要问题：</label>
            <div class="col-sm-8">
                <input type="hidden" class="form-control" name="content">
                <div class="summernote" id="content"></div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">附件：</label>
            <div class="col-sm-8">
                <textarea name="attachments" class="form-control" required></textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">状态：</label>
            <div class="col-sm-8">
                <select name="status" class="form-control m-b" th:with="type=${@dict.getType('jubao_status')}" required>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">受理时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="acceptTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">办结时间：</label>
            <div class="col-sm-8">
                <div class="input-group date">
                    <input name="finishTime" class="form-control" placeholder="yyyy-MM-dd" type="text">
                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                </div>
            </div>
        </div>
    </form>
</div>
<th:block th:insert="~{include :: footer}"/>
<th:block th:insert="~{include :: datetimepicker-js}"/>
<th:block th:insert="~{include :: summernote-js}"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/jubaoCase"
    $("#form-jubaoCase-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/add", $('#form-jubaoCase-add').serialize());
        }
    }

    $("input[name='acceptTime']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });

    $("input[name='finishTime']").datetimepicker({
        format: "yyyy-mm-dd",
        minView: "month",
        autoclose: true
    });

    $(function () {
        $('.summernote').summernote({
            lang: 'zh-CN',
            dialogsInBody: true,
            callbacks: {
                onChange: function (contents, $edittable) {
                    $("input[name='" + this.id + "']").val(contents);
                },
                onImageUpload: function (files) {
                    var obj = this;
                    var data = new FormData();
                    data.append("file", files[0]);
                    $.ajax({
                        type: "post",
                        url: ctx + "common/upload",
                        data: data,
                        cache: false,
                        contentType: false,
                        processData: false,
                        dataType: 'json',
                        success: function (result) {
                            if (result.code == web_status.SUCCESS) {
                                $('#' + obj.id).summernote('insertImage', result.url);
                            } else {
                                $.modal.alertError(result.msg);
                            }
                        },
                        error: function (error) {
                            $.modal.alertWarning("图片上传失败。");
                        }
                    });
                }
            }
        });
    });
</script>
</body>
</html>