<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('查看举报')}"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-jubaoCase-edit">
        <input name="id" th:field="*{id}" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label">举报类型：</label>
            <div class="col-sm-8">
                <div class="form-control-static"
                     th:text="${@dict.getLabel('reporter_type', jubaoCase.reporterType + '')}"></div>
            </div>
        </div>
        <fieldset th:if="${jubaoCase.reporterType==1}">
            <legend>举报人信息</legend>
            <div class="form-group">
                <label class="col-sm-3 control-label">姓名：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="${jubaoCase.reporterName}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">身份证号：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="${jubaoCase.reporterIdCardNo}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">手机号：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="${jubaoCase.reporterMobile}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">现居住地址：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="${jubaoCase.reporterAddress}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">政治面貌：</label>
                <div class="col-sm-8">
                    <div class="form-control-static"
                         th:text="${@dict.getLabel('politicalStatus', jubaoCase.reporterPoliticalStatus)}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">级别：</label>
                <div class="col-sm-8">
                    <div class="form-control-static"
                         th:text="${@dict.getLabel('reporterLevel', jubaoCase.reporterLevel)}"></div>
                </div>
            </div>
        </fieldset>
        <fieldset>
            <legend>被举报人信息</legend>
            <div class="form-group">
                <label class="col-sm-3 control-label">姓名：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="${jubaoCase.reportedName}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">工作单位：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="${jubaoCase.reportedEmployer}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">职务：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="${jubaoCase.reportedPosition}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">级别：</label>
                <div class="col-sm-8">
                    <div class="form-control-static"
                         th:text="${@dict.getLabel('reportedLevel', jubaoCase.reportedLevel)}"></div>
                </div>
            </div>
        </fieldset>
        <fieldset>
            <legend>举报问题</legend>
            <div class="form-group">
                <label class="col-sm-3 control-label">标题：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="${jubaoCase.title}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">问题类别：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="${category1}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">问题细类：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="${category2}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">主要问题：</label>
                <div class="col-sm-8">
                    <div class="form-control-static" th:text="${jubaoCase.content}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">附件：</label>
                <div class="col-sm-8">
                    <p th:each="url:${#strings.arraySplit(jubaoCase.attachments, ',')}">
                        <a th:text="${url}" th:href="${url}" target="_blank"></a>
                    </p>
                </div>
            </div>
        </fieldset>
        <div class="form-group">
            <label class="col-sm-3 control-label">状态：</label>
            <div class="col-sm-8">
                <div class="form-control-static" th:text="${@dict.getLabel('jubao_status', jubaoCase.status)}"></div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">举报时间：</label>
            <div class="col-sm-8">
                <div class="form-control-static"
                     th:text="${#dates.format(jubaoCase.createTime, 'yyyy-MM-dd HH:mm:ss')}"></div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">受理时间：</label>
            <div class="col-sm-8">
                <div class="form-control-static"
                     th:text="${#dates.format(jubaoCase.acceptTime, 'yyyy-MM-dd HH:mm:ss')}"></div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">办结时间：</label>
            <div class="col-sm-8">
                <div class="form-control-static"
                     th:text="${#dates.format(jubaoCase.finishTime, 'yyyy-MM-dd HH:mm:ss')}"></div>
            </div>
        </div>
    </form>
</div>
<th:block th:insert="~{include :: footer}"/>
</body>
</html>