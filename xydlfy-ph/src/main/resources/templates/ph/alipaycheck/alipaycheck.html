<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:insert="~{include :: header('支付宝对账列表')}"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>日期：</label>
                            <input type="text" name="id"/>
                        </li>
                        <li>
                            <label>支付平账？：</label>
                            <select name="payBalanced" th:with="type=${@dict.getType('yes_no')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>退款平账？：</label>
                            <select name="refundBalanced" th:with="type=${@dict.getType('yes_no')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:alipaycheck:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var payBalancedDatas = [[${@dict.getType('yes_no')}]];
    var refundBalancedDatas = [[${@dict.getType('yes_no')}]];
    var syncAlipayBillFlag = [[${@permission.hasPermi('ph:alipaycheck:syncAlipayBill')}]];
    var downloadAlipayBillFlag = [[${@permission.hasPermi('ph:alipaycheck:downloadAlipayBill')}]];
    var syncHisBillFlag = [[${@permission.hasPermi('ph:alipaycheck:syncHisBill')}]];
    var downloadHisBillFlag = [[${@permission.hasPermi('ph:alipaycheck:downloadHisBill')}]];
    var showDiffOrdersFlag = [[${@permission.hasPermi('ph:alipaycheck:showDiffOrders')}]];
    var prefix = ctx + "ph/alipaycheck";

    $(function () {
        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            modalName: "支付宝对账",
            onEditableSave: updateCell,
            sortName: "id",
            sortOrder: "desc",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: '日期'
                },
                {
                    field: 'alipayPayAmount',
                    title: '支付宝支付总金额'
                },
                {
                    field: 'hisPayAmount',
                    title: 'HIS充值总金额'
                },
                {
                    field: 'diffPayAmount',
                    title: '支付差额'
                },
                {
                    field: 'payBalanced',
                    title: '支付平账？',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(payBalancedDatas, value);
                    }
                },
                {
                    field: 'alipayRefundAmount',
                    title: '支付宝退款总金额'
                },
                {
                    field: 'hisRefundAmount',
                    title: 'HIS退款总金额'
                },
                {
                    field: 'diffRefundAmount',
                    title: '退款差额'
                },
                {
                    field: 'refundBalanced',
                    title: '退款平账？',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(refundBalancedDatas, value);
                    }
                },
                {
                    field: 'alipayNetIn',
                    title: '支付宝净流入'
                },
                {
                    field: 'hisNetIn',
                    title: 'HIS净流入'
                },
                {
                    field: 'remark',
                    title: '备注'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + syncAlipayBillFlag + '" href="javascript:void(0)" onclick="syncAlipayBill(\'' + row.id + '\')"><i class="fa fa-refresh"></i>同步支付宝账单</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + downloadAlipayBillFlag + '" href="javascript:void(0)" onclick="downloadAlipayBill(\'' + row.id + '\')"><i class="fa fa-download"></i>下载支付宝账单</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + syncHisBillFlag + '" href="javascript:void(0)" onclick="syncHisBill(\'' + row.id + '\')"><i class="fa fa-refresh"></i>同步His账单</a> ');
                        actions.push('<a class="btn btn-success btn-xs ' + downloadHisBillFlag + '" href="javascript:void(0)" onclick="downloadHisBill(\'' + row.id + '\')"><i class="fa fa-download"></i>下载HIS账单</a> ');
                        if (row.payBalanced === 0 || row.refundBalanced === 0) {
                            actions.push('<a class="btn btn-success btn-xs ' + showDiffOrdersFlag + '" href="javascript:void(0)" onclick="showDiffOrders(\'' + row.id + '\')"><i class="fa fa-eye"></i>显示错单</a> ');
                        }
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function syncAlipayBill(id) {
        $.operate.post(prefix + '/syncAlipayBill/' + id, {});
    }

    function downloadAlipayBill(id) {
        window.open(prefix + '/downloadAlipayBill/' + id, "_blank");
    }

    function syncHisBill(id) {
        $.operate.post(prefix + '/syncHisBill/' + id, {});
    }

    function downloadHisBill(id) {
        window.open(prefix + '/downloadHisBill/' + id, "_blank");
    }

    function showDiffOrders(id) {
        var url = prefix + '/showDiffOrders/' + id;
        $.modal.openTab("错单 - " + id, url);
    }

    function updateCell(field, row) {
        var form = {};
        form['id'] = row['id'];
        form[field] = row[field];
        $.operate.post(prefix + '/edit', form);
    }
</script>
</body>
</html>
