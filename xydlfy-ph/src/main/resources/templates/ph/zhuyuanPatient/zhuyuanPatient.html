<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('住院患者列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>身份证号：</label>
                                <input type="text" name="idCardNo"/>
                            </li>
                            <li>
                                <label>患者编号：</label>
                                <input type="text" name="patientNo"/>
                            </li>
                            <li>
                                <label>住院号：</label>
                                <input type="text" name="zhuyuanNo"/>
                            </li>

                            <li>
                                <label>住院号码：</label>
                                <input type="text" name="admissionNumber"/>
                            </li>
                            <li>
                                <label>姓名：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>性别：</label>
                                <input type="text" name="gender"/>
                            </li>
                            <li>
                                <label>科室编码：</label>
                                <input type="text" name="departmentCode"/>
                            </li>
                            <li>
                                <label>科室名称：</label>
                                <input type="text" name="departmentName"/>
                            </li>
                            <li>
                                <label>床号：</label>
                                <input type="text" name="bedNo"/>
                            </li>
                            <li>
                                <label>住院时间：</label>
                                <input type="text" class="time-input" placeholder="请选择住院时间" name="comeTime"/>
                            </li>
                            <li>
                                <label>离院时间：</label>
                                <input type="text" class="time-input" placeholder="请选择离院时间" name="leaveTime"/>
                            </li>
                            <li>
                                <label>状态：</label>
                                <input type="text" name="state"/>
                            </li>
                            <li>
                                <label>openId：</label>
                                <input type="text" name="openId"/>
                            </li>
                            <li>
                                <label>unionId：</label>
                                <input type="text" name="unionId"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ph:zhuyuanPatient:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="ph:zhuyuanPatient:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="ph:zhuyuanPatient:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:zhuyuanPatient:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('ph:zhuyuanPatient:edit')}]];
        var removeFlag = [[${@permission.hasPermi('ph:zhuyuanPatient:remove')}]];
        var genderDatas = [[${@dict.getType('sys_user_sex')}]];
        var prefix = ctx + "ph/zhuyuanPatient";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "住院患者",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: '',
                    visible: false
                },
                {
                    field: 'idCardNo',
                    title: '身份证号'
                },
                {
                    field: 'patientNo',
                    title: '患者编号'
                },
                {
                    field: 'zhuyuanNo',
                    title: '住院号'
                },
                {
                    field: 'name',
                    title: '姓名'
                },
                {
                    field: 'gender',
                    title: '性别',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(genderDatas, value);
                    }
                },
                {
                    field: 'departmentCode',
                    title: '科室编码'
                },
                {
                    field: 'departmentName',
                    title: '科室名称'
                },
                {
                    field: 'bedNo',
                    title: '床号'
                },
                {
                    field: 'comeTime',
                    title: '住院时间'
                },
                {
                    field: 'leaveTime',
                    title: '离院时间'
                },
                {
                    field: 'state',
                    title: '状态'
                },
                {
                    field: 'openId',
                    title: ''
                },
                {
                    field: 'unionId',
                    title: ''
                },
                {
                    field: 'admissionNumber',
                    title: '住院号码'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>