<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改住院患者')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-zhuyuanPatient-edit" th:object="${zhuyuanPatient}">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">身份证号：</label>
                <div class="col-sm-8">
                    <input name="idCardNo" th:field="*{idCardNo}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">患者编号：</label>
                <div class="col-sm-8">
                    <input name="patientNo" th:field="*{patientNo}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">住院号：</label>
                <div class="col-sm-8">
                    <input name="zhuyuanNo" th:field="*{zhuyuanNo}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">姓名：</label>
                <div class="col-sm-8">
                    <input name="name" th:field="*{name}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">性别：</label>
                <div class="col-sm-8">
                    <input name="gender" th:field="*{gender}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">科室编码：</label>
                <div class="col-sm-8">
                    <input name="departmentCode" th:field="*{departmentCode}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">科室名称：</label>
                <div class="col-sm-8">
                    <input name="departmentName" th:field="*{departmentName}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">床号：</label>
                <div class="col-sm-8">
                    <input name="bedNo" th:field="*{bedNo}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">住院时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="comeTime" th:value="${#dates.format(zhuyuanPatient.comeTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">离院时间：</label>
                <div class="col-sm-8">
                    <div class="input-group date">
                        <input name="leaveTime" th:value="${#dates.format(zhuyuanPatient.leaveTime, 'yyyy-MM-dd')}" class="form-control" placeholder="yyyy-MM-dd" type="text">
                        <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">状态：</label>
                <div class="col-sm-8">
                    <input name="state" th:field="*{state}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">openid：</label>
                <div class="col-sm-8">
                    <input name="openId" th:field="*{openId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">unionid：</label>
                <div class="col-sm-8">
                    <input name="unionId" th:field="*{unionId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">住院号码：</label>
                <div class="col-sm-8">
                    <input name="admissionNumber" th:field="*{admissionNumber}" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "ph/zhuyuanPatient";
        $("#form-zhuyuanPatient-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-zhuyuanPatient-edit').serialize());
            }
        }

        $("input[name='comeTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });

        $("input[name='leaveTime']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true
        });
    </script>
</body>
</html>