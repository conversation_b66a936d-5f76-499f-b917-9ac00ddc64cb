<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('修改问题类型')}"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-jubaoCaseCategory-edit" th:object="${jubaoCaseCategory}">
        <input name="id" th:field="*{id}" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">父类型：</label>
            <div class="col-sm-8">
                <div class="input-group">
                    <input id="treeId" name="parentId" type="hidden" th:field="*{parentId}"/>
                    <input class="form-control" type="text" onclick="selectJubaoCaseCategoryTree()" id="treeName"
                           readonly="true" th:field="*{parentName}" required>
                    <span class="input-group-addon"><i class="fa fa-search"></i></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">名称：</label>
            <div class="col-sm-8">
                <input name="name" th:field="*{name}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">序号：</label>
            <div class="col-sm-8">
                <input name="sortNo" th:field="*{sortNo}" class="form-control" type="text" required>
            </div>
        </div>
    </form>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    const prefix = ctx + "ph/jubaoCaseCategory";
    $("#form-jubaoCaseCategory-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/edit", $('#form-jubaoCaseCategory-edit').serialize());
        }
    }

    /*问题类型-编辑-选择父问题类型树*/
    function selectJubaoCaseCategoryTree() {
        const options = {
            title: '问题类型选择',
            width: "380",
            url: prefix + "/selectJubaoCaseCategoryTree/" + $("#treeId").val(),
            callBack: doSubmit
        };
        $.modal.openOptions(options);
    }

    function doSubmit(index, layero) {
        const body = $.modal.getChildFrame(index);
        $("#treeId").val(body.find('#treeId').val() || 0);
        $("#treeName").val(body.find('#treeName').val());
        $.modal.close(index);
    }
</script>
</body>
</html>