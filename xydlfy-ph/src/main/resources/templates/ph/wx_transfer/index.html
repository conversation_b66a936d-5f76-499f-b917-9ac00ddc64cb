<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('微信转账')}"/>
    <style>
        th,
        td {
          font-weight: bold !important;
        }
        #formId label {
          width: auto;
          margin-right: 10px;
        }
        #bootstrap-table tbody td {
          font-family: Consolas, Monaco, "Courier New", monospace;
          font-weight: 500;
          font-size: 12px;
        }
        .amount-cell:hover {
          cursor: pointer;
        }
        /* 图表容器样式 */
        #chartContainer {
          width: 100%;
          height: 400px;
          margin-bottom: 20px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label for="outBillNo">商户转账单号</label>
                            <input type="text" id="outBillNo" name="outBillNo"/>
                        </li>
                        <li>
                            <label for="transferBillNo">微信转账单号</label>
                            <input
                                    type="text"
                                    id="transferBillNo"
                                    name="transferBillNo"
                            />
                        </li>
                        <li>
                            <label for="patientIdCardNo">身份证号</label>
                            <input
                                    type="text"
                                    id="patientIdCardNo"
                                    name="patientIdCardNo"
                            />
                        </li>
                        <li>
                            <label for="patientCardNo">就诊卡号</label>
                            <input type="text" id="patientCardNo" name="patientCardNo"/>
                        </li>
                        <li>
                            <label for="transferState">转账状态</label>
                            <select id="transferState" name="transferState">
                                <option value="">全部</option>
                                <option value="SUCCESS">成功</option>
                                <option value="FAILED">失败</option>
                            </select>
                        </li>
                        <li>
                            <a
                                    class="btn btn-primary btn-rounded btn-sm"
                                    onclick="$.table.search()"
                            >
                                <i class="fa fa-search"></i>&nbsp;搜索</a
                            >
                            <a
                                    class="btn btn-warning btn-rounded btn-sm"
                                    onclick="$.form.reset()"
                            >
                                <i class="fa fa-refresh"></i>&nbsp;重置</a
                            >
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table">
            <table
                    id="bootstrap-table"
                    data-mobile-responsive="true"
                    class="table-bordered table-striped"
            ></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    /*<![CDATA[*/
    var requestWxTransferFlag = /*[[${@permission.hasPermi('ph:wx_transfer:requestWxTransfer')}]]*/ false;
    var refreshWxTransferFlag = /*[[${@permission.hasPermi('ph:wx_transfer:refreshWxTransfer')}]]*/ false;
    /*]]>*/
    var prefix = ctx + "ph/wx_transfer";
    var tableOptions = {
      url: prefix + "/page",
      modalName: "余额转账",
      showSearch: false,
      showColumns: false,
      showToggle: false,
      columns: [
        [
          {
            field: "createTime",
            title: "申请时间",
            align: "center",
            valign: "middle",
            rowspan: 2,
          },
          {
            field: "amount",
            title: "转账金额",
            align: "right",
            valign: "middle",
            rowspan: 2,
          },
          {
            title: "患者信息",
            align: "center",
            valign: "middle",
            colspan: 4,
          },
          {
            title: "HIS交易信息",
            align: "center",
            valign: "middle",
            colspan: 3,
          },
          {
            title: "微信转账信息",
            align: "center",
            valign: "middle",
            colspan: 6,
          },
          {
            title: "操作",
            align: "center",
            valign: "middle",
            rowspan: 2,
            formatter: function (value, row, index) {
              var actions = [];
              if (!["SUCCESS", "FAILED", "BANK_FAIL"].includes(row.transferState)) {
                actions.push('<a class="btn btn-success btn-xs ' + requestWxTransferFlag + '" href="javascript:void(0)" onclick="requestWxTransfer(\'' + row.id + '\')"><i class="fa fa-paper-plane"></i>发起转账</a> ');
              }
              actions.push('<a class="btn btn-success btn-xs ' + refreshWxTransferFlag + '" href="javascript:void(0)" onclick="refreshWxTransfer(\'' + row.id + '\')"><i class="fa fa-refresh"></i>刷新状态</a> ');
              return actions.join('');
            }
          }
        ],
        [
          {
            field: "patientIdCardNo",
            title: "身份证号",
            align: "right",
            valign: "middle",
          },
          {
            field: "patientCardNo",
            title: "就诊卡号",
            align: "right",
            valign: "middle"
          },
          {
            field: "patientName",
            title: "姓名",
            align: "right",
            valign: "middle"
          },
          {
            field: "patientMobile",
            title: "手机号",
            align: "right",
            valign: "middle"
          },
          {
            field: "hisRefundTime",
            title: "交易时间",
            align: "right",
            valign: "middle"
          },
          {
            field: "hisRefundSuccess",
            title: "交易状态",
            align: "right",
            valign: "middle",
            formatter: function (value, row, index) {
              if (value === true) {
                return "成功";
              } else {
                return row.hisRefundFailReason || '';
              }
            },
          },
          {
            field: "hisOrderNo",
            title: "流水号",
            align: "right",
            valign: "middle"
          },
          {
            field: "outBillNo",
            title: "商户转账单号",
            align: "right",
            valign: "middle"
          },
          {
            field: "transferBillNo",
            title: "微信转账单号",
            align: "right",
            valign: "middle"
          },
          {
            field: "transferCreateTime",
            title: "转账开始时间",
            align: "right",
            valign: "middle"
          },
          {
            field: "transferUpdateTime",
            title: "最后更新时间",
            align: "right",
            valign: "middle"
          },
          {
            field: "transferState",
            title: "转账状态",
            align: "right",
            valign: "middle"
          },
          {
            field: "transferFailReason",
            title: "失败原因",
            align: "right",
            valign: "middle"
          }
        ],
      ],
    };
    $(function () {
      $.table.init(tableOptions);
    });

    function requestWxTransfer(id) {
      $.modal.confirm("确认要发起转账吗？", function() {
        $.operate.post(prefix + "/requestWxTransfer/" + id);
      });
    }

    function refreshWxTransfer(id) {
       $.operate.post(prefix + "/refreshWxTransfer/" + id);
    }
</script>
</body>
</html>
