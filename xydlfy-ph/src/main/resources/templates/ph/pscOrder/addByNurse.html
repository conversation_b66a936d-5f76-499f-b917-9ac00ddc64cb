<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('新增订单')}"/>
    <style>
        body {
            zoom: 1.2;
        }

        .exam-item-wrapper {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
        }
    </style>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-pscOrder-add">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">患者索引号：</label>
            <div class="col-sm-8">
                <input id="patientId" name="patientId" th:value="${session.pscOrder.patientId}"
                       class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">患者住院号：</label>
            <div class="col-sm-8">
                <input id="admissionNumber" name="admissionNumber" th:value="${session.pscOrder.admissionNumber}"
                       class="form-control" type="text">
            </div>
        </div>
        <div class="form-group patient-field">
            <label class="col-sm-3 control-label is-required">患者科室：</label>
            <div class="col-sm-8">
                <input id="patientDepartment" name="patientDepartment" th:value="${session.pscOrder.patientDepartment}"
                       class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group patient-field">
            <label class="col-sm-3 control-label">患者床号：</label>
            <div class="col-sm-8">
                <input id="patientBedNumber" name="patientBedNumber" th:value="${session.pscOrder.patientBedNumber}"
                       class="form-control" type="text">
            </div>
        </div>
        <div class="form-group patient-field">
            <label class="col-sm-3 control-label is-required">患者姓名：</label>
            <div class="col-sm-8">
                <input id="patientName" name="patientName" th:value="${session.pscOrder.patientName}"
                       class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group patient-field">
            <label class="col-sm-3 control-label is-required">患者手机号：</label>
            <div class="col-sm-8">
                <input id="patientMobile" name="patientMobile" th:value="${session.pscOrder.patientMobile}"
                       class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group patient-field">
            <label class="col-sm-3 control-label is-required">患者身份证号：</label>
            <div class="col-sm-8">
                <input id="patientIdCardNo" name="patientIdCardNo" th:value="${session.pscOrder.patientIdCardNo}"
                       class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">服务类型：</label>
            <div class="col-sm-8">
                <select name="serviceType" class="form-control m-b" th:with="type=${@dict.getType('psc_service_type')}"
                        required>
                    <option value="">请选择服务类型</option>
                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">检查项目：</label>
            <div class="col-sm-8 exam-item-wrapper" th:with="type=${@dict.getType('psc_exam_item')}">
                <label th:each="dict : ${type}" class="check-box" style="display: block">
                    <input name="examItems" type="checkbox" th:value="${dict.dictValue}" th:text="${dict.dictLabel}"
                           required>
                </label>
            </div>
        </div>
    </form>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/pscOrder"
    $("#form-pscOrder-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/addByNurse", $('#form-pscOrder-add').serialize());
        }
    }
</script>
</body>
</html>
