<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:insert="~{include :: header('订单列表')}"/>
    <style>
        #bootstrap-table td:first-of-type a.btn {
            display: inline-block;
            margin: 5px !important;
        }

        .patient-name {
            font-weight: bold;
            text-decoration: underline;
            cursor: pointer;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>住院号：</label>
                            <input type="text" name="admissionNumber"/>
                        </li>
                        <li>
                            <label>患者姓名：</label>
                            <input type="text" id="patientName" name="patientName"/>
                        </li>
                        <li>
                            <label>科室：</label>
                            <select name="patientDepartment" th:with="type=${@dict.getType('psc_order_status')}">
                                <option value="">所有</option>
                                <option th:each="department : ${departments}" th:text="${department}"
                                        th:value="${department}"></option>
                            </select>
                        </li>
                        <li>
                            <label>陪检员工号：</label>
                            <input type="text" name="carerEmployeeNo"/>
                        </li>
                        <li>
                            <label>状态：</label>
                            <select name="status" th:with="type=${@dict.getType('psc_order_status')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li class="select-time">
                            <label>创建时间：</label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                   name="params[beginCreateTime]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                   name="params[endCreateTime]"/>
                        </li>
                        <li>
                            <label>只显示病友服务中心的订单：</label>
                            <select name="params[filterCenterOrder]">
                                <option value="">所有</option>
                                <option value="true">是</option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ph:pscOrder:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()"
               shiro:hasPermission="ph:pscOrder:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="ph:pscOrder:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-danger multiple disabled" onclick="assign()"
               shiro:hasPermission="ph:pscOrder:assign">
                <i class="fa fa-share"></i> 派单
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:pscOrder:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('ph:pscOrder:edit')}]];
    var removeFlag = [[${@permission.hasPermi('ph:pscOrder:remove')}]];
    var assignFlag = [[${@permission.hasPermi('ph:pscOrder:assign')}]];
    var patientSexDatas = [[${@dict.getType('sys_user_sex')}]];
    var patientNationDatas = [[${@dict.getType('minzu')}]];
    var serviceTypeDatas = [[${@dict.getType('psc_service_type')}]];
    var examItemDatas = [[${@dict.getType('psc_exam_item')}]];
    var statusDatas = [[${@dict.getType('psc_order_status')}]];
    var scoreDatas = [[${@dict.getType('psc_order_score')}]];
    var prefix = ctx + "ph/pscOrder";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "订单",
            pageSize: 25,
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'status',
                    title: '状态',
                    width: '100px',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field: 'createTime',
                    title: '下单时间',
                    width: '100px',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return value.split(' ').join('<br>')
                    }
                },
                {
                    field: 'endTime',
                    title: '结束时间',
                    width: '100px',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return value ? value.split(' ').join('<br>') : ''
                    }
                },
                {
                    field: 'serviceType',
                    title: '服务类型',
                    width: '100px',
                    formatter: function (value, row, index) {
                        const text = $.table.selectDictLabel(serviceTypeDatas, value)
                        return `<span style="font-weight: bold;">${text}</span>`;
                    }
                },
                {
                    field: 'examItems',
                    title: '检查项目',
                    width: '120px',
                    formatter: function (value, row, index) {
                        const examItemsByValue = {};
                        examItemDatas.forEach(it => {
                            examItemsByValue[it.dictValue] = it.dictLabel.split('-')[1]
                        });
                        const values = value.split(',');
                        return values.map(it => examItemsByValue[it]).join('、');
                    }
                },
                {
                    field: 'patientId',
                    title: '患者',
                    width: '210px',
                    align: 'left',
                    formatter: function (value, row, index) {
                        let html = [
                            `姓名：<span class="patient-name" onclick="onClickPatientName('${row.patientName}');">${row.patientName}</span>`,
                            `住院号：${row.admissionNumber}`
                        ]
                        return html.join('<br>')
                    }
                },
                {
                    field: 'patientDepartment',
                    title: '科室（床号）',
                    width: '210px',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return `<span style="color: red;">${row.patientDepartment}<br/>${row.patientBedNumber}</span>`
                    }
                },
                {
                    field: 'nurseName',
                    title: '护士',
                    width: '210px',
                    align: 'center'
                },
                {
                    field: 'carerName',
                    title: '陪检员',
                    width: '210px',
                    align: 'center'
                },
                {
                    field: 'score',
                    title: '评价',
                    width: '100px',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(scoreDatas, value);
                    }
                }
            ]
        };
        $.table.init(options);
    });

    function onClickPatientName(patientName) {
        $('#patientName').val(patientName);
        $.table.search();
    }

    function assign() {
        const rows = $('#bootstrap-table').bootstrapTable('getSelections')
        if (rows.length === 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }

        const validRows = rows.filter(it => it.status === 0 || it.status === 1)
        if (validRows.length === 0) {
            $.modal.alertWarning("只有状态为【待分配陪检】和【待陪检接单】的订单可以派单，选中记录均不满足派单条件");
            return;
        }

        $.modal.confirm("此操作将为状态为【待分配陪检】和【待陪检接单】的订单分配陪检人员，其它状态的订单将被忽略。确定要继续操作吗？", function () {
            $.modal.open("派单", prefix + "/assign?ids=" + validRows.map(it => it.id).join(','));
        });
    }
</script>
</body>
</html>
