<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('文章预览')}"/>
    <style>
        .article {
            padding: 10px;
        }

        img, video {
            max-width: 100% !important;
            height: auto !important;
        }

        .publish-time {
            margin-right: 5px;
            font-size: small;
            color: #B9B8BD;
        }

        .content {
            padding: 0 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body th:object="${wikiArticle}" class="article">
<h3 class="title text-center" th:text="*{title}"></h3>
<p class="text-center"><span th:text="${#dates.format(article.updateTime, 'yyyy-MM-dd HH:mm')}"></span></p>
<div class="content" th:utext="*{content}"></div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    const title = /*[[${wikiArticle.title}]]*/ 'default';
    document.title = title;
</script>
</body>
</html>