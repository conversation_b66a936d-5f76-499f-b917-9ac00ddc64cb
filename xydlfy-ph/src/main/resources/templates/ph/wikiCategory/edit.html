<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:insert="~{include :: header('修改知识分类')}"/>
    <th:block th:insert="~{include :: bootstrap-fileinput-css}"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-wikiCategory-edit" th:object="${wikiCategory}">
        <input name="id" th:field="*{id}" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">名称：</label>
            <div class="col-sm-8">
                <input name="name" th:field="*{name}" class="form-control" type="text" required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">图标：</label>
            <div class="col-sm-8">
                <input type="hidden" name="icon" th:field="*{icon}">
                <div class="file-loading">
                    <input class="form-control file-upload" id="icon" name="file" type="file">
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">排序号：</label>
            <div class="col-sm-8">
                <input name="sortNo" th:field="*{sortNo}" class="form-control" type="text" required>
            </div>
        </div>
    </form>
</div>
<th:block th:insert="~{include :: footer}"/>
<th:block th:insert="~{include :: bootstrap-fileinput-js}"/>
<script th:inline="javascript">
    var prefix = ctx + "ph/wikiCategory";
    $("#form-wikiCategory-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/edit", $('#form-wikiCategory-edit').serialize());
        }
    }

    $(".file-upload").each(function (i) {
        var val = $("input[name='" + this.id + "']").val()
        $(this).fileinput({
            'uploadUrl': ctx + 'common/upload',
            initialPreviewAsData: true,
            initialPreview: val ? [val] : [],
            maxFileCount: 1,
            autoReplace: true,
            allowedFileTypes: ['image'],
            showUpload: false,
            browseOnZoneClick: true,
        }).on('filebatchselected', function (event, files) {
            $(this).fileinput("upload");
        }).on('fileuploaded', function (event, data, previewId, index) {
            $("input[name='" + event.currentTarget.id + "']").val(data.response.url)
        }).on('fileremoved', function (event, id, index) {
            $("input[name='" + event.currentTarget.id + "']").val('')
        })
        $(this).fileinput('_initFileActions');
    });
</script>
</body>
</html>