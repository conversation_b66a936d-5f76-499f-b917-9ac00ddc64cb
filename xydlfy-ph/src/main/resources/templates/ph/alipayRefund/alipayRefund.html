<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:insert="~{include :: header('支付宝退款记录列表')}"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li class="select-time">
                            <label>下单时间：</label>
                            <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                                   name="params[beginCreateTime]"/>
                            <span>-</span>
                            <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                                   name="params[endCreateTime]"/>
                        </li>
                        <li>
                            <label>患者索引号：</label>
                            <input type="text" name="patientId"/>
                        </li>
                        <li>
                            <label>就诊卡号：</label>
                            <input type="text" name="jzCardNo"/>
                        </li>
                        <li>
                            <label>掌医充值订单号：</label>
                            <input type="text" name="outTradeNo"/>
                        </li>
                        <li>
                            <label>支付宝充值订单号：</label>
                            <input type="text" name="tradeNo"/>
                        </li>
                        <li>
                            <label>掌医退款订单号：</label>
                            <input type="text" name="outRefundNo"/>
                        </li>
                        <li>
                            <label>HIS交易单号：</label>
                            <input type="text" name="hisTradeNo"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:alipayRefund:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
    var manualRefundStateDatas = [[${@dict.getType('manual_state')}]];
    var queryAlipayOrderFlag = [[${@permission.hasPermi('ph:alipayRefund:queryAlipayOrder')}]];
    var requestAlipayRefundFlag = [[${@permission.hasPermi('ph:alipayRefund:requestAlipayRefund')}]];
    var approveAlipayRefundFlag = [[${@permission.hasPermi('ph:alipayRefund:approveAlipayRefund')}]];
    var prefix = ctx + "ph/alipayRefund";

    $(function () {
        var options = {
            url: prefix + "/list",
            exportUrl: prefix + "/export",
            modalName: "支付宝退款记录",
            columns: [{
                checkbox: true
            },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'createTime',
                    title: '下单时间'
                },
                {
                    field: 'patientId',
                    title: '患者索引号'
                },
                {
                    field: 'jzCardNo',
                    title: '就诊卡号'
                },
                {
                    field: 'totalAmount',
                    title: '总金额',
                    formatter: function (value, row, index) {
                        return (Number(value) / 100).toFixed(2);
                    }
                },
                {
                    field: 'amount',
                    title: '订单金额',
                    formatter: function (value, row, index) {
                        return (Number(value) / 100).toFixed(2);
                    }
                },
                {
                    field: 'outTradeNo',
                    title: '掌医订单号'
                },
                {
                    field: 'tradeNo',
                    title: '支付宝订单号'
                },
                {
                    field: 'outRefundNo',
                    title: '退款单号'
                },
                {
                    field: 'hisTradeStatus',
                    title: 'HIS交易状态'
                },
                {
                    field: 'hisTradeNo',
                    title: 'HIS交易单号'
                },
                {
                    field: 'alipayTradeStatus',
                    title: '支付宝交易状态',
                    formatter: function (value, row, index) {
                        return value === 'REFUND_SUCCESS' ? '退款成功' : value
                    }
                },
                {
                    field: 'alipayTradeTime',
                    title: '支付宝交易时间'
                },
                {
                    field: 'manualRefundState',
                    title: '人工退款状态',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(manualRefundStateDatas, value);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + queryAlipayOrderFlag + '" href="javascript:void(0)" onclick="queryAlipayOrder(\'' + row.id + '\')"><i class="fa fa-refresh"></i>查询支付宝订单状态</a> ');
                        if (row['hisTradeStatus'] === '退款成功' && row['alipayTradeStatus'] !== 'REFUND_SUCCESS' && row['manualRefundState'] == 0) {
                            actions.push('<a class="btn btn-success btn-xs ' + requestAlipayRefundFlag + '" href="javascript:void(0)" onclick="requestAlipayRefund(\'' + row.id + '\')"><i class="fa fa-paper-plane-o"></i>申请支付宝退款</a> ');
                        }
                        if (row['hisTradeStatus'] === '退款成功' && row['alipayTradeStatus'] !== 'REFUND_SUCCESS' && row['manualRefundState'] == 1) {
                            actions.push('<a class="btn btn-success btn-xs ' + approveAlipayRefundFlag + '" href="javascript:void(0)" onclick="approveAlipayRefund(\'' + row.id + '\')"><i class="fa fa-check"></i>放行支付宝退款</a> ');
                        }
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function queryAlipayOrder(id) {
        $.operate.get(prefix + '/queryAlipayOrder/' + id, function (resp) {
            layer.open({
                type: 1,
                skin: 'layui-layer-rim',
                area: ['80%', '80%'],
                title: '查询订单状态',
                content: '<pre class="layui-code" lay-title="JSON" lay-encode="true">' + JSON.stringify(resp, null, 4) + '</pre>',
                success: function () {
                    layui.use('code', function () {
                        layui.code();
                    });
                }
            });
        });
    }

    function requestAlipayRefund(id) {
        $.operate.post(prefix + '/requestAlipayRefund/' + id, {})
    }

    function approveAlipayRefund(id) {
        $.operate.post(prefix + '/approveAlipayRefund/' + id, {})
    }
</script>
</body>
</html>
