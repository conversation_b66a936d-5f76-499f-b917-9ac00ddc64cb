<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('病历邮寄列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>患者号：</label>
                                <input type="text" name="patientNo"/>
                            </li>
                            <li>
                                <label>就诊卡号：</label>
                                <input type="text" name="jzCardNo"/>
                            </li>
                            <li>
                                <label>住院号：</label>
                                <input type="text" name="zhuyuanNo"/>
                            </li>
                            <li>
                                <label>患者姓名：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>患者手机号：</label>
                                <input type="text" name="mobile"/>
                            </li>
                            <li>
                                <label>身份证号：</label>
                                <input type="text" name="idCardNo"/>
                            </li>
                            <li>
                                <label>患者openid：</label>
                                <input type="text" name="openid"/>
                            </li>
                            <li>
                                <label>状态：</label>
                                <select name="status" th:with="type=${@dict.getType('medical_record_mailing_status')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>掌医支付单号：</label>
                                <input type="text" name="zyPayNo"/>
                            </li>
                            <li>
                                <label>微信支付单号：</label>
                                <input type="text" name="wxPayNo"/>
                            </li>
                            <li>
                                <label>快递单号：</label>
                                <input type="text" name="courierNumber"/>
                            </li>
                            <li>
                                <label>操作员名称：</label>
                                <input type="text" name="operatorName"/>
                            </li>
                            <li class="select-time">
                                <label>创建时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginCreateTime]"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endCreateTime]"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ph:medical_record_mailing:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="ph:medical_record_mailing:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="ph:medical_record_mailing:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:medical_record_mailing:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('ph:medical_record_mailing:edit')}]];
        var removeFlag = [[${@permission.hasPermi('ph:medical_record_mailing:remove')}]];
        var statusDatas = [[${@dict.getType('medical_record_mailing_status')}]];
        var prefix = ctx + "ph/medical_record_mailing";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "病历邮寄",
                columns: [{
                    checkbox: false
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'patientNo',
                    title: '患者号'
                },
                {
                    field: 'jzCardNo',
                    title: '就诊卡号'
                },
                {
                    field: 'zhuyuanNo',
                    title: '住院号'
                },
                {
                    field: 'name',
                    title: '患者姓名'
                },
                {
                    field: 'mobile',
                    title: '患者手机号'
                },
                {
                    field: 'idCardNo',
                    title: '身份证号'
                },
                {
                    field: 'address',
                    title: '收件地址'
                },
                {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                       return $.table.selectDictLabel(statusDatas, value);
                    }
                },
                {
                    field: 'medicalRecordPages',
                    title: '病历页数'
                },
                {
                    field: 'copyingFee',
                    title: '复印费用',
                    formatter: function(value, row, index) {
                        return value / 100;
                    }
                },
                {
                    field: 'expressFee',
                    title: '快递费用',
                    formatter: function(value, row, index) {
                        return value / 100;
                    }
                },
                {
                    field: 'zyPayNo',
                    title: '掌医支付单号'
                },
                {
                    field: 'wxPayNo',
                    title: '微信支付单号'
                },
                {
                    field: 'courierNumber',
                    title: '快递单号'
                },
                {
                    field: 'shutdownReason',
                    title: '订单关闭原因'
                },
                {
                    field: 'operatorName',
                    title: '操作员名称'
                },
                {
                    field: 'createTime',
                    title: '创建时间'
                },
                {
                    field: 'updateTime',
                    title: '更新时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>处理</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        /**
         * 确认病例信息
         * @param id
         */
        function confirm(id) {
            $.operate.post(prefix + '/syncAlipayBill/' + id, {});
        }
    </script>
</body>
</html>