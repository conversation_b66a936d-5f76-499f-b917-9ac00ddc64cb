<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
  <th:block th:insert="~{include :: header('退款记录列表')}"/>
</head>
<body class="gray-bg">
<div class="container-div">
  <div class="row">
    <div class="col-sm-12 search-collapse">
      <form id="formId">
        <div class="select-list">
          <ul>
            <li class="select-time">
              <label>下单时间：</label>
              <input type="text" class="time-input" id="startTime" placeholder="开始时间"
                     name="params[beginCreateTime]"/>
              <span>-</span>
              <input type="text" class="time-input" id="endTime" placeholder="结束时间"
                     name="params[endCreateTime]"/>
            </li>
            <li>
              <label>患者索引号：</label>
              <input type="text" name="patientNo"/>
            </li>
            <li>
              <label>就诊卡号：</label>
              <input type="text" name="jzCardNo"/>
            </li>
            <li>
              <label>住院号：</label>
              <input type="text" name="zhuyuanNo"/>
            </li>
            <li>
              <label>掌医充值订单号：</label>
              <input type="text" name="zyPayNo"/>
            </li>
            <li>
              <label>微信充值订单号：</label>
              <input type="text" name="wxPayNo"/>
            </li>
            <li>
              <label>掌医退款订单号：</label>
              <input type="text" name="zyRefundNo"/>
            </li>
            <li>
              <label>微信退款订单号：</label>
              <input type="text" name="wxRefundNo"/>
            </li>
            <li>
              <label>人工退款状态：</label>
              <select name="manualRefundState" th:with="type=${@dict.getType('manual_state')}">
                <option value="">所有</option>
                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                        th:value="${dict.dictValue}"></option>
              </select>
            </li>
            <li>
              <label>只显示错单：</label>
              <select name="params[filterErrorOrder]">
                <option value="">所有</option>
                <option value="true">是</option>
              </select>
            </li>
            <li>
              <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                  class="fa fa-search"></i>&nbsp;搜索</a>
              <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                  class="fa fa-refresh"></i>&nbsp;重置</a>
            </li>
          </ul>
        </div>
      </form>
    </div>

    <div class="btn-group-sm" id="toolbar" role="group">
      <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="ph:refund:export">
        <i class="fa fa-download"></i> 导出
      </a>
    </div>
    <div class="col-sm-12 select-table table-striped">
      <table id="bootstrap-table"></table>
    </div>
  </div>
</div>
<th:block th:insert="~{include :: footer}"/>
<script th:inline="javascript">
  var queryWXOrderFlag = [[${@permission.hasPermi('ph:refund:queryWXOrder')}]];
  var requestWXRefundFlag = [[${@permission.hasPermi('ph:refund:requestWXRefund')}]];
  var approveWXRefundFlag = [[${@permission.hasPermi('ph:refund:approveWXRefund')}]];
  var typeDatas = [[${@dict.getType('mz_zy')}]];
  var manualRefundStateDatas = [[${@dict.getType('manual_state')}]];
  var prefix = ctx + "ph/refund";

  $(function () {
    var options = {
      url: prefix + "/list",
      createUrl: prefix + "/add",
      updateUrl: prefix + "/edit/{id}",
      removeUrl: prefix + "/remove",
      exportUrl: prefix + "/export",
      modalName: "退款记录",
      columns: [{
        checkbox: true
      },
        {
          field: 'id',
          title: 'ID',
          visible: false
        },
        {
          field: 'createTime',
          title: '下单时间'
        },
        {
          field: 'type',
          title: '类型',
          formatter: function (value, row, index) {
            return $.table.selectDictLabel(typeDatas, value);
          }
        },
        {
          field: 'openid',
          title: 'openid',
          visible: false
        },
        {
          field: 'patientNo',
          title: '患者索引号'
        },
        {
          field: 'jzCardNo',
          title: '就诊卡号'
        },
        {
          field: 'zhuyuanNo',
          title: '住院号'
        },
        {
          field: 'amount',
          title: '订单金额',
          formatter: function (value, row, index) {
            return (Number(value) / 100).toFixed(2);
          }
        },
        {
          field: 'zyPayNo',
          title: '掌医充值订单号'
        },
        {
          field: 'wxPayNo',
          title: '微信充值订单号'
        },
        {
          field: 'zyRefundNo',
          title: '掌医退款订单号'
        },
        {
          field: 'hisTradeStatus',
          title: 'HIS交易状态'
        },
        {
          field: 'wxTradeStatus',
          title: '微信交易状态'
        },
        {
          field: 'manualRefundState',
          title: '人工退款状态',
          formatter: function (value, row, index) {
            return $.table.selectDictLabel(manualRefundStateDatas, value);
          }
        },
        {
          title: '操作',
          align: 'center',
          formatter: function (value, row, index) {
            var actions = [];
            actions.push('<a class="btn btn-success btn-xs ' + queryWXOrderFlag + '" href="javascript:void(0)" onclick="queryWXOrder(\'' + row.id + '\')"><i class="fa fa-refresh"></i>查询订单状态</a> ');
            if (row['hisTradeStatus'] === '退款成功' && row['wxTradeStatus'] !== '退款成功' && row['manualRefundState'] == 0) {
              actions.push('<a class="btn btn-success btn-xs ' + requestWXRefundFlag + '" href="javascript:void(0)" onclick="requestWXRefund(\'' + row.id + '\')"><i class="fa fa-paper-plane-o"></i>申请微信退款</a> ');
            }
            if (row['hisTradeStatus'] === '退款成功' && row['wxTradeStatus'] !== '退款成功' && row['manualRefundState'] == 1) {
              actions.push('<a class="btn btn-success btn-xs ' + approveWXRefundFlag + '" href="javascript:void(0)" onclick="approveWXRefund(\'' + row.id + '\')"><i class="fa fa-check"></i>放行微信退款</a> ');
            }
            return actions.join('');
          }
        }]
    };
    $.table.init(options);
  });

  function queryWXOrder(id) {
    $.operate.get(prefix + '/queryWXOrder/' + id, function (resp) {
      layer.open({
        type: 1,
        skin: 'layui-layer-rim',
        area: ['80%', '80%'],
        title: '查询订单状态',
        content: '<pre class="layui-code" lay-title="JSON" lay-encode="true">' + JSON.stringify(resp, null, 4) + '</pre>',
        success: function () {
          layui.use('code', function () {
            layui.code();
          });
        }
      });
    });
  }

  function requestWXRefund(id) {
    $.operate.post(prefix + '/requestWXRefund/' + id, {})
  }

  function approveWXRefund(id) {
    $.operate.post(prefix + '/approveWXRefund/' + id, {})
  }
</script>
</body>
</html>