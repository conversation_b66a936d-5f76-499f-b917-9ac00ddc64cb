<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('医生分类列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>ID：</label>
                                <input type="text" name="id"/>
                            </li>
                            <li>
                                <label>名称：</label>
                                <input type="text" name="name"/>
                            </li>
                            <li>
                                <label>分类代码：</label>
                                <input type="text" name="categoryCode"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.treeTable.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="ph:doctorCategory:add">
                    <i class="fa fa-plus"></i> 新增
                </a>
                <a class="btn btn-primary" onclick="$.operate.edit()" shiro:hasPermission="ph:doctorCategory:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-info" id="expandAllBtn">
                    <i class="fa fa-exchange"></i> 展开/折叠
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-tree-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var addFlag = [[${@permission.hasPermi('ph:doctorCategory:add')}]];
        var editFlag = [[${@permission.hasPermi('ph:doctorCategory:edit')}]];
        var removeFlag = [[${@permission.hasPermi('ph:doctorCategory:remove')}]];
        var isSubspecialtyDatas = [[${@dict.getType('yes_no')}]];
        var prefix = ctx + "ph/doctorCategory";

        $(function() {
            var options = {
                code: "id",
                parentCode: "parentId",
                expandColumn: "1",
                uniqueId: "id",
                url: prefix + "/list",
                createUrl: prefix + "/add/{id}",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove/{id}",
                modalName: "医生分类",
                detailView: true,
                onExpandRow : function(index, row, $detail) {
                    initChildTable(index, row, $detail);
                },
                columns: [{
                    field: 'selectItem',
                    radio: true
                },
                {
                    field: 'name',
                    title: '名称',
                    align: 'left'
                },
                {
                    field: 'sortNo',
                    title: '排序号',
                    align: 'left'
                },
                {
                    field: 'parentId',
                    title: '父项ID',
                    align: 'left'
                },
                    {
                        field: 'categoryCode',
                        title: '分类代码',
                        align: 'left'
                    },
                    {
                        field: 'address',
                        title: '位置',
                        align: 'left'
                    },
                    {
                        field: 'remark',
                        title: '备注',
                        align: 'left'
                    },
                    {
                        title: '操作',
                        align: 'center',
                        align: 'left',
                        formatter: function (value, row, index) {
                            var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-info  btn-xs ' + addFlag + '" href="javascript:void(0)" onclick="$.operate.add(\'' + row.id + '\')"><i class="fa fa-plus"></i>新增</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        actions.push('<button type="button" class="btn btn-success btn-xs" onclick="selectDoctor(\'' + row.id + '\')">选择医生</button>');
                        return actions.join('');
                    }
                }]
            };
            $.treeTable.init(options);

            initChildTable = function(index, row, $detail) {
                var childTable = $detail.html('<table style="table-layout:fixed"></table>').find('table');
                $(childTable).bootstrapTable({
                    url: prefix + "/list",
                    method: 'post',
                    sidePagination: "server",
                    contentType: "application/x-www-form-urlencoded",
                    queryParams : {
                        userName: '测试8'
                    },
                    columns: [{
                        field : 'userId',
                        title : '子表ID'
                    },
                        {
                            field : 'userCode',
                            title : '子表编号'
                        },
                        {
                            field : 'userName',
                            title : '子表姓名'
                        },
                        {
                            field: 'status',
                            title: '子表状态',
                            align: 'center',
                            formatter: function(value, row, index) {
                                return $.table.selectDictLabel(datas, value);
                            }
                        }]
                });
            };
        });

        function selectDoctor(doctorCategoryId) {
            $.modal.open("选择医生", ctx + "ph/doctor" + `/associateDoctor?doctorCategoryId=${doctorCategoryId}`);
        }
    </script>
</body>
</html>