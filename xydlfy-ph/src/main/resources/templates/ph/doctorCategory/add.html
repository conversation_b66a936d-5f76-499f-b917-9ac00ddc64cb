<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增医生分类')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-doctorCategory-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">名称：</label>
                <div class="col-sm-8">
                    <input name="name" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">排序号：</label>
                <div class="col-sm-8">
                    <input name="sortNo" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">父项ID：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input id="treeId" name="parentId" type="hidden" th:value="${doctorCategory?.id}"/>
                        <input class="form-control" type="text" onclick="selectDoctorCategoryTree()" id="treeName" readonly="true" th:value="${doctorCategory?.name}">
                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">分类代码：</label>
                <div class="col-sm-8">
                    <input name="categoryCode" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">位置：</label>
                <div class="col-sm-8">
                    <input name="address" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">是否亚专业：</label>
                <div class="col-sm-8">
                    <select name="isSubspecialty" class="form-control m-b" th:with="type=${@dict.getType('yes_no')}">
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                th:value="${dict.dictValue}"></option>
                    </select>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "ph/doctorCategory"
        $("#form-doctorCategory-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-doctorCategory-add').serialize());
            }
        }

        /*医生分类-新增-选择父医生分类树*/
        function selectDoctorCategoryTree() {
            var options = {
                title: '医生分类选择',
                width: "380",
                url: prefix + "/selectDoctorCategoryTree/" + $("#treeId").val(),
                callBack: doSubmit
            };
            $.modal.openOptions(options);
        }

        function doSubmit(index, layero){
            var body = $.modal.getChildFrame(index);
            $("#treeId").val(body.find('#treeId').val());
            $("#treeName").val(body.find('#treeName').val());
            $.modal.close(index);
        }
    </script>
</body>
</html>