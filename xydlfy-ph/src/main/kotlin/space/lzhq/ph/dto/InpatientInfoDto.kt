package space.lzhq.ph.dto

import com.fasterxml.jackson.annotation.JsonUnwrapped
import org.mospital.bsoft.ZhuyuanPatient
import org.mospital.common.TokenManager
import org.mospital.jackson.DateTimeFormatters
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

data class InpatientInfo(
    val admissionId: String,
    val name: String,
    val idCardNo: String,
    val phone: String,
    val sex: String,
    val birthday: LocalDate,
    val status: String,
    val admissionTime: LocalDateTime,
    val dischargeTime: LocalDateTime?,
    val ward: String
) {
    companion object {

        private val log: Logger = LoggerFactory.getLogger(InpatientInfo::class.java)
        private val witontekDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/M/d H:mm:ss")

        @JvmStatic
        fun fromBsoftZhuyuanPatient(patient: ZhuyuanPatient): InpatientInfo? {
            // 检查必要字段是否为空
            if (patient.patientBirthday.isEmpty() || patient.admissionTime.isEmpty()) {
                log.error("住院信息缺少必要字段: patientBirthday=${patient.patientBirthday}, admissionTime=${patient.admissionTime}")
                return null
            }

            return try {
                val birthday =
                    LocalDate.parse(patient.patientBirthday, DateTimeFormatters.NORM_DATE_FORMATTER)
                val admissionTime =
                    LocalDateTime.parse(patient.admissionTime, DateTimeFormatters.NORM_DATETIME_FORMATTER)
                val ward = listOf(patient.departmentName, patient.patientBedNumber).filter { it.isNotEmpty() }
                    .joinToString("-")
                InpatientInfo(
                    admissionId = patient.admissionNumber,
                    name = patient.patientName,
                    idCardNo = patient.patientIdCard,
                    phone = patient.patientPhone,
                    sex = when (patient.patientSex) {
                        "1" -> "男"
                        "2" -> "女"
                        else -> "未知"
                    },
                    birthday = birthday,
                    status = "",
                    admissionTime = admissionTime,
                    dischargeTime = null,
                    ward = ward
                )
            } catch (e: Exception) {
                log.error("解析住院信息失败: ${e.message}", e)
                null
            }
        }
    }
}

data class InpatientInfoDto(
    @field:JsonUnwrapped
    val data: InpatientInfo
) {
    val token: String = TokenManager.generateToken(data)
}