package space.lzhq.ph.interceptor

import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.json.JSON
import com.ruoyi.common.utils.ServletUtils
import com.ruoyi.common.utils.spring.SpringUtils
import org.springframework.stereotype.Component
import org.springframework.web.method.HandlerMethod
import org.springframework.web.servlet.HandlerInterceptor
import space.lzhq.ph.common.Values.CURRENT_ZHUYUAN_PATIENT
import space.lzhq.ph.domain.Session
import space.lzhq.ph.domain.ZhuyuanPatient
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IZhuyuanPatientService
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse

@Component
class ActiveZhuyuanPatientInterceptor : HandlerInterceptor {

    override fun preHandle(request: HttpServletRequest, response: HttpServletResponse, handler: Any): Bo<PERSON>an {
        if (handler is HandlerMethod) {
//            val requireActivePatient = handler.method.getAnnotation(RequireActivePatient::class.java) ?: return true


            val session: Session? = request.getClientSession()
            if (session == null) {
                ServletUtils.renderString(response, JSON.marshal(AjaxResult.error("会话无效，请重新登录")))
                return false
            }

            val patientService = SpringUtils.getBean(IZhuyuanPatientService::class.java)
            val currentPatient: ZhuyuanPatient? = patientService.selectActivePatientByOpenId(session.openId)
            if (currentPatient == null) {
                ServletUtils.renderString(response, JSON.marshal(AjaxResult.error("请先绑定住院号")))
                return false
            }



            request.setAttribute(CURRENT_ZHUYUAN_PATIENT, currentPatient)
            return true
        } else {
            return super.preHandle(request, response, handler)
        }
    }

}
