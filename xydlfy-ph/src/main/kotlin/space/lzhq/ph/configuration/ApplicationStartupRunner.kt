package space.lzhq.ph.configuration

import com.ruoyi.framework.manager.AsyncManager
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.boot.ApplicationArguments
import org.springframework.boot.ApplicationRunner
import org.springframework.stereotype.Component
import space.lzhq.ph.service.DepartmentKit
import java.util.TimerTask

/**
 * 应用启动后执行的任务
 *
 * <AUTHOR>
 */
@Component
class ApplicationStartupRunner : ApplicationRunner {

    private val log: Logger = LoggerFactory.getLogger(ApplicationStartupRunner::class.java)

    override fun run(args: ApplicationArguments?) {
        log.info("应用启动完成，开始执行启动后任务...")

        // 异步预加载科室列表，避免阻塞应用启动
        AsyncManager.me().execute(object : TimerTask() {
            override fun run() {
                try {
                    log.info("开始异步预加载科室列表...")
                    DepartmentKit.sync()
                    log.info("科室列表预加载完成")
                } catch (e: Exception) {
                    log.error("预加载科室列表失败", e)
                }
            }
        })

        log.info("启动后任务已提交执行")
    }
}
