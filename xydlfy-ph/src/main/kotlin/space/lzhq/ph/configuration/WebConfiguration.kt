package space.lzhq.ph.configuration

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Configuration
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
import space.lzhq.ph.interceptor.*

@Configuration
open class WebConfiguration : WebMvcConfigurer {

    @Autowired
    private lateinit var wxSessionInterceptor: SessionInterceptor

    @Autowired
    private lateinit var activePatientInterceptor: ActivePatientInterceptor

    @Autowired
    private lateinit var pscNurseSessionInterceptor: PscNurseSessionInterceptor

    @Autowired
    private lateinit var pscCarerSessionInterceptor: PscCarerSessionInterceptor

    @Autowired
    private lateinit var activeZhuyuanPatientInterceptor: ActiveZhuyuanPatientInterceptor

    override fun addInterceptors(registry: InterceptorRegistry) {
        registry.addInterceptor(wxSessionInterceptor).addPathPatterns("/api/**")
        registry.addInterceptor(activePatientInterceptor).addPathPatterns("/api/**")
            .excludePathPatterns("/api/psc/**", "/api/zhuyuan/**")
        registry.addInterceptor(activeZhuyuanPatientInterceptor)
            .addPathPatterns("/api/zhuyuan/**")
            .excludePathPatterns("/api/zhuyuan/refund/**", "/api/zhuyuan/refund")
        registry.addInterceptor(pscNurseSessionInterceptor).addPathPatterns("/api/psc/**")
        registry.addInterceptor(pscCarerSessionInterceptor).addPathPatterns("/api/psc/**")
    }

}
