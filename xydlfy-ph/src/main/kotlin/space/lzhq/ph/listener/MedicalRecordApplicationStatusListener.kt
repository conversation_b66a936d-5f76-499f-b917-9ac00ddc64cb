package space.lzhq.ph.listener

import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import space.lzhq.ph.domain.MedicalRecordApplicationStatusEvent
import space.lzhq.ph.enums.MedicalRecordApplicationStatus
import space.lzhq.ph.ext.WeixinExt

@Component
class MedicalRecordApplicationStatusListener {

    @Async
    @EventListener(MedicalRecordApplicationStatusEvent::class)
    fun handle(event: MedicalRecordApplicationStatusEvent) {
        val application = event.data
        val companionId = "medical_record_application#${application.id}#${application.status.name}"
        when (application.status) {
            MedicalRecordApplicationStatus.APPROVED -> {
                // 审核通过
                WeixinExt.sendSubscribeMessage(
                    messageId = "si0cBPPTZ5c8Qj7FsoaHe-XCrf-wnacjhxdMnT8qMQI",
                    messagePage = null,
                    messageDataList = listOf(
                        WeixinExt.newWxMaSubscribeMessageThingData("thing1", application.patientName),
                        WeixinExt.newWxMaSubscribeMessageThingData("character_string2", application.admissionId),
                        WeixinExt.newWxMaSubscribeMessageThingData("thing3", "审核通过，待支付"),
                        WeixinExt.newWxMaSubscribeMessageThingData("thing4", "您的病历审核通过，请尽快支付费用")
                    ),
                    openid = application.openId,
                    companionId = companionId
                )
            }

            MedicalRecordApplicationStatus.REJECTED -> {
                // 审核驳回
                WeixinExt.sendSubscribeMessage(
                    messageId = "si0cBPPTZ5c8Qj7FsoaHe-XCrf-wnacjhxdMnT8qMQI",
                    messagePage = null,
                    messageDataList = listOf(
                        WeixinExt.newWxMaSubscribeMessageThingData("thing1", application.patientName),
                        WeixinExt.newWxMaSubscribeMessageThingData("character_string2", application.admissionId),
                        WeixinExt.newWxMaSubscribeMessageThingData("thing3", "审核驳回"),
                        WeixinExt.newWxMaSubscribeMessageThingData("thing4", application.rejectReason)
                    ),
                    openid = application.openId,
                    companionId = companionId
                )
            }

            MedicalRecordApplicationStatus.DELIVERED -> {
                // 邮寄完成
                WeixinExt.sendSubscribeMessage(
                    messageId = "5AEUp-wmu2Yc_Sb3ubiKJjNENLKG4FqmrWnf0jp5J6s",
                    messagePage = null,
                    messageDataList = listOf(
                        WeixinExt.newWxMaSubscribeMessageThingData("thing1", application.recipientName),
                        WeixinExt.newWxMaSubscribeMessageThingData("phone_number6", application.recipientMobile),
                        WeixinExt.newWxMaSubscribeMessageThingData("character_string2", application.trackingNumber),
                        WeixinExt.newWxMaSubscribeMessageThingData("thing4", "您申请的病历已发货，请及时关注物流信息")
                    ),
                    openid = application.openId,
                    companionId = companionId
                )
            }

            else -> { /* do nothing */ }
        }
    }

}