package space.lzhq.ph.vo

import com.fasterxml.jackson.annotation.JsonFormat
import java.time.LocalDateTime

/**
 * 门诊停诊消息VO
 */
data class OutpatientStopClinicVO(
    /**
     * 消息ID
     */
    val id: Long,

    /**
     * 排班ID
     */
    val scheduleMark: String,

    /**
     * 预约ID
     */
    val appointmentId: String,

    /**
     * 挂号科室名称
     */
    val registeredDeptName: String,

    /**
     * 挂号医生姓名
     */
    val registeredDoctorName: String?,

    /**
     * 挂号时间
     */
    @field:JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    val registeredDateTime: LocalDateTime,

    /**
     * 预约时间（就诊时间）
     */
    @field:JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    val appointmentDateTime: LocalDateTime,

    /**
     * 消息内容
     */
    val messageContent: String?,

    /**
     * 是否已读
     */
    val isRead: Boolean,

    /**
     * 创建时间
     */
    @field:JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    val createTime: LocalDateTime
) {
    /**
     * 是否已过期
     */
    val expired: Boolean
        get() = appointmentDateTime.isBefore(LocalDateTime.now())
}