package space.lzhq.ph.service

import com.ruoyi.common.utils.spring.SpringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import space.lzhq.ph.domain.Department

object DoctorKit {

    private val log: Logger = LoggerFactory.getLogger(DoctorKit::class.java)

    fun sync(): <PERSON><PERSON><PERSON> {
        return try {
            val departmentService: IDepartmentService = SpringUtils.getBean(IDepartmentService::class.java)
            val departments: List<Department> = departmentService.selectDepartmentAll()
            true
        } catch (e: Exception) {
            log.debug("同步医生出错", e)
            false
        }
    }

}