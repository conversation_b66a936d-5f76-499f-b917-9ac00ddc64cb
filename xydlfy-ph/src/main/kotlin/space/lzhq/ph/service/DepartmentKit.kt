package space.lzhq.ph.service

import com.ruoyi.common.utils.PinyinUtils
import com.ruoyi.common.utils.spring.SpringUtils
import kotlinx.coroutines.runBlocking
import org.mospital.bsoft.BSoftService
import org.mospital.bsoft.ReservationDept
import org.mospital.bsoft.ReservationDeptForm
import org.mospital.bsoft.Result
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import space.lzhq.ph.common.Values
import space.lzhq.ph.domain.DoctorCategory
import java.text.Collator
import java.util.*

object DepartmentKit {

    private val log: Logger = LoggerFactory.getLogger(DepartmentKit::class.java)

    @Synchronized
    fun sync() {
        try {
            val deptListResult: Result<List<ReservationDept>> = runBlocking {
                BSoftService.me.getReservationDeptList(
                    ReservationDeptForm(sourceFlag = 1)
                )
            }
            if (!deptListResult.isOk()) {
                return
            }

            val deptList: List<ReservationDept> = deptListResult.data!!
            Values.INDEXED_DEPARTMENTS = deptList
                .asSequence()
                .distinctBy { it.deptId }
                .onEach {
                    it.deptName = it.deptName.removePrefix("门")
                }.filter {
                    it.deptId != "119" && it.deptName != "急诊医学科门诊"
                }.groupBy {
                    PinyinUtils.getSimplePinyin(it.deptName.first().toString(), "", true).uppercase()
                }.map {
                    mapOf("index" to it.key, "departments" to it.value.sortedWith { d1, d2 ->
                        Collator.getInstance(Locale.SIMPLIFIED_CHINESE).compare(d1.deptName, d2.deptName)
                    })
                }.sortedWith { o1, o2 -> (o1["index"] as String).compareTo(o2["index"] as String) }
                .toList()

            val hisDepartmentList = deptList.distinctBy { it.deptId }.onEach {
                it.deptName = it.deptName.removePrefix("门")
            }.map {
                DoctorCategory().apply {
                    this.parentId = 1
                    this.name = it.deptName
                    this.sortNo = 1
                    this.categoryCode = it.deptId
                    this.namePinyinFirst = PinyinUtils.getSimplePinyin(this.name, "", true)
                    this.namePinyin = PinyinUtils.getFullPinyin(this.name, "", true)
                    this.isSubspecialty = 0
                }
            }

            val doctorCategoryService = SpringUtils.getBean(IDoctorCategoryService::class.java)
            val categoryList = doctorCategoryService.selectDoctorCategoryList(null)
            val departmentList = categoryList.filter { it.parentId == 1L }

            hisDepartmentList
                .filter { hisDept -> departmentList.all { it.categoryCode != hisDept.categoryCode } }
                .forEach { doctorCategoryService.insertDoctorCategory(it) }
        } catch (e: Exception) {
            log.debug("同步科室出错", e);
        }
    }

}