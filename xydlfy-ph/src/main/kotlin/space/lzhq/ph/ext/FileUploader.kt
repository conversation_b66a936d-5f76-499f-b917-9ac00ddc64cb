package space.lzhq.ph.ext

import com.ruoyi.common.config.RuoYiConfig
import com.ruoyi.common.config.ServerConfig
import com.ruoyi.common.exception.file.FileNameLengthLimitExceededException
import com.ruoyi.common.exception.file.FileSizeLimitExceededException
import com.ruoyi.common.exception.file.InvalidExtensionException
import com.ruoyi.common.exception.file.InvalidExtensionException.*
import com.ruoyi.common.utils.DateUtils
import com.ruoyi.common.utils.StringUtils
import com.ruoyi.common.utils.file.MimeTypeUtils
import com.ruoyi.common.utils.spring.SpringUtils
import org.apache.commons.io.FilenameUtils
import org.dromara.hutool.core.data.id.IdUtil
import org.slf4j.LoggerFactory
import org.springframework.web.multipart.MultipartFile
import space.lzhq.ph.util.FilenameDecoder
import java.io.File
import java.io.IOException
import java.nio.file.Path
import java.nio.file.Paths

object FileUploader {

    /**
     * 默认大小 5M
     */
    const val DEFAULT_MAX_SIZE = 5 * 1024 * 1024L

    /**
     * 默认的文件名最大长度 100
     */
    const val DEFAULT_FILE_NAME_LENGTH = 100

    /**
     * 默认上传的地址
     */
    private var defaultBaseDir = RuoYiConfig.getUploadPath()

    private val logger = LoggerFactory.getLogger(FileUploader::class.java)

    /**
     * 检查上传文件名称是否合法，主要检查:
     * 1. 文件是否为空
     * 2. 文件名长度是否超过限制(100字符)
     * 3. 文件名是否包含非法字符(只允许字母数字下划线减号竖线点空格和中文)
     * 4. 文件名是否包含路径穿越字符(.., /, \)
     * 5. 文件扩展名是否在允许列表中
     *
     * @param file 待上传的文件
     * @param allowedExtension 允许的文件扩展名数组,默认为DEFAULT_ALLOWED_EXTENSION
     * @return 文件名
     * @throws IllegalArgumentException 当文件名不合法时抛出,包含具体错误信息
     */
    @Throws(IllegalArgumentException::class)
    fun checkAndGetFileName(
        file: MultipartFile,
        allowedExtension: Array<String> = MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION,
    ): String {
        // 检查文件是否为空
        require(!(file.isEmpty)) { "文件不能为空" }

        val fileName = FilenameDecoder.getCorrectFilename(file)

        // 检查文件名长度，限制为100个字符
        val maxFilenameLength = DEFAULT_FILE_NAME_LENGTH
        require(fileName.length <= maxFilenameLength) { "文件名长度不能超过${maxFilenameLength}个字符" }

        // 检查文件名是否包含非法字符 - 使用正则表达式验证
        val filenamePattern = "[a-zA-Z0-9_\\-|.@ \\u4e00-\\u9fa5]+"
        require(fileName.matches(Regex(filenamePattern))) { "文件名包含非法字符" }

        // 检查文件名是否包含路径遍历字符
        require(!(fileName.contains("..") || fileName.contains("/") || fileName.contains("\\"))) { "文件名包含非法路径字符" }

        // 检查文件后缀是否在允许范围内
        val extension = getFileExtension(fileName)
        require(isAllowedExtension(extension, allowedExtension)) { "不支持的文件类型: $extension" }

        return fileName
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 文件扩展名
     */
    private fun getFileExtension(fileName: String): String {
        val dotIndex = fileName.lastIndexOf('.')
        return if (dotIndex == -1) "" else fileName.substring(dotIndex + 1).lowercase()
    }

    /**
     * 文件上传
     *
     * @param baseDir 相对应用的基目录
     * @param file 上传的文件
     * @param allowedExtension 上传文件类型
     * @return 返回上传成功的文件名
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws FileNameLengthLimitExceededException 文件名太长
     * @throws IOException 比如读写文件出错时
     * @throws InvalidExtensionException 文件校验异常
     */
    @Throws(
        FileSizeLimitExceededException::class,
        IOException::class,
        FileNameLengthLimitExceededException::class,
        InvalidExtensionException::class
    )
    fun upload(
        file: MultipartFile,
        baseDir: String = defaultBaseDir,
        allowedExtension: Array<String> = MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION,
    ): File {
        checkAndGetFileName(file, allowedExtension)
        checkAndGetExtension(file, allowedExtension)
        checkFileSize(file)
        val fileName = randomFileName(file) // 随机文件名，示例：2025/05/22/b6856524f19f91ebb6856524f19f91eb.png
        val destinationFile = getFile(baseDir, fileName)
        file.transferTo(destinationFile)
        return destinationFile
    }

    /**
     * 校验文件大小和扩展名
     *
     * @param file 待校验的上传文件
     * @param allowedExtension 允许的文件扩展名数组
     * @return 文件扩展名
     * @throws FileSizeLimitExceededException 当文件大小超过DEFAULT_MAX_SIZE限制时抛出
     * @throws InvalidExtensionException 当文件扩展名不在允许范围内时抛出，根据文件类型抛出对应的子类异常:
     *         - InvalidImageExtensionException: 图片类型校验失败
     *         - InvalidFlashExtensionException: Flash类型校验失败
     *         - InvalidMediaExtensionException: 媒体类型校验失败
     */
    @Throws(FileSizeLimitExceededException::class, InvalidExtensionException::class)
    fun checkAndGetExtension(file: MultipartFile, allowedExtension: Array<String>): String {
        val fileName = file.originalFilename
        val extension = getExtension(file)
        if (allowedExtension.isNotEmpty() && !isAllowedExtension(extension, allowedExtension)) {
            when (allowedExtension) {
                MimeTypeUtils.IMAGE_EXTENSION -> {
                    throw InvalidImageExtensionException(
                        allowedExtension, extension,
                        fileName
                    )
                }

                MimeTypeUtils.FLASH_EXTENSION -> {
                    throw InvalidFlashExtensionException(
                        allowedExtension, extension,
                        fileName
                    )
                }

                MimeTypeUtils.MEDIA_EXTENSION -> {
                    throw InvalidMediaExtensionException(
                        allowedExtension, extension,
                        fileName
                    )
                }

                else -> {
                    throw InvalidExtensionException(allowedExtension, extension, fileName)
                }
            }
        }

        return extension
    }

    @Throws(FileSizeLimitExceededException::class)
    fun checkFileSize(file: MultipartFile) {
        if (file.size > DEFAULT_MAX_SIZE) {
            throw FileSizeLimitExceededException(DEFAULT_MAX_SIZE / 1024 / 1024)
        }
    }

    @Throws(IOException::class)
    fun getUrl(file: File): String {
        val absolutePath = file.toPath().normalize().toAbsolutePath().toString()
        val profilePath = Paths.get(RuoYiConfig.getProfile()).normalize().toAbsolutePath()
        val relativePath = profilePath.relativize(Paths.get(absolutePath)).toString()

        val serverConfig: ServerConfig = SpringUtils.getBean(ServerConfig::class.java)
        return serverConfig.profileUrl + "/" + relativePath.replace("\\", "/")
    }

    fun getFile(url: String): File {
        val serverConfig: ServerConfig = SpringUtils.getBean(ServerConfig::class.java)
        val profileUrl = serverConfig.profileUrl
        val relativePath = url.substringAfter(profileUrl, "")

        // 检查 relativePath 是否为空，如果为空说明 URL 不包含期望的 profileUrl 前缀
        require(relativePath.isNotEmpty()) { "URL '$url' is not within the configured profileUrl '$profileUrl'" }

        return Path.of(RuoYiConfig.getProfile(), relativePath).toFile()
    }

    /**
     * 删除文件
     *
     * @param url 文件URL，不能为空
     * @return 是否删除成功，如果文件不存在，则返回true
     * @throws IllegalArgumentException 当URL为空或格式不正确时抛出
     * @throws SecurityException 当没有删除文件的权限时抛出
     */
    @Throws(IllegalArgumentException::class, SecurityException::class)
    fun deleteFile(url: String): Boolean {
        require(url.isNotBlank()) { "文件URL不能为空" }

        return try {
            val file = getFile(url)
            when {
                !file.exists() -> true
                file.delete() -> {
                    logger.debug("文件删除成功: ${file.absolutePath}")
                    true
                }

                else -> {
                    logger.error("文件删除失败: ${file.absolutePath}")
                    false
                }
            }
        } catch (e: Exception) {
            logger.error("删除文件时发生异常，URL: $url", e)
            when (e) {
                is IllegalArgumentException, is SecurityException -> throw e
                else -> throw IllegalStateException("删除文件时发生错误: ${e.message}", e)
            }
        }
    }

    @Throws(IOException::class)
    private fun getFile(uploadDir: String = defaultBaseDir, fileName: String): File {
        val file = File(uploadDir + File.separator + fileName)

        // 确保父目录存在
        if (!file.parentFile.exists()) {
            val dirsCreated = file.parentFile.mkdirs()
            if (!dirsCreated && !file.parentFile.exists()) {
                throw IOException("无法创建目录: ${file.parentFile.absolutePath}")
            }
        }

        return file
    }

    /**
     * 编码文件名
     */
    fun randomFileName(file: MultipartFile): String {
        val extension = getExtension(file)
        return DateUtils.datePath() + "/" + IdUtil.fastSimpleUUID() + "." + extension
    }

    /**
     * 获取文件名的后缀
     *
     * @param file 表单文件
     * @return 后缀名
     */
    fun getExtension(file: MultipartFile): String {
        var extension = FilenameUtils.getExtension(file.originalFilename)
        if (StringUtils.isEmpty(extension)) {
            extension = MimeTypeUtils.getExtension(file.contentType)
        }
        return extension
    }

    /**
     * 判断MIME类型是否是允许的MIME类型
     *
     * @param extension
     * @param allowedExtension
     * @return
     */
    fun isAllowedExtension(extension: String, allowedExtension: Array<String>): Boolean {
        for (str in allowedExtension) {
            if (str.equals(extension, ignoreCase = true)) {
                return true
            }
        }
        return false
    }
}
