package space.lzhq.ph.ext

import cn.binarywang.wx.miniapp.api.WxMaService
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage
import com.alibaba.fastjson.JSON
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult
import com.github.binarywang.wxpay.bean.request.*
import com.github.binarywang.wxpay.bean.result.WxInsurancePayRefundResult
import com.github.binarywang.wxpay.bean.result.WxPayBillInfo
import com.github.binarywang.wxpay.bean.result.WxPayBillResult
import com.github.binarywang.wxpay.bean.result.WxPayRefundResult
import com.github.binarywang.wxpay.config.WxPayConfig
import com.github.binarywang.wxpay.constant.WxPayConstants
import com.github.binarywang.wxpay.exception.WxPayException
import com.github.binarywang.wxpay.service.WxInsurancePayService
import com.github.binarywang.wxpay.service.WxPayService
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl
import com.ruoyi.common.constant.Constants
import com.ruoyi.common.utils.spring.SpringUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.launch
import me.chanjar.weixin.common.bean.ocr.WxOcrIdCardResult
import me.chanjar.weixin.common.service.WxOcrService
import org.dromara.hutool.core.io.file.FileUtil
import org.dromara.hutool.core.text.StrUtil
import org.dromara.hutool.http.client.HttpDownloader
import org.dromara.hutool.log.LogFactory
import org.mospital.common.StringKit
import org.mospital.jackson.DateTimeFormatters
import org.mospital.wecity.mip.WeixinMipSetting
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.configuration.WxSubscribeMessageConfiguration
import space.lzhq.ph.domain.*
import space.lzhq.ph.service.IMipWxOrderService
import space.lzhq.ph.service.IWxPaymentService
import space.lzhq.ph.service.IWxRefundService
import space.lzhq.ph.service.IWxSubscribeMessageService
import java.io.File
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

object WeixinExt {

    private val log = LogFactory.getLog(WeixinExt::class.java)

    private var wxPayServiceForMenZhen: WxPayService? = null
    private var wxPayServiceForZhuYuan: WxPayService? = null
    private val wxPayServices = mutableMapOf<String, WxPayService>()

    init {
        mapOf(
            "MenZhen" to ::getWxPayServiceForMenZhen,
            "ZhuYuan" to ::getWxPayServiceForZhuYuan
        ).forEach { (name, initializer) ->
            try {
                initializer()
            } catch (e: Exception) {
                log.error("Failed to pre-initialize WxPayService for $name", e)
            }
        }
    }

    @Synchronized
    fun getWxPayServiceForMenZhen(): WxPayService {
        if (wxPayServiceForMenZhen == null) {
            val mchId = "1610072859"
            val wxPayConfig: WxPayConfig = WxPayConfig().apply {
                this.appId = "wxccb0430de6991c6d"
                this.mchId = mchId
                this.mchKey = "XinJiangErTongYiYuan161007285900"
                this.keyPath = "classpath:apiclient_cert.p12"
                this.apiV3Key = "2mB6RKZJXzJUPhrjpCA6sNFNNxNMNUW9"
                this.certSerialNo = "3DB26475690AA803E74DA419E61E314864A0F0C7"
                this.privateKeyPath = "classpath:1610072859_20250423_key.pem"
                this.privateCertPath = "classpath:1610072859_20250423_cert.pem"
            }
            wxPayServiceForMenZhen = WxPayServiceImpl().apply {
                this.config = wxPayConfig
            }
            wxPayServices[mchId] = wxPayServiceForMenZhen!!
        }
        return wxPayServiceForMenZhen!!
    }

    @Synchronized
    fun getWxPayServiceForZhuYuan(): WxPayService {
        if (wxPayServiceForZhuYuan == null) {
            val mchId = "1722500805"
            val wxPayConfig: WxPayConfig = WxPayConfig().apply {
                this.appId = "wxccb0430de6991c6d"
                this.mchId = mchId
                this.mchKey = "dwbSjBUK2VyzYQw784ABBA24zpyx7ptD"
                this.keyPath = "classpath:1722500805_20250715.p12"
                this.apiV3Key = "at64DDmPHBCseuGnyypFUh3xfAX3bhFH"
                this.certSerialNo = "3DB5F8159303E552603D4D5F666DABDC7B6FB7C4"
                this.publicKeyId = "PUB_KEY_ID_0117225008052025071500211808002002"
                this.publicKeyPath = "classpath:1722500805_20250715_pub_key.pem"
                this.privateKeyPath = "classpath:1722500805_20250715_key.pem"
                this.privateCertPath = "classpath:1722500805_20250715_cert.pem"
            }
            wxPayServiceForZhuYuan = WxPayServiceImpl().apply {
                this.config = wxPayConfig
            }
            wxPayServices[mchId] = wxPayServiceForZhuYuan!!
        }
        return wxPayServiceForZhuYuan!!
    }

    fun getWxPayServiceByMchId(mchId: String): WxPayService? {
        return wxPayServices[mchId]
    }

    fun getWxPayServices(): List<WxPayService> {
        return wxPayServices.values.toList()
    }

    fun createUnifiedOrderRequest(
        serviceType: ServiceType,
        openid: String,
        amount: Int,
        ip: String,
        outTradeNo: String,
        remark: String = "",
        expireTime: LocalDateTime = LocalDateTime.now().plusMinutes(10)
    ): WxPayUnifiedOrderRequest {
        val startTimeString: String = LocalDateTime.now().format(DateTimeFormatters.PURE_DATETIME_FORMATTER)
        // 10分钟后过期
        val expireTimeString: String = expireTime.format(DateTimeFormatters.PURE_DATETIME_FORMATTER)

        val bodyString: String = "${Constants.MERCHANT_NAME}-" + when (serviceType) {
            ServiceType.MZ -> "门诊预交金"
            ServiceType.ZY -> "住院预交金"
            ServiceType.ZJ -> "诊间缴费"
            ServiceType.BL -> "病历邮寄"
            ServiceType.TF -> "微信商家转账"
        } + (if (remark.isNotBlank()) "-$remark" else "")

        return WxPayUnifiedOrderRequest.newBuilder()
            .tradeType(WxPayConstants.TradeType.JSAPI)
            .body(bodyString)
            .outTradeNo(outTradeNo)
            .totalFee(amount)
            .spbillCreateIp(ip)
            .timeStart(startTimeString)
            .timeExpire(expireTimeString)
            .notifyUrl(Constants.ON_WX_PAY)
            .openid(openid)
            .build()
    }

    fun pay(
        serviceType: ServiceType,
        amount: Int,
        ip: String,
        outTradeNo: String,
        patient: Patient,
        expireTime: LocalDateTime = LocalDateTime.now().plusMinutes(10),
        remark: String = "",
        wxPayService: WxPayService = getWxPayServiceForMenZhen(),
        callback: (WxPayUnifiedOrderRequest, WxPayMpOrderResult) -> Unit,
    ): WxPayMpOrderResult {
        val unifiedOrderRequest: WxPayUnifiedOrderRequest = createUnifiedOrderRequest(
            serviceType = serviceType,
            openid = patient.openId,
            amount = amount,
            ip = ip,
            outTradeNo = outTradeNo,
            expireTime = expireTime,
            remark = remark
        )
        val orderResult: WxPayMpOrderResult = wxPayService.createOrder(unifiedOrderRequest)

        CoroutineScope(Dispatchers.IO).launch {
            callback(unifiedOrderRequest, orderResult)
        }

        return orderResult
    }

    fun pay(
        serviceType: ServiceType,
        amount: Int,
        ip: String,
        outTradeNo: String,
        patient: ZhuyuanPatient,
        wxPayService: WxPayService = getWxPayServiceForZhuYuan(),
        expireTime: LocalDateTime = LocalDateTime.now().plusMinutes(10),
        remark: String = "",
        callback: (WxPayUnifiedOrderRequest, WxPayMpOrderResult) -> Unit,
    ): WxPayMpOrderResult {
        val unifiedOrderRequest: WxPayUnifiedOrderRequest = createUnifiedOrderRequest(
            serviceType = serviceType,
            openid = patient.openId,
            amount = amount,
            ip = ip,
            outTradeNo = outTradeNo,
            expireTime = expireTime,
            remark = remark
        )
        val orderResult: WxPayMpOrderResult = wxPayService.createOrder(unifiedOrderRequest)

        CoroutineScope(Dispatchers.IO).launch {
            callback(unifiedOrderRequest, orderResult)
        }

        return orderResult
    }

    fun pay(
        serviceType: ServiceType,
        amount: Int,
        ip: String,
        outTradeNo: String,
        openId: String,
        wxPayService: WxPayService = getWxPayServiceForZhuYuan(),
        expireTime: LocalDateTime = LocalDateTime.now().plusMinutes(10),
        remark: String = "",
        callback: (WxPayUnifiedOrderRequest, WxPayMpOrderResult) -> Unit,
    ): WxPayMpOrderResult {
        val unifiedOrderRequest: WxPayUnifiedOrderRequest = createUnifiedOrderRequest(
            serviceType = serviceType,
            openid = openId,
            amount = amount,
            ip = ip,
            outTradeNo = outTradeNo,
            expireTime = expireTime,
            remark = remark
        )
        val orderResult: WxPayMpOrderResult = wxPayService.createOrder(unifiedOrderRequest)

        CoroutineScope(Dispatchers.IO).launch {
            callback(unifiedOrderRequest, orderResult)
        }

        return orderResult
    }


    /**
     * 退款
     * @param totalAmount 充值金额
     * @param amount 退款金额
     * @param wxPayNo 微信充值订单号
     * @param zyRefundNo 掌医退款订单号
     */
    @Throws(WxPayException::class)
    fun refund(
        totalAmount: Int,
        amount: Int,
        wxPayNo: String,
        zyRefundNo: String,
        wxPayService: WxPayService
    ): WxPayRefundResult {
        val refundRequest: WxPayRefundRequest =
            WxPayRefundRequest.newBuilder()
                .transactionId(wxPayNo)
                .outRefundNo(zyRefundNo)
                .totalFee(totalAmount)
                .refundFee(amount)
                .notifyUrl(Constants.ON_WX_REFUND)
                .build()
        return wxPayService.refund(refundRequest)
    }

    fun downloadAndSaveWxBill(
        billDate: LocalDate,
        wxPayService: WxPayService = getWxPayServiceForMenZhen()
    ): WxPayBillResult? {
        return try {
            (wxPayService as WxPayServiceImpl).downloadAndSaveBill(billDate = billDate)
        } catch (e: WxPayException) {
            if (e.returnMsg == "No Bill Exist") {
                FileUtil.writeUtf8String("当日无账单", buildBillPath(billDate))
                val wxPayBillResult = WxPayBillResult()
                wxPayBillResult.totalRecord = "0"
                wxPayBillResult.totalFee = "0.00"
                wxPayBillResult.totalAmount = "0.00"
                wxPayBillResult.totalRefundFee = "0.00"
                wxPayBillResult
            } else {
                throw e
            }
        }
    }

    fun downloadAndSaveMipBill(billDate: LocalDate): File? {
        return try {
            val wxInsurancePayDownloadBillResult = getWxInsurancePayService().downloadBill(
                WxInsurancePayDownloadBillRequest(
                    billDate = billDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                    billType = "ALL"
                )
            )
            val file = File(buildMipBillPath(billDate))
            HttpDownloader.downloadFile(wxInsurancePayDownloadBillResult.downloadUrl, file)
            file
        } catch (e: Exception) {
            null
        }
    }

    fun getOrDownloadBillFile(billDate: LocalDate): File {
        val path: String = buildBillPath(billDate)
        val file = File(path)
        if (file.exists()) {
            return file
        }

        downloadAndSaveWxBill(billDate)
        return File(path)
    }

    fun getOrDownloadMipBillFile(billDate: LocalDate): File {
        val path: String = buildMipBillPath(billDate)
        val file = File(path)
        if (file.exists()) {
            return file
        }

        downloadAndSaveMipBill(billDate)
        return File(path)
    }

    /**
     * 发送订阅消息
     */
    fun sendSubscribeMessage(
        messageId: String?,
        messagePage: String?,
        messageDataList: List<WxMaSubscribeMessage.MsgData>,
        openid: String,
        companionId: String,
    ): WxSubscribeMessage? {
        if (messageId.isNullOrBlank()) {
            return null
        }

        val wxSubscribeMessageConfiguration = SpringUtils.getBean(WxSubscribeMessageConfiguration::class.java)
        val type: String = when (messageId) {
            wxSubscribeMessageConfiguration.yuyueId -> "Yuyue"
            wxSubscribeMessageConfiguration.cancelYuyueId -> "CancelYuyue"
            wxSubscribeMessageConfiguration.visitingReminderId -> "VisitingReminder"
            wxSubscribeMessageConfiguration.stopClinicId -> "StopClinic"
            else -> "Unknown"
        }

        val wxSubscribeMessageService = SpringUtils.getBean(IWxSubscribeMessageService::class.java)
        if (wxSubscribeMessageService.existsByCompanionIdAndTypeAndSendStatus(companionId, type, true)) {
            return null
        }

        val subscribeMessage: WxMaSubscribeMessage = WxMaSubscribeMessage.builder().apply {
            if (!messagePage.isNullOrBlank()) {
                this.page(messagePage)
            }

            this.templateId(messageId)
            this.toUser(openid)
            this.data(messageDataList)
        }.build()
        val data = JSON.toJSONString(subscribeMessage.data)

        val wxSubscribeMessage = WxSubscribeMessage.builder()
            .templateId(subscribeMessage.templateId)
            .openid(subscribeMessage.toUser)
            .data(data)
            .type(type)
            .companionId(companionId)
            .sendTime(LocalDateTime.now())
            .sendStatus(true)
            .error("OK")
            .build()

        try {
            val wxMaService = SpringUtils.getBean(WxMaService::class.java)
            wxMaService.msgService.sendSubscribeMsg(subscribeMessage)
        } catch (e: Exception) {
            wxSubscribeMessage.sendStatus = false
            wxSubscribeMessage.error = e.message ?: "未知错误"
        }

        wxSubscribeMessageService.save(wxSubscribeMessage)
        return wxSubscribeMessage
    }

    fun newWxMaSubscribeMessagePhraseData(name: String, value: String): WxMaSubscribeMessage.MsgData {
        return WxMaSubscribeMessage.MsgData(name, StringKit.removeNonBasicChinese(value).take(5))
    }

    fun newWxMaSubscribeMessageThingData(name: String, value: String): WxMaSubscribeMessage.MsgData {
        return WxMaSubscribeMessage.MsgData(name, value.take(20))
    }

    fun newWxMaSubscribeMessageNameData(name: String, value: String): WxMaSubscribeMessage.MsgData {
        return WxMaSubscribeMessage.MsgData(name, StringKit.removeNonBasicChinese(value).take(10))
    }

    private var wxInsurancePayService: WxInsurancePayService? = null

    @Synchronized
    fun getWxInsurancePayService(): WxInsurancePayService {
        if (wxInsurancePayService == null) {
            val wxPayService: WxPayService = getWxPayServiceForMenZhen()
            val wxMaService: WxMaService = SpringUtils.getBean(WxMaService::class.java)
            val wxPayConfig: WxPayConfig = WxPayConfig().apply {
                val originalPayConfig: WxPayConfig = wxPayService.config
                this.appId = originalPayConfig.appId
                this.mchId = originalPayConfig.mchId
                this.mchKey = WeixinMipSetting.ma.mipKey
                this.keyPath = originalPayConfig.keyPath
            }
            wxInsurancePayService = WxInsurancePayService(wxPayConfig, {
                wxMaService.getAccessToken(it)
            }, wxPayService)
        }
        return wxInsurancePayService!!
    }

    fun idCardOcr(file: File): WxOcrIdCardResult {
        val wxMaService: WxMaService = SpringUtils.getBean(WxMaService::class.java)
        val wxMaOcrService: WxOcrService = wxMaService.ocrService
        return wxMaOcrService.idCard(file)
    }

    fun refundWeixin(mipWxOrder: MipWxOrder): WxInsurancePayRefundResult {
        check(mipWxOrder.ownPayAmount > BigDecimal.ZERO) { "微信支付金额为0，无需退微信" }
        check(mipWxOrder.hospOutRefundNo.isNullOrBlank()) { "订单已退款" }
        check(!mipWxOrder.medTransactionId.isNullOrBlank()) { "订单未支付" }
        mipWxOrder.hospOutRefundNo = mipWxOrder.hospitalOutTradeNo + "#R"
        val refundRequest: WxInsurancePayRefundRequest = WxInsurancePayRefundRequest().apply {
            this.medTransId = mipWxOrder.medTransactionId
            this.hospOutRefundNo = mipWxOrder.hospOutRefundNo
            this.partRefundType = "CASH_ONLY"
            this.payOrderId = mipWxOrder.payOrderId
            this.refReason = "申请退款"
        }
        val refundResult: WxInsurancePayRefundResult = getWxInsurancePayService().refund(refundRequest)
        val mipWxOrderService: IMipWxOrderService = SpringUtils.getBean(IMipWxOrderService::class.java)
        mipWxOrderService.updateOnRefund(mipWxOrder, refundResult)

        return refundResult
    }

    fun refundWeixin(paymentId: Long, amount: BigDecimal): WxRefund {
        val wxPaymentService: IWxPaymentService = SpringUtils.getBean(IWxPaymentService::class.java)
        val wxRefundService: IWxRefundService = SpringUtils.getBean(IWxRefundService::class.java)

        val payment: WxPayment = wxPaymentService.selectWxPaymentById(paymentId)
            ?: throw IllegalArgumentException("订单不存在")
        val refundAmountCents = PaymentKit.yuanToFen(amount)
        check(refundAmountCents > 0) { "退款金额必须大于0" }
        check(refundAmountCents <= payment.unrefund) { "退款金额超出订单限制" }

        val refund: WxRefund = wxRefundService.createAndSave(payment, amount)
        wxPaymentService.updateOnRefund(payment, refundAmountCents)

        // 调用退款接口
        refund(
            totalAmount = refund.totalAmount.toInt(),
            amount = refund.amount.toInt(),
            wxPayNo = refund.wxPayNo,
            zyRefundNo = refund.zyRefundNo,
            wxPayService = getWxPayServiceByMchId(refund.mchId)!!
        )

        return refund
    }

}

private fun buildBillPath(billDate: LocalDate): String =
    Constants.WX_BILL_DIR + File.separatorChar + "${formatWxBillDate(billDate)}_wxbill.csv"

private fun buildMipBillPath(billDate: LocalDate): String =
    Constants.WX_BILL_DIR + File.separatorChar + "${formatWxBillDate(billDate)}_mipbill.csv"

private fun formatWxBillDate(billDate: LocalDate): String =
    billDate.format(DateTimeFormatters.PURE_DATE_FORMATTER)

private fun buildWxPayDownloadRequest(
    billDate: LocalDate,
    billType: String = "ALL",
    tarType: String? = null,
    deviceInfo: String? = null,
): WxPayDownloadBillRequest {
    val wxPayDownloadBillRequest = WxPayDownloadBillRequest()
    wxPayDownloadBillRequest.billType = billType
    wxPayDownloadBillRequest.billDate = formatWxBillDate(billDate)
    wxPayDownloadBillRequest.tarType = tarType
    wxPayDownloadBillRequest.deviceInfo = deviceInfo
    return wxPayDownloadBillRequest
}

fun WxPayServiceImpl.downloadAndSaveBill(
    billDate: LocalDate,
    billType: String = "ALL",
    tarType: String? = null,
    deviceInfo: String? = null,
): WxPayBillResult? {
    val wxPayDownloadBillRequest = buildWxPayDownloadRequest(billDate, billType, tarType, deviceInfo)
    val responseContent = this.downloadRawBill(wxPayDownloadBillRequest)
    FileUtil.writeUtf8String(responseContent, buildBillPath(billDate))
    return if (StrUtil.isEmpty(responseContent)) {
        null
    } else {
        WxPayBillResult.fromRawBillResultString(responseContent, wxPayDownloadBillRequest.billType)
    }
}

fun WxPayBillInfo.isMenzhen(): Boolean = outTradeNo.startsWith(ServiceType.MZ.name)
fun WxPayBillInfo.isZhuyuan(): Boolean = outTradeNo.startsWith(ServiceType.ZY.name)
fun WxPayBillInfo.isPay(): Boolean = this.refundId.isNullOrBlank() || this.refundId == "0"
fun WxPayBillInfo.isRefund(): Boolean = !this.isPay()
fun WxPayBillResult.calculateTotalPayAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isPay() }.map { it.totalFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateMenzhenPayAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isPay() && it.isMenzhen() }
        .map { it.totalFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateZhuyuanPayAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isPay() && it.isZhuyuan() }
        .map { it.totalFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateTotalRefundAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isRefund() }
        .map { it.settlementRefundFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateMenzhenRefundAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isRefund() && it.isMenzhen() }
        .map { it.settlementRefundFee.toBigDecimal() }
        .sumOf { it }

fun WxPayBillResult.calculateZhuyuanRefundAmount(appId: String): BigDecimal =
    (this.billInfoList ?: emptyList()).filter { appId == it.appId && it.isRefund() && it.isZhuyuan() }
        .map { it.settlementRefundFee.toBigDecimal() }
        .sumOf { it }