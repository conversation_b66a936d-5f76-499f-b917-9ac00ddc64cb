package space.lzhq.ph.ext

import com.alipay.api.response.AlipayTradeRefundApplyResponse
import com.ruoyi.common.utils.spring.SpringUtils
import org.dromara.hutool.http.client.HttpDownloader
import org.mospital.alipay.AlipayService
import org.mospital.jackson.DateTimeFormatters
import space.lzhq.ph.domain.MipAlipayOrder
import space.lzhq.ph.service.IMipAlipayOrderService
import java.io.File
import java.time.LocalDate

object AlipayExt {

    fun refundAlipay(mipAlipayOrder: MipAlipayOrder): AlipayTradeRefundApplyResponse {
        check(mipAlipayOrder.alipayRefundStatus.isNullOrBlank()) { "订单已退款" }
        check(!mipAlipayOrder.tradeNo.isNullOrBlank()) { "订单未支付" }
        val refundResponse: AlipayTradeRefundApplyResponse = AlipayService.refundMip(
            refundAmount = mipAlipayOrder.feeSumAmount,
            tradeNo = mipAlipayOrder.tradeNo,
            outTradeNo = mipAlipayOrder.outTradeNo,
            outRequestNo = mipAlipayOrder.outTradeNo + "#R",
            cancelSerialNo = mipAlipayOrder.outTradeNo,
            cancelBillNo = mipAlipayOrder.outTradeNo
        )
        if (refundResponse.isSuccess) {
            mipAlipayOrder.alipayOutRequestNo = refundResponse.outRequestNo
            mipAlipayOrder.alipayRefundStatus = refundResponse.refundStatus
        } else {
            mipAlipayOrder.alipayOutRequestNo = refundResponse.outRequestNo
            mipAlipayOrder.alipayRefundStatus = "FAIL"
        }

        val mipAlipayOrderService: IMipAlipayOrderService = SpringUtils.getBean(IMipAlipayOrderService::class.java)
        mipAlipayOrderService.updateById(mipAlipayOrder)

        return refundResponse
    }

    fun getOrDownloadBillFile(billDate: LocalDate): File {
        val path: String = AlipayService.buildBillPath(billDate)
        val file = File(path)
        if (file.exists()) {
            return file
        }

        val billDownloadUrlResponse = AlipayService.queryBillDownloadUrl(billDate)
        return if (billDownloadUrlResponse.isSuccess) {
            HttpDownloader.downloadFile(billDownloadUrlResponse.billDownloadUrl, file)
        } else if (billDownloadUrlResponse.subCode == "isp.bill_not_exist") {
            throw RuntimeException("${billDate.format(DateTimeFormatters.NORM_DATE_FORMATTER)}未发生交易")
        } else {
            throw RuntimeException("下载账单失败：${billDownloadUrlResponse.subCode}-${billDownloadUrlResponse.subMsg}")
        }
    }

}
