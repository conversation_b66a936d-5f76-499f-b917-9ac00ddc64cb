package space.lzhq.ph.ext

import com.ruoyi.common.constant.Constants
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.utils.spring.SpringUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.runBlocking
import org.dromara.hutool.core.date.DateUtil
import org.dromara.hutool.crypto.SecureUtil
import org.mospital.bsoft.*
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.service.IAlipayPaymentService
import space.lzhq.ph.service.IMedicalRecordApplicationService
import space.lzhq.ph.service.IWxPaymentService
import java.math.BigDecimal

object HisExt {

    private val NOT_CHINESE_REGEXP = Regex("[^一-\u9FFF]")

    fun matchesChinese(s1: String, s2: String): Boolean {
        return s1.replace(NOT_CHINESE_REGEXP, "") == s2.replace(NOT_CHINESE_REGEXP, "")
    }

    fun recharge(wxPaymentId: Long): Result<Recharge>? {
        val wxPaymentService: IWxPaymentService = SpringUtils.getBean(IWxPaymentService::class.java)
        synchronized("HisExt.recharge#${wxPaymentId}".intern()) {
            val payment = wxPaymentService.selectWxPaymentById(wxPaymentId)
            if (payment.wxTradeStatus != Constants.PAY_OK ||
                payment.hisTradeStatus == Constants.RECHARGE_OK ||
                (payment.exrefund ?: 0) > 0
            ) {
                return null
            }
            if (payment.isMenzhen) {
                val hisBillResult: Result<List<MenzhenBill>> = runBlocking {
                    BSoftService.me.getMenzhenBillList(
                        MenzhenBillForm(
                            patientId = payment.patientNo,
                        )
                    )
                }
                val hisBill = hisBillResult.data?.firstOrNull { it.orderNumber == payment.wxPayNo }
                if (hisBill != null) {
                    payment.hisReceiptNo = hisBill.orderNumber
                    payment.hisTradeStatus = Constants.RECHARGE_OK
                    wxPaymentService.updateWxPayment(payment)
                    return null
                }

                val rechargeResult: Result<Recharge> = runBlocking(Dispatchers.IO) {
                    BSoftService.me.menzhenRecharge(
                        form = MenzhenRechargeForm(
                            patientId = payment.patientNo,
                            patientCard = payment.jzCardNo,
                            orderNumber = payment.wxPayNo,
                            debitAmount = PaymentKit.fenToYuan(payment.amount),
                            batchNumber = payment.id.toString(),
                            operationType = "20",   // 掌医
                            payType = "16",         // 微信
                        )
                    )
                }
                wxPaymentService.updateOnRecharge(payment, rechargeResult)

                if (rechargeResult.isOk()) {
                    PaymentKit.CONTINUOUS_HIS_FAILURES.set(0)
                }

                return rechargeResult
            }

            if (payment.isZhenjian) {
                val amountYuan = PaymentKit.fenToYuan(payment.amount)
                val settlementResult: Result<MenzhenSettlement> = BSoftService.menzhenSettlement(
                    MenzhenSettlementForm(
                        applyNos = payment.settlementIds,
                        debitAmount = amountYuan.toString(),
                        orderNumber = payment.wxPayNo,
                        serialNumber = payment.wxPayNo,
                        serialNo = payment.wxPayNo,
                        patientId = payment.patientNo,
                        payType = "16",         // 微信
                        totalMoney = amountYuan.toString()
                    )
                )
                wxPaymentService.updateOnSettlement(payment, settlementResult)
                return null
            }

            if (payment.isZhuyuan) {
                val rechargeResult: Result<Recharge> = runBlocking {
                    BSoftService.me.zhuyuanRecharge(
                        form = ZhuyuanRechargeForm(
                            admissionNo = payment.zhuyuanNo,
                            orderNumber = payment.wxPayNo,
                            debitAmount = PaymentKit.fenToYuan(payment.amount),
                            operationType = "10",
                            payType = "20",         // 微信
                        )
                    )
                }
                wxPaymentService.updateOnRecharge(payment, rechargeResult)

                if (rechargeResult.isOk()) {
                    PaymentKit.CONTINUOUS_HIS_FAILURES.set(0)
                }

                return rechargeResult
            }

            if (payment.isBingli) {
                val applicationService = SpringUtils.getBean(IMedicalRecordApplicationService::class.java)
                val application = applicationService.getByZyPayNo(payment.zyPayNo) ?: return null
                applicationService.updatePaymentInfo(application.id, payment.wxPayNo)
            }
        }

        return null
    }

    fun rechargeByAlipay(alipayPaymentId: Long): Result<Recharge>? {
        val alipayPaymentService: IAlipayPaymentService = SpringUtils.getBean(IAlipayPaymentService::class.java)
        synchronized("HisExt.rechargeByAlipay#${alipayPaymentId}".intern()) {
            val payment = alipayPaymentService.selectAlipayPaymentById(alipayPaymentId)
            if (payment.tradeStatus != Constants.TRADE_SUCCESS) {
                return null
            }
            if (payment.hisTradeStatus == Constants.RECHARGE_OK) {
                return null
            }
            if ((payment.exrefund ?: 0) > 0) {
                return null
            }

            if (payment.isMenzhen) {
                val hisBillResult: Result<List<MenzhenBill>> = runBlocking {
                    BSoftService.me.getMenzhenBillList(
                        MenzhenBillForm(
                            patientId = payment.patientId,
                        )
                    )
                }
                val hisBill = hisBillResult.data?.firstOrNull { it.orderNumber == payment.outTradeNo }
                if (hisBill != null) {
                    payment.hisReceiptNo = hisBill.orderNumber
                    payment.hisTradeStatus = Constants.RECHARGE_OK
                    payment.hisSuccessTime = DateUtil.date(hisBill.operationDate)
                    alipayPaymentService.updateAlipayPayment(payment)
                    return null
                }

                val rechargeResult: Result<Recharge> = runBlocking(Dispatchers.IO) {
                    BSoftService.me.menzhenRecharge(
                        form = MenzhenRechargeForm(
                            patientId = payment.patientId,
                            patientCard = payment.jzCardNo,
                            orderNumber = payment.outTradeNo,
                            debitAmount = PaymentKit.fenToYuan(payment.totalAmount),
                            operationType = "20",   // 掌医
                            payType = "14",         // 支付宝
                        )
                    )
                }
                alipayPaymentService.updateOnRecharge(payment, rechargeResult)

                if (rechargeResult.isOk()) {
                    PaymentKit.CONTINUOUS_HIS_FAILURES.set(0)
                }

                return rechargeResult
            }

            if (payment.isZhenjian) {
                val amountYuan = PaymentKit.fenToYuan(payment.totalAmount)
                val settlementResult: Result<MenzhenSettlement> = BSoftService.menzhenSettlement(
                    MenzhenSettlementForm(
                        applyNos = payment.settlementIds,
                        debitAmount = amountYuan.toString(),
                        orderNumber = payment.outTradeNo,
                        serialNumber = payment.outTradeNo,
                        serialNo = payment.outTradeNo,
                        patientId = payment.patientId,
                        payType = "14",         // 支付宝
                        totalMoney = amountYuan.toString()
                    )
                )
                alipayPaymentService.updateOnSettlement(payment, settlementResult)
                return null
            }

            if (payment.isZhuyuan) {
                val rechargeResult: Result<Recharge> = runBlocking {
                    BSoftService.me.zhuyuanRecharge(
                        form = ZhuyuanRechargeForm(
                            admissionNo = payment.zhuyuanNo,
                            orderNumber = payment.outTradeNo,
                            debitAmount = PaymentKit.fenToYuan(payment.totalAmount),
                            operationType = "10",
                            payType = "15",         // 支付宝
                        )
                    )
                }
                alipayPaymentService.updateOnRecharge(payment, rechargeResult)

                if (rechargeResult.isOk()) {
                    PaymentKit.CONTINUOUS_HIS_FAILURES.set(0)
                }

                return rechargeResult
            }

            return null

        }
    }

    fun menzhenSettle(
        patientId: String,
        settlementIds: String,
        amount: BigDecimal,
        orderNo: String
    ): Result<MenzhenSettlement> {
        val settlementResult: Result<MenzhenSettlement> = BSoftService.menzhenSettlement(
            MenzhenSettlementForm(
                applyNos = settlementIds,
                debitAmount = amount.toString(),
                orderNumber = orderNo,
                serialNo = orderNo,
                patientId = patientId,
                payType = "12",         // 就诊卡
                totalMoney = amount.toString()
            )
        )
        return settlementResult
    }

    private val AES_KEY = "bsfot@POIU12++=="
    private val QRCODE_PREFIX = "ET"

    fun encryptForQrCode(s: String): String {
        val encryptedString = SecureUtil.aes(AES_KEY.toByteArray(Charsets.UTF_8)).encryptBase64(s)
        return "$QRCODE_PREFIX$encryptedString"
    }
}

fun <T> Result<T>.toAjaxResult(): AjaxResult =
    if (isOk()) {
        AjaxResult.success(this.data)
    } else {
        AjaxResult.error(this.message)
    }
