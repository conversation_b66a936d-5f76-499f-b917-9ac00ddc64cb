package space.lzhq.ph.ext

import com.tencentcloudapi.common.Credential
import com.tencentcloudapi.ocr.v20181119.OcrClient
import com.tencentcloudapi.ocr.v20181119.models.ResidenceBookletOCRRequest
import com.tencentcloudapi.ocr.v20181119.models.ResidenceBookletOCRResponse
import org.dromara.hutool.core.codec.binary.Base64
import java.io.File

object TencentCloudApi {

    private val ocrClient: OcrClient by lazy {
        val credential = Credential("AKIDRkqjOr79yZzB8rOiotnV0GJ9mBMJ4cx5", "0oOn8LK6mp25xc3GF0bzCBwr9HBsZ3JV")
        OcrClient(credential, "")
    }

    fun residenceBookletOCR(file: File): ResidenceBookletOCRResponse {
        val request = ResidenceBookletOCRRequest().apply {
            this.imageBase64 = Base64.encode(file)
        }
        return ocrClient.ResidenceBookletOCR(request)
    }

}