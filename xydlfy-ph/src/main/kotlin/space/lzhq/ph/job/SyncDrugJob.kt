package space.lzhq.ph.job

import kotlinx.coroutines.runBlocking
import org.mospital.bsoft.BSoftService
import org.mospital.bsoft.Drug
import org.mospital.bsoft.DrugForm
import org.mospital.bsoft.Result
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.service.IDrugService

/**
 * 同步药品
 * 调用目标字符串：syncDrugJob.sync()
 * cron表达式：0 45 1 * * ?
 */
@Component("syncDrugJob")
class SyncDrugJob {

    @Autowired
    private lateinit var drugService: IDrugService

    fun sync() {
        val drugResult: Result<List<Drug>> = runBlocking {
            BSoftService.me.getDrugs(DrugForm())
        }
        if (!drugResult.isOk() || drugResult.data.isNullOrEmpty()) {
            return
        }

        drugService.clearAll()

        val drugs = drugResult.data!!.map { space.lzhq.ph.domain.Drug(it) }
        drugService.insertDrugs(drugs)
    }

}