package space.lzhq.ph.job

import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult
import com.github.binarywang.wxpay.service.WxPayService
import org.mospital.alipay.AlipayService.queryOrder
import org.mospital.jackson.DateTimeFormatters
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.domain.AlipayPayment
import space.lzhq.ph.domain.WxPayment
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.IAlipayPaymentService
import space.lzhq.ph.service.IWxPaymentService
import java.time.LocalDateTime

/**
 * 自动充值
 * 表达式：0 0/2 * * * ?
 * <AUTHOR>
 */
@Component("autoRechargeJob")
class AutoRechargeJob {

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var alipayPaymentService: IAlipayPaymentService

    fun execute() {
        val timeRange = buildTimeRange()

        refreshOrderStatusForWxPayments(timeRange)
        rechargeForWxPayments(timeRange)

        refreshOrderStatusForAliPayments(timeRange)
        rechargeForAliPayments(timeRange)
    }

    private fun buildTimeRange(): Array<String> {
        val beginCreateTime = LocalDateTime.now().minusDays(1).format(DateTimeFormatters.NORM_DATETIME_FORMATTER)
        val endCreateTime = LocalDateTime.now().minusMinutes(3).format(DateTimeFormatters.NORM_DATETIME_FORMATTER)
        return arrayOf(beginCreateTime, endCreateTime)
    }

    private fun refreshOrderStatusForWxPayments(timeRange: Array<String>) {
        val params = mutableMapOf<String, Any>(
            "beginCreateTime" to timeRange[0],
            "endCreateTime" to timeRange[1],
            "wxTradeStatusIsUnknown" to true
        )
        val payments: List<WxPayment> = wxPaymentService.selectWxPaymentList(WxPayment().apply {
            this.params = params
        })
        payments.forEach {
            val wxPayService: WxPayService = WeixinExt.getWxPayServiceByMchId(it.mchId)
                ?: return@forEach
            val wxPayOrderQueryResult: WxPayOrderQueryResult = wxPayService.queryOrder(null, it.zyPayNo)
            wxPaymentService.updateOnPay(it, wxPayOrderQueryResult)
        }
    }

    private fun rechargeForWxPayments(timeRange: Array<String>) {
        val params = mutableMapOf<String, Any>(
            "beginCreateTime" to timeRange[0],
            "endCreateTime" to timeRange[1],
            "filterErrorOrder" to true
        )

        val payments: List<WxPayment> = wxPaymentService.selectWxPaymentList(WxPayment().apply {
            this.params = params
        })
        payments.forEach {
            HisExt.recharge(it.id)
        }
    }

    private fun refreshOrderStatusForAliPayments(timeRange: Array<String>) {
        val params = mutableMapOf<String, Any>(
            "beginCreateTime" to timeRange[0],
            "endCreateTime" to timeRange[1],
            "tradeStatusIsUnknown" to true
        )
        val payments: List<AlipayPayment> = alipayPaymentService.selectAlipayPaymentList(AlipayPayment().apply {
            this.params = params
        })
        payments.forEach {
            val queryOrderResponse = queryOrder(
                outTradeNo = it.getOutTradeNo(),
                tradeNo = "",
                queryOptions = listOf("fund_bill_list")
            )
            alipayPaymentService.updateOnPay(it, queryOrderResponse)
        }
    }

    private fun rechargeForAliPayments(timeRange: Array<String>) {
        val params = mutableMapOf<String, Any>(
            "beginCreateTime" to timeRange[0],
            "endCreateTime" to timeRange[1],
            "filterErrorOrder" to true
        )

        val payments: List<AlipayPayment> = alipayPaymentService.selectAlipayPaymentList(AlipayPayment().apply {
            this.params = params
        })
        payments.forEach {
            HisExt.rechargeByAlipay(it.id)
        }
    }

}
