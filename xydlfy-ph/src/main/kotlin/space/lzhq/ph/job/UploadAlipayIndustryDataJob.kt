package space.lzhq.ph.job

import com.alipay.api.domain.DepartmentData
import com.alipay.api.domain.HospitalData
import org.dromara.hutool.core.net.url.UrlEncoder
import org.mospital.alipay.AlipayService
import org.mospital.alipay.AlipaySetting
import org.mospital.bsoft.BSoftService
import org.mospital.bsoft.ReservationDept
import org.mospital.bsoft.Result
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/**
 * 向支付宝上传医疗行业数据
 * 表达式：0 0 4 * * ?
 */
@Component("uploadAlipayIndustryDataJob")
class UploadAlipayIndustryDataJob {

    private val logger: Logger = LoggerFactory.getLogger(UploadAlipayIndustryDataJob::class.java)

    fun execute() {
        try {
            uploadHospitalData()
        } catch (e: Exception) {
            logger.warn(e.message, e)
        }

        try {
            uploadDepartmentsData()
        } catch (e: Exception) {
            logger.warn(e.message, e)
        }
    }

    private fun uploadHospitalData() {
        val hosptialData: HospitalData = HospitalData().also {
            it.hospitalId = "1"
            it.hospitalName = AlipaySetting.maHospitalName
            it.hospitalAlias = "新医大六附院"
            it.hospitalStandardCode = "H65010200028"
            it.hospitalProvince = "新疆维吾尔自治区"
            it.hospitalCity = "乌鲁木齐市"
            it.hospitalDistrict = "天山区"
            it.hospitalAddr = "新疆维吾尔自治区乌鲁木齐市天山区五星南路39号"
            it.hospitalLat = "43.809554"
            it.hospitalLgt = "87.630925"
            it.hospitalType = "公立"
            it.hospitalGrade = "三级"
            it.hospitalWorkTime = ""
            it.hospitalTel = ""
            it.hospitalLogo = "https://appstoreisvpic.alipayobjects.com/prod/4c2626e0-bc27-4b3a-a508-ee6fe23baa3f.png"
            it.countryKeyDepartment = ""
            it.provinceKeyDepartment = ""
            it.keyDepartment = ""
            it.hospitalOptag = ""
        }
        AlipayService.uploadHospitalMedicalIndustryData(listOf(hosptialData))
    }

    private fun uploadDepartmentsData() {
        val deptListResult: Result<List<ReservationDept>> = BSoftService.getReservationDeptList()
        if (!deptListResult.isOk()) {
            logger.warn(deptListResult.message)
            return
        }

        val deptList: List<ReservationDept> = deptListResult.data ?: return
        val hospitalName: String = AlipaySetting.maHospitalName
        val appId: String = AlipaySetting.maAppId
        val departmentDataList: List<DepartmentData> = deptList.map { dept ->
            DepartmentData().also { departmentData ->
                departmentData.departmentId = dept.deptId
                departmentData.departmentName = dept.deptName.removePrefix("门")
                departmentData.departmentType = "一级科室"
                departmentData.hospitalName = hospitalName

                val page = "pages_yuyue/PaibanYisheng/PaibanYisheng"
                val query: String =
                    UrlEncoder.encodeAll("departmentId=${dept.deptId}&departmentName=${departmentData.departmentName}")
                departmentData.departmentUrl = "alipays://platformapi/startapp?appId=$appId&page=$page&query=$query"
            }
        }
        AlipayService.uploadDepartmentMedicalIndustryData(departmentDataList)
    }

}
