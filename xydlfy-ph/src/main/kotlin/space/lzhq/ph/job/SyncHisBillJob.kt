package space.lzhq.ph.job

import org.mospital.bsoft.BSoftService
import org.mospital.bsoft.BillDetail
import org.mospital.bsoft.BillDetailForm
import org.mospital.bsoft.Result
import org.mospital.common.IdUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.service.IAlipayCheckService
import space.lzhq.ph.service.IWxCheckService
import java.time.LocalDate

@Component("syncHisBillJob")
class SyncHisBillJob {

    @Autowired
    private lateinit var wxCheckService: IWxCheckService

    @Autowired
    private lateinit var alipayCheckService: IAlipayCheckService

    fun sync() {
        val billDate = LocalDate.now().minusDays(1)
        val billResponse: Result<List<BillDetail>> = BSoftService.getBillDetails(
            BillDetailForm(
                startDate = billDate,
                endDate = LocalDate.now(),
            )
        )
        try {
            wxCheckService.syncHisBill(billDate, billResponse)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        try {
            alipayCheckService.syncHisBill(IdUtil.localDateToId(billDate), billResponse)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}
