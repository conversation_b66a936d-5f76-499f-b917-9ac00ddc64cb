package space.lzhq.ph.job

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage
import com.ruoyi.common.constant.Constants
import org.dromara.hutool.log.LogFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.configuration.WxSubscribeMessageConfiguration
import space.lzhq.ph.domain.Reservation
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.IPatientService
import space.lzhq.ph.service.IReservationService
import space.lzhq.ph.service.OutpatientStopClinicService
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * 每天20:30发送次日就诊提醒
 * 表达式：0 30 20 * * ?
 */
@Component("sendVisitingReminderJob")
class SendVisitingReminderJob {

    private val log = LogFactory.getLog(SendVisitingReminderJob::class.java)

    @Autowired
    private lateinit var subscribeMessageConfiguration: WxSubscribeMessageConfiguration

    @Autowired
    private lateinit var reservationService: IReservationService

    @Autowired
    private lateinit var stopClinicService: OutpatientStopClinicService

    @Autowired
    private lateinit var patientService: IPatientService


    fun execute() {
        log.info("开始执行发送次日就诊提醒任务")

        // 获取次日日期
        val tomorrow = LocalDate.now().plusDays(1)

        try {
            // 查询次日有效预约（预约成功且未取消）
            val validReservations: List<Reservation> = reservationService.getValidReservationsByDate(tomorrow)
            log.info("次日有效预约数量: {}", validReservations.size)

            // 遍历预约并发送提醒
            validReservations.forEach { reservation ->
                // 已停诊的预约不发送提醒
                val stopClinic = stopClinicService.getOneByAppointmentId(reservation.reservationNumber)
                if (stopClinic != null) {
                    return@forEach
                }

                // 只给微信用户发送提醒
                if (!reservation.openid.startsWith(Constants.ALIPAY_USER_ID_PREFIX)) {
                    sendVisitingReminder(reservation)
                }
            }

            log.info("完成发送次日就诊提醒任务")
        } catch (e: Exception) {
            log.error("发送次日就诊提醒任务异常", e)
        }
    }

    /**
     * 发送就诊提醒消息
     */
    private fun sendVisitingReminder(reservation: Reservation) {
        val visitTimeStr =
            reservation.jzTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))

        WeixinExt.sendSubscribeMessage(
            messageId = subscribeMessageConfiguration.visitingReminderId,
            messagePage = subscribeMessageConfiguration.visitingReminderPage,
            messageDataList = listOf(
                // 就诊人
                WeixinExt.newWxMaSubscribeMessageNameData("name1", reservation.name),
                // 就诊时间
                WxMaSubscribeMessage.MsgData("date2", visitTimeStr),
                // 就诊科室
                WeixinExt.newWxMaSubscribeMessageThingData("thing3", reservation.departmentName),
                // 科室医生
                WeixinExt.newWxMaSubscribeMessageThingData("thing10", reservation.doctorName),
                // 温馨提示
                WeixinExt.newWxMaSubscribeMessageThingData("thing5", "请您及时前往医院就诊")
            ),
            openid = reservation.openid,
            companionId = "ph_reservation#${reservation.id}"
        )
    }
} 