package space.lzhq.ph.job

import kotlinx.coroutines.runBlocking
import org.mospital.bsoft.BSoftService
import org.mospital.bsoft.Material
import org.mospital.bsoft.MaterialForm
import org.mospital.bsoft.Result
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.service.IMaterialService

/**
 * 同步材料
 * 调用目标字符串：syncMaterialJob.sync()
 * cron表达式：0 40 1 * * ?
 */
@Component("syncMaterialJob")
class SyncMaterialJob {

    @Autowired
    private lateinit var materialService: IMaterialService

    fun sync() {
        val materialResult: Result<List<Material>> = runBlocking {
            BSoftService.me.getMaterials(MaterialForm())
        }
        if (!materialResult.isOk() || materialResult.data.isNullOrEmpty()) {
            return
        }

        materialService.clearAll()

        val materials = materialResult.data!!.map { it -> space.lzhq.ph.domain.Material(it) }
        materialService.insertMaterials(materials)
    }

}