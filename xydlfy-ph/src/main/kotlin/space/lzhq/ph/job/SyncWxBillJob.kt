package space.lzhq.ph.job

import com.github.binarywang.wxpay.bean.result.WxPayBillResult
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.IWxCheckService
import java.time.LocalDate

@Component("syncWxBillJob")
open class SyncWxBillJob {

    @Autowired
    private lateinit var wxCheckService: IWxCheckService

    open fun sync() {
        val billDate = LocalDate.now().minusDays(1)
        val wxPayBillResult: WxPayBillResult? = WeixinExt.downloadAndSaveWxBill(billDate)
        wxCheckService.syncWxBill(billDate, wxPayBillResult)
    }
}
