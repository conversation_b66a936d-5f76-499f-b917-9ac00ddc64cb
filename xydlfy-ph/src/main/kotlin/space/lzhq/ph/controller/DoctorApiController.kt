package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.domain.Doctor
import space.lzhq.ph.model.DoctorWrapper
import space.lzhq.ph.service.IDoctorService

@RestController
@RequestMapping("/open/doctor")
class DoctorApiController : BaseController() {

    @Autowired
    private lateinit var doctorService: IDoctorService

    @GetMapping("/list")
    @ResponseBody
    fun list(doctorWrapper: DoctorWrapper): AjaxResult {
        startPage("sort_no desc")
        val doctors = doctorService.selectDoctorList(doctorWrapper)
        return AjaxResult.success(getDataTable(doctors))
    }

    @GetMapping("/show/{id}")
    @ResponseBody
    fun show(@PathVariable id: String): AjaxResult {
        val doctor: Doctor? = doctorService.selectDoctorById(id)

        return if (doctor == null) {
            AjaxResult.error("找不到指定的医生")
        } else {
            AjaxResult.success(doctor)
        }
    }

}