package space.lzhq.ph.controller

import com.alipay.api.response.AlipayEcoCityserviceCityserviceEnergySendResponse
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.IO
import kotlinx.coroutines.runBlocking
import org.mospital.alipay.AlipayService
import org.mospital.alipay.EnergyScene
import org.mospital.bsoft.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.IPacsReportService

@RestController
@RequestMapping("/api/report")
class ReportApiController : BaseController() {

    @Autowired
    private lateinit var pacsReportService: IPacsReportService

    /**
     * 获取门诊的LIS报告
     * @param pageNo 第几页，默认为1
     * @param pageSize 每页几条，默认为100
     */
    @GetMapping("mzlis")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun mzlis(): AjaxResult {
        val activePatient = request.getCurrentPatient()
        return lis(patientId = activePatient.patientNo, type = "1")
    }


    /**
     * 获取LIS报告详单
     * @param reportId 报告单号
     */
    @GetMapping("lisDetail")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun lisDetail(
        @RequestParam sampleNo: String,
        @RequestParam aliAuthCode: String?,
    ): AjaxResult {
        val activePatient = request.getCurrentPatient()
        val result: Result<List<InspectionReport>> = BSoftService.getInspectionReports(
            InspectionReportForm(
                patientId = activePatient.patientNo,
                reportNo = sampleNo
            )
        )

        var alipayEnergy: Long = 0L
        if (result.isOk() && result.data!!.isNotEmpty() && activePatient.clientTypeEnum == ClientType.ALIPAY && !aliAuthCode.isNullOrBlank()) {
            val energySendResponse: AlipayEcoCityserviceCityserviceEnergySendResponse =
                AlipayService.sendEnergy(
                    authCode = aliAuthCode,
                    scene = EnergyScene.HOINQUIRE,
                    outerNo = "LIS#$sampleNo"
                )
            if (energySendResponse.isSuccess) {
                alipayEnergy = energySendResponse.totalEnergy
            }
        }
        return AjaxResult.success().data(
            mapOf(
                "reportItems" to result.data,
                "alipayEnergy" to alipayEnergy
            )
        )
    }

    /**
     * 获取PACS报告详单
     * @param reportId 报告单号
     */
    @GetMapping("pacsDetail")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun pacsDetail(
        @RequestParam reportId: String,
        @RequestParam aliAuthCode: String?,
    ): AjaxResult {
        val activePatient = request.getCurrentPatient()
        var alipayEnergy: Long = 0L
        if (activePatient.clientTypeEnum == ClientType.ALIPAY && !aliAuthCode.isNullOrBlank()) {
            val energySendResponse: AlipayEcoCityserviceCityserviceEnergySendResponse =
                AlipayService.sendEnergy(
                    authCode = aliAuthCode,
                    scene = EnergyScene.HOINQUIRE,
                    outerNo = "PACS#" + reportId
                )
            if (energySendResponse.isSuccess) {
                alipayEnergy = energySendResponse.totalEnergy
            }
        }
        return AjaxResult.success().data(
            mapOf(
                "alipayEnergy" to alipayEnergy
            )
        )
    }

    /**
     * 获取门诊的PACS报告
     * @param pageNo 第几页，默认为1
     * @param pageSize 每页几条，默认为100
     */
    @GetMapping("mzpacs")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun mzpacs(
        @RequestParam(required = false, defaultValue = "1") pageNo: Int,
        @RequestParam(required = false, defaultValue = "100") pageSize: Int,
    ): AjaxResult {
        val activePatient = request.getCurrentPatient()
        return pacs(patientId = activePatient.patientNo, source = "1")
    }

     fun lis(patientId: String, type: String): AjaxResult {
        val result: Result<List<InspectionRecord>> = BSoftService.getInspectionRecords(
            InspectionRecordForm(
                patientId = patientId,
                stayHospitalMode = type
            )
        )

        return if (result.isOk()) {
            // 状态为4、5、6的，可以查看报告
            AjaxResult.success(result.data?.filter { it.resultstatus in listOf("4", "5", "6") }
                ?: emptyList<InspectionRecord>())
        } else {
            AjaxResult.success(emptyList<InspectionRecord>())
        }

    }

    fun pacs(patientId: String, source: String): AjaxResult {
        val response = runBlocking(Dispatchers.IO) {
            BSoftService.getListCheckReport(
                ListCheckForm(
                    patientId = patientId,
                    source = source
                )
            )
        }
        if (!response.isOk()) {
            return AjaxResult.error(response.message)
        }
        val pacsReports = response.data ?: return AjaxResult.error("获取PACS报告失败")
        return AjaxResult.success(pacsReportService.fillReportLinks(pacsReports))
    }

}
