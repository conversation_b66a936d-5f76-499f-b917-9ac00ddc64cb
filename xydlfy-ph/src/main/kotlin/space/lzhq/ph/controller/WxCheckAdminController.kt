package space.lzhq.ph.controller

import com.github.binarywang.wxpay.bean.request.WxInsurancePayDownloadBillRequest
import com.github.binarywang.wxpay.bean.result.WxPayBillResult
import com.ruoyi.common.annotation.Excel
import com.ruoyi.common.annotation.Log
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.common.enums.BusinessType
import com.ruoyi.common.utils.poi.ExcelUtil
import jakarta.servlet.http.HttpServletResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import org.apache.shiro.authz.annotation.Logical
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.dromara.hutool.core.io.file.FileUtil
import org.dromara.hutool.http.client.HttpDownloader
import org.mospital.bsoft.BSoftService
import org.mospital.bsoft.BillDetail
import org.mospital.bsoft.BillDetailForm
import org.mospital.bsoft.Result
import org.mospital.common.IdUtil
import org.mospital.jackson.DateTimeFormatters
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.ModelAndView
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import space.lzhq.ph.domain.WxCheck
import space.lzhq.ph.domain.WxCheckReport
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.IWxCheckService
import java.math.BigDecimal
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * 对账Controller
 *
 * <AUTHOR>
 * @date 2020-06-09
 */
@Controller
@RequestMapping("/ph/check")
class WxCheckAdminController : BaseController() {
    private val prefix = "ph/check"

    @Autowired
    private lateinit var wxCheckService: IWxCheckService

    @RequiresPermissions("ph:check:view")
    @GetMapping
    fun check(modelMap: ModelMap): String {
        modelMap.addAttribute("user", sysUser);
        return "ph/check/check"
    }

    @RequiresPermissions("ph:check:reportView")
    @GetMapping("/report")
    fun report(): String {
        return "ph/check/report"
    }

    /**
     * 查询对账列表
     */
    @RequiresPermissions("ph:check:list")
    @PostMapping("/list")
    @ResponseBody
    fun list(wxCheck: WxCheck?): TableDataInfo {
        startPage()
        val list = wxCheckService.selectWxCheckList(wxCheck)
        return getDataTable(list)
    }

    @RequiresPermissions("ph:check:reportList")
    @PostMapping("/reportList")
    @ResponseBody
    fun reportList(wxCheck: WxCheck?): TableDataInfo {
        startPage()
        val list: List<WxCheck> = wxCheckService.selectWxCheckList(wxCheck)
        return getDataTable(list).apply { rows = list.map(::WxCheckReport) }
    }

    /**
     * 导出对账列表
     */
    @RequiresPermissions("ph:check:export")
    @Log(title = "对账", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    fun export(wxCheck: WxCheck?): AjaxResult {
        val list = wxCheckService.selectWxCheckList(wxCheck)
        val util = ExcelUtil(WxCheck::class.java)
        return util.exportExcel(list, "check")
    }

    /**
     * 修改保存对账
     */
    @RequiresPermissions("ph:check:edit")
    @Log(title = "对账", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    fun editSave(wxCheck: WxCheck?): AjaxResult {
        return toAjax(wxCheckService.updateWxCheck(wxCheck))
    }

    /**
     * 同步微信账单
     */
    @RequiresPermissions("ph:check:syncWxBill")
    @PostMapping("/syncWxBill/{id}")
    @ResponseBody
    fun syncWxBill(@PathVariable id: Long): AjaxResult {
        val billDate = IdUtil.idToLocalDate(id)
        val wxPayBillResult: WxPayBillResult? = WeixinExt.downloadAndSaveWxBill(billDate)
        val ok = wxCheckService.syncWxBill(billDate, wxPayBillResult)
        return toAjax(ok)
    }

    /**
     * 下载微信账单
     */
    @RequiresPermissions("ph:check:downloadWxBill")
    @GetMapping("/downloadWxBill/{id}")
    @ResponseBody
    fun downloadWxBill(@PathVariable("id") id: Long): ResponseEntity<StreamingResponseBody> {
        val billDate = IdUtil.idToLocalDate(id)
        val billFile = WeixinExt.getOrDownloadBillFile(billDate)
        return ResponseEntity.ok()
            .contentType(MediaType.parseMediaType("text/csv"))
            .contentLength(billFile.length())
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=${billFile.name}")
            .body(StreamingResponseBody {
                try {
                    FileUtil.writeToStream(billFile, it)
                } catch (e: Exception) {
                    it.write("Unknown error occurred".toByteArray())
                }
            })
    }

    /**
     * 同步微信医保账单
     */
    @RequiresPermissions("ph:check:syncWxMipBill")
    @PostMapping("/syncWxMipBill/{id}")
    @ResponseBody
    fun syncWxMipBill(@PathVariable id: Long): AjaxResult {
        val billDate = IdUtil.idToLocalDate(id)
        val billFile = WeixinExt.getOrDownloadMipBillFile(billDate)
        val ok = wxCheckService.syncWxMipBill(billDate, billFile)
        return toAjax(ok)
    }

    /**
     * 下载微信医保账单
     */
    @RequiresPermissions(
        value = ["ph:check:downloadWxMipBill", "ph:check:reportDownloadWxMipBill"],
        logical = Logical.OR
    )
    @GetMapping("/downloadWxMipBill/{id}")
    @ResponseBody
    fun downloadWxMipBill(@PathVariable id: Long): ResponseEntity<StreamingResponseBody> {
        val billDate = IdUtil.idToLocalDate(id).format(DateTimeFormatter.ofPattern("yyyyMMdd"))
        val wxInsurancePayDownloadBillResult = WeixinExt.getWxInsurancePayService().downloadBill(
            WxInsurancePayDownloadBillRequest(
                billDate = billDate,
                billType = "ALL"
            )
        )
        return ResponseEntity.ok()
            .contentType(MediaType.parseMediaType("text/csv"))
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=weixin_yibao_bill_${billDate}.csv")
            .body(StreamingResponseBody {
                HttpDownloader.download(wxInsurancePayDownloadBillResult.downloadUrl, it, true, null)
            })
    }

    /**
     * 同步HIS账单
     */
    @RequiresPermissions("ph:check:syncHisBill")
    @PostMapping("/syncHisBill/{id}")
    @ResponseBody
    fun syncHisBill(@PathVariable id: Long): AjaxResult {
        val billDate = IdUtil.idToLocalDate(id)
        val billResponse: Result<List<BillDetail>> = runBlocking(Dispatchers.IO) {
            BSoftService.getBillDetails(
                BillDetailForm(
                    startDate = LocalDate.now().minusDays(1),
                    endDate = LocalDate.now(),
                )
            )
        }
        return wxCheckService.syncHisBill(billDate, billResponse)
    }

    data class HisBill(
        @Excel(name = "交易类型")
        val paymentType: String,

        @Excel(name = "支付方式")
        val paymentMethod: String,

        @Excel(name = "患者索引号")
        val patientId: String,

        @Excel(name = "交易时间", width = 24.0)
        val orderTime: String,

        @Excel(name = "订单号", width = 32.0)
        val orderNo: String,

        @Excel(name = "交易金额")
        val amount: BigDecimal,


        ) {
        companion object {
            fun convertFrom(bill: BillDetail): HisBill = HisBill(
                paymentType = bill.paymentType,
                paymentMethod = "",
                patientId = bill.patientId,
                orderTime = bill.accountTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                orderNo = bill.platformTransId,
                amount = bill.paymentMoney
            )
        }
    }

    /**
     * 下载HIS账单
     */
    @RequiresPermissions("ph:check:downloadHisBill")
    @GetMapping("/downloadHisBill/{id}")
    @ResponseBody
    fun downloadHisBill(@PathVariable id: Long, response: HttpServletResponse) {
        val billDate = IdUtil.idToLocalDate(id)
        val billResponse: Result<List<BillDetail>> = runBlocking(Dispatchers.IO) {
            BSoftService.getBillDetails(
                BillDetailForm(
                    startDate = billDate,
                    endDate = billDate.plusDays(1),
                )
            )
        }
        return if (billResponse.isOk()) {
            val bills: List<BillDetail> = billResponse.data ?: emptyList()
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=hisbill_${id}_.xlsx")
            response.contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            ExcelUtil(HisBill::class.java).apply {
                this.init(bills.map(HisBill.Companion::convertFrom), "Sheet1", "", Excel.Type.EXPORT)
            }.exportExcel(response)
        } else {
            response.sendError(500, billResponse.message)
        }
    }

    data class DiffOrder(
        var orderType: String,
        var patientId: String,
        var orderNo: String,
        var wxOrderTime: String,
        var hisOrderTime: String,
        var wxAmount: BigDecimal,
        var hisAmount: BigDecimal,
        var diffAmount: BigDecimal,
    )

    /**
     * 显示错单
     */
    @RequiresPermissions("ph:check:showDiffOrders")
    @GetMapping("/showDiffOrders/{id}")
    fun showDiffOrders(@PathVariable id: Long): ModelAndView {
        val billDate = IdUtil.idToLocalDate(id)
        return ModelAndView(
            "$prefix/diffOrders",
            mapOf("id" to id, "billDate" to billDate.format(DateTimeFormatters.NORM_DATE_FORMATTER))
        )
    }

}
