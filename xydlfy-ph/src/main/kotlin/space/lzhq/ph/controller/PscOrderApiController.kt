package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.mospital.jackson.DateTimeFormatters
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequirePscCarerSession
import space.lzhq.ph.annotation.RequirePscNurseSession
import space.lzhq.ph.domain.PscCarer
import space.lzhq.ph.domain.PscCarerSession
import space.lzhq.ph.domain.PscOrder
import space.lzhq.ph.ext.getCurrentPscCarer
import space.lzhq.ph.ext.getCurrentPscNurse
import space.lzhq.ph.service.IPscOrderService
import java.time.LocalDateTime
import java.util.*

@RestController
@RequestMapping("/api/psc/order")
class PscOrderApiController : BaseController() {

    @Autowired
    private lateinit var pscOrderService: IPscOrderService

    @PostMapping
    @RequirePscNurseSession
    fun create(@RequestBody pscOrder: PscOrder): AjaxResult {
        val pscNurse = request.getCurrentPscNurse()
        pscOrder.nurseEmployeeNo = pscNurse.employeeNo
        pscOrder.nurseName = pscNurse.name
        pscOrder.nurseMobile = pscNurse.mobile
        pscOrder.examItemsCount = pscOrder.calculateExamItemCount()
        pscOrder.createTime = Date()
        pscOrder.status = PscOrder.STATUS_INIT
        pscOrderService.insertPscOrder(pscOrder)
        return AjaxResult.success(pscOrder)
    }

    @GetMapping("/byNurse")
    @RequirePscNurseSession
    fun listByNurse(
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") beginCreateTime: LocalDateTime,
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") endCreateTime: LocalDateTime,
    ): AjaxResult {
        val pscNurse = request.getCurrentPscNurse()
        val pscOrder = PscOrder().apply {
            this.nurseEmployeeNo = pscNurse.employeeNo
            this.params = mapOf(
                "beginCreateTime" to beginCreateTime.format(DateTimeFormatters.NORM_DATETIME_FORMATTER),
                "endCreateTime" to endCreateTime.format(DateTimeFormatters.NORM_DATETIME_FORMATTER),
            )
        }
        val list = pscOrderService.selectPscOrderList(pscOrder).sortedByDescending { it.createTime }
        return AjaxResult.success(list)
    }

    @GetMapping("/byCarer")
    @RequirePscCarerSession
    fun listByCarer(
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") beginCreateTime: LocalDateTime,
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") endCreateTime: LocalDateTime,
        @RequestParam(required = false) patientDepartment: String?,
        @RequestParam(required = false) examItemCode: String?,
    ): AjaxResult {
        val pscCarer: PscCarerSession = request.getCurrentPscCarer()
        val list: List<PscOrder> = pscOrderService.selectPscOrderListByCarer(
            pscCarer.employeeNo,
            patientDepartment,
            examItemCode,
            beginCreateTime,
            endCreateTime
        ).sortedBy { it.createTime }
        return AjaxResult.success(list)
    }

    @GetMapping("/departments")
    fun getDepartments(): AjaxResult {
        val departments: MutableList<String> = pscOrderService.selectDepartments()
        return AjaxResult.success(departments)
    }

    @PostMapping("/take/{orderId}")
    @RequirePscCarerSession
    fun take(
        @PathVariable orderId: Long,
    ): AjaxResult {
        val pscCarer = request.getCurrentPscCarer()
        val pscOrder = pscOrderService.selectPscOrderById(orderId)
            ?: return AjaxResult.error("工单不存在或已删除")
        if (pscOrder.status != PscOrder.STATUS_INIT && pscOrder.status != PscOrder.STATUS_TAKING) {
            return AjaxResult.error("此工单目前不可接单")
        }

        pscOrder.status = PscOrder.STATUS_READY
        pscOrder.takeTime = Date()
        pscOrder.carerEmployeeNo = pscCarer.employeeNo
        pscOrder.carerName = pscCarer.name
        pscOrder.carerMobile = pscCarer.mobile

        val updateRows = pscOrderService.updatePscOrder(pscOrder);
        if (updateRows == 1) {
            return AjaxResult.success(pscOrder)
        } else {
            return AjaxResult.error("操作失败")
        }
    }

    @PostMapping("/takeBatch")
    @RequirePscCarerSession
    fun takeBatch(
        @RequestParam orderIds: String,
    ): AjaxResult {
        val ids = orderIds.split(",").toTypedArray()
        if (ids.isEmpty()) {
            return AjaxResult.error("请选择工单")
        }

        val pscCarerSession = request.getCurrentPscCarer()
        val pscCarer = PscCarer().apply {
            this.employeeNo = pscCarerSession.employeeNo
            this.name = pscCarerSession.name
            this.mobile = pscCarerSession.mobile
        }
        pscOrderService.takeBatch(ids, pscCarer)
        return AjaxResult.success()
    }

    @PostMapping("/release")
    @RequirePscCarerSession
    fun release(
        @RequestParam orderIds: String,
    ): AjaxResult {
        val ids = orderIds.split(",").toTypedArray()
        if (ids.isEmpty()) {
            return AjaxResult.error("请选择工单")
        }

        val pscCarerSession = request.getCurrentPscCarer()
        pscOrderService.release(ids, pscCarerSession.employeeNo)
        return AjaxResult.success()
    }

    @PostMapping("/start/{orderId}")
    @RequirePscCarerSession
    fun start(
        @PathVariable orderId: Long,
    ): AjaxResult {
        val pscCarer = request.getCurrentPscCarer()
        val pscOrder = pscOrderService.selectPscOrderById(orderId)
            ?: return AjaxResult.error("工单不存在或已删除")
        if (pscOrder.carerEmployeeNo != pscCarer.employeeNo) {
            return AjaxResult.error("权限不足")
        }
        if (pscOrder.status != PscOrder.STATUS_READY) {
            return AjaxResult.error("此工单目前不可开始")
        }
        pscOrder.status = PscOrder.STATUS_SERVING
        pscOrder.startTime = Date()
        val updateRows = pscOrderService.updatePscOrder(pscOrder);
        if (updateRows == 1) {
            return AjaxResult.success(pscOrder)
        } else {
            return AjaxResult.error("操作失败")
        }
    }

    @PostMapping("/end/{orderId}")
    @RequirePscCarerSession
    fun end(
        @PathVariable orderId: Long,
    ): AjaxResult {
        val pscCarer = request.getCurrentPscCarer()
        val pscOrder = pscOrderService.selectPscOrderById(orderId)
            ?: return AjaxResult.error("工单不存在或已删除")
        if (pscOrder.carerEmployeeNo != pscCarer.employeeNo) {
            return AjaxResult.error("权限不足")
        }
        if (pscOrder.status != PscOrder.STATUS_SERVING) {
            return AjaxResult.error("此工单目前不可完成")
        }
        pscOrder.status = PscOrder.STATUS_CONFIRMING
        pscOrder.endTime = Date()
        val updateRows = pscOrderService.updatePscOrder(pscOrder);
        if (updateRows == 1) {
            return AjaxResult.success(pscOrder)
        } else {
            return AjaxResult.error("操作失败")
        }
    }

    @PostMapping("/confirm/{orderId}")
    @RequirePscNurseSession
    fun confirm(
        @PathVariable orderId: Long,
        @RequestParam score: Int,
    ): AjaxResult {
        val pscNurse = request.getCurrentPscNurse()
        val pscOrder = pscOrderService.selectPscOrderById(orderId)
            ?: return AjaxResult.error("工单不存在或已删除")
        if (pscOrder.nurseEmployeeNo != pscNurse.employeeNo) {
            return AjaxResult.error("权限不足")
        }
        if (pscOrder.status != PscOrder.STATUS_CONFIRMING) {
            return AjaxResult.error("此工单目前不可验收")
        }
        pscOrder.score = score
        pscOrder.status = PscOrder.STATUS_COMPLETE
        pscOrder.confirmTime = Date()
        val updateRows = pscOrderService.updatePscOrder(pscOrder);
        if (updateRows == 1) {
            return AjaxResult.success(pscOrder)
        } else {
            return AjaxResult.error("操作失败")
        }
    }

}
