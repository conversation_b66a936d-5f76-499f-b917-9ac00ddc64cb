package space.lzhq.ph.controller

import com.ruoyi.common.annotation.Log
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.common.enums.BusinessType
import com.ruoyi.common.utils.poi.ExcelUtil
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.domain.ZhuyuanPatient
import space.lzhq.ph.service.IZhuyuanPatientService

@Controller
@RequestMapping("/ph/zhuyuanPatient")
class ZhuyuanPatientAdminController : BaseController() {
    private val prefix = "ph/zhuyuanPatient"

    @Autowired
    private val zhuyuanPatientService: IZhuyuanPatientService? = null

    @RequiresPermissions("ph:zhuyuanPatient:view")
    @GetMapping
    fun zhuyuanPatient(): String? {
        return "$prefix/zhuyuanPatient"
    }

    /**
     * 查询住院患者列表
     */
    @RequiresPermissions("ph:zhuyuanPatient:list")
    @PostMapping("/list")
    @ResponseBody
    fun list(zhuyuanPatient: ZhuyuanPatient?): TableDataInfo? {
        startPage()
        val list: List<ZhuyuanPatient?> = zhuyuanPatientService!!.selectZhuyuanPatientList(zhuyuanPatient)
        return getDataTable(list)
    }

    /**
     * 导出住院患者列表
     */
    @RequiresPermissions("ph:zhuyuanPatient:export")
    @Log(title = "住院患者", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    fun export(zhuyuanPatient: ZhuyuanPatient?): AjaxResult? {
        val list: List<ZhuyuanPatient> = zhuyuanPatientService!!.selectZhuyuanPatientList(zhuyuanPatient)
        val util: ExcelUtil<ZhuyuanPatient> = ExcelUtil<ZhuyuanPatient>(ZhuyuanPatient::class.java)
        return util.exportExcel(list, "住院患者数据")
    }

    /**
     * 新增住院患者
     */
    @GetMapping("/add")
    fun add(): String? {
        return "$prefix/add"
    }

    /**
     * 新增保存住院患者
     */
    @RequiresPermissions("ph:zhuyuanPatient:add")
    @Log(title = "住院患者", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    fun addSave(zhuyuanPatient: ZhuyuanPatient?): AjaxResult? {
        return toAjax(zhuyuanPatientService!!.insertZhuyuanPatient(zhuyuanPatient))
    }

    /**
     * 修改住院患者
     */
    @RequiresPermissions("ph:zhuyuanPatient:edit")
    @GetMapping("/edit/{id}")
    fun edit(@PathVariable id: Long?, mmap: ModelMap): String? {
        val zhuyuanPatient: ZhuyuanPatient = zhuyuanPatientService!!.selectZhuyuanPatientById(id)
        mmap["zhuyuanPatient"] = zhuyuanPatient
        return "$prefix/edit"
    }

    /**
     * 修改保存住院患者
     */
    @RequiresPermissions("ph:zhuyuanPatient:edit")
    @Log(title = "住院患者", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    fun editSave(zhuyuanPatient: ZhuyuanPatient?): AjaxResult? {
        return toAjax(zhuyuanPatientService!!.updateZhuyuanPatient(zhuyuanPatient))
    }

    /**
     * 删除住院患者
     */
    @RequiresPermissions("ph:zhuyuanPatient:remove")
    @Log(title = "住院患者", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    fun remove(ids: String?): AjaxResult? {
        return toAjax(zhuyuanPatientService!!.deleteZhuyuanPatientByIds(ids))
    }
}