package space.lzhq.ph.controller

import com.alipay.api.response.AlipayTradeRefundApplyResponse
import com.github.binarywang.wxpay.bean.request.WxInsurancePayUnifiedOrderRequest
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.service.ISysConfigService
import kotlinx.coroutines.runBlocking
import org.dromara.hutool.core.text.StrUtil
import org.dromara.hutool.crypto.digest.MD5
import org.dromara.hutool.http.server.servlet.ServletUtil
import org.mospital.bsoft.mip.*
import org.mospital.jackson.DateTimeFormatters
import org.mospital.jackson.JacksonKit
import org.mospital.wecity.mip.ClientType
import org.mospital.wecity.mip.Constants
import org.mospital.wecity.mip.WecityMipService
import org.mospital.wecity.mip.WeixinMipSetting
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.MipAlipayOrder
import space.lzhq.ph.domain.MipWxOrder
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.ext.AlipayExt
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.IMipAlipayOrderService
import space.lzhq.ph.service.IMipWxOrderService
import java.time.LocalDateTime

@RestController
@RequestMapping("/api/weixinMip")
class WeixinMipController constructor(
    private var mipWxOrderService: IMipWxOrderService,
    private var mipAlipayOrderService: IMipAlipayOrderService,
    private var sysConfigService: ISysConfigService
) : BaseController() {

    /**
     * 自动合并诊查费用
     */
    @PostMapping("autoMergeFees")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun autoMergeFees(): AjaxResult {
        if (!sysConfigService.getBoolean("bool-wxxcx-yibao-yidong-zhifu", true)) {
            return AjaxResult.error("医保移动支付功能正在升级维护，暂停使用，敬请谅解")
        }

        val activePatient: Patient = request.getCurrentPatient()

        val queryFeesResult: QueryFeesResult = runBlocking {
            BSoftMipService.WEIXIN.queryFees(
                QueryFeesForm(
                    patientId = activePatient.patientNo
                )
            )
        }
        if (!queryFeesResult.isOk() && "当前无就诊记录信息！" != queryFeesResult.message) {
            return AjaxResult.error(queryFeesResult.message)
        }
        if (queryFeesResult.data.isEmpty()) {
            return AjaxResult.success()
        }

        val mergeFeesResult: MergeFeesResult = runBlocking {
            BSoftMipService.WEIXIN.mergeFees(
                MergeFeesForm(
                    feeIds = queryFeesResult.data.joinToString(",") {
                        if (it.yjxh.startsWith("YJ")) it.yjxh else "YJ${it.yjxh}"
                    }
                )
            )
        }
        if (!mergeFeesResult.isOk()) {
            return AjaxResult.error(mergeFeesResult.message)
        }

        return AjaxResult.success()
    }

    /**
     * 获取待结算费用清单
     */
    @GetMapping("pendingSettlements")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getPendingSettlements(): AjaxResult {
        if (!sysConfigService.getBoolean("bool-wxxcx-yibao-yidong-zhifu", true)) {
            return AjaxResult.error("医保移动支付功能正在升级维护，暂停使用，敬请谅解")
        }

        val activePatient: Patient = request.getCurrentPatient()
        val pendingSettlementResult: PendingSettlementResult = runBlocking {
            BSoftMipService.WEIXIN.getPendingSettlements(
                PendingSettlementForm(
                    patientId = activePatient.patientNo
                )
            )
        }

        return if (pendingSettlementResult.isOk()) {
            AjaxResult.success(pendingSettlementResult.toPendingSettlements())
        } else {
            AjaxResult.error(pendingSettlementResult.message)
        }
    }

    /**
     * 获取授权地址
     * @param accountType 账户类型：self=本人医保，family=亲属医保
     */
    @GetMapping("authUrl")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getAuthUrl(
        @RequestParam accountType: String,
    ): AjaxResult {
        val activePatient = request.getCurrentPatient()
        val authUrl = WecityMipService.buildAuthUrl(
            clientType = ClientType.MA,
            accountType = accountType,
            userName = activePatient.name,
            userCardNo = activePatient.idCardNo,
        )
        return AjaxResult.success(authUrl)
    }

    /**
     * 创建订单
     * @param acctUsedFlag 个人账户使用标志：0=不使用，1=使用
     */
    @PostMapping("createOrder/v2")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun createOrderV2(
        @RequestParam settlementIds: String,
        @RequestParam(required = false, defaultValue = "") manBingCode: String,
        @RequestParam(required = false, defaultValue = "") manBingName: String,
        @RequestParam mipAuthCode: String,
        @RequestParam(required = false, defaultValue = "1") acctUsedFlag: Int,
    ): AjaxResult {
        if (settlementIds.isBlank()) {
            return AjaxResult.error("结算单号不能为空")
        }

        val settlementIdSeparator = StrUtil.COMMA
        val settlementIdList =
            settlementIds.split(settlementIdSeparator).map { it.trim() }.filter { it.isNotBlank() }.distinct()
        if (settlementIdList.any { !it.startsWith("CF") && !it.startsWith("YJ") }) {
            return AjaxResult.error("结算单号格式不正确")
        }

        if (manBingCode.isNotBlank() && settlementIdList.size > 1) {
            return AjaxResult.error("慢病处方必须单独结算")
        }

        val activePatient = request.getCurrentPatient()
        val userQueryResult = WecityMipService.queryUser(
            qrcode = mipAuthCode,
            openid = activePatient.openId
        )
        if (!userQueryResult.isOK()) {
            return AjaxResult.error(userQueryResult.message)
        }
        if (userQueryResult.cityId.isNullOrBlank()) {
            return AjaxResult.error("获取医保参保地失败")
        }
        if (!userQueryResult.cityId!!.startsWith("65")) {
            return AjaxResult.error("目前仅支持新疆维吾尔自治区区医保")
        }

        if (userQueryResult.isSelfPay() && (userQueryResult.userCardNo != activePatient.idCardNo || userQueryResult.userName != activePatient.name)) {
            return AjaxResult.error("医保信息与当前就诊人信息不一致")
        }

        val payAuthNo = if (userQueryResult.isSelfPay()) {
            userQueryResult.payAuthNo!!
        } else if (userQueryResult.isFamilyPay()) {
            userQueryResult.familyPayAuthNo!!
        } else {
            // Unreachable
            return AjaxResult.error("未知的医保支付方式")
        }
        val userName = if (userQueryResult.isSelfPay()) {
            userQueryResult.userName!!
        } else {
            activePatient.name
        }
        val userCardNo = if (userQueryResult.isSelfPay()) {
            userQueryResult.userCardNo!!
        } else {
            activePatient.idCardNo
        }

        val settlementIds = settlementIdList.joinToString(separator = settlementIdSeparator)
        val createOrderResult = runBlocking {
            BSoftMipService.WEIXIN.createOrder(
                CreateOrderForm(
                    orgCode = BSoftMipSetting.WEIXIN.orgCode,
                    orgId = BSoftMipSetting.WEIXIN.orgId,
                    idNo = userCardNo,
                    userName = userName,
                    cfyjhm = settlementIds,
                    feeIds = "",
                    mbbm = manBingCode,
                    mbmc = manBingName,
                    payAuthNo = payAuthNo,
                    acctUsedFlag = acctUsedFlag,
                    payFlag = 1,
                    insuredId = userQueryResult.cityId!!,
                )
            )
        }

        return if (createOrderResult.isOk()) {
            createOrderResult.data!!.extraData.apply {
                this["payAuthNo"] = payAuthNo
                this["longitudeLatitude"] = userQueryResult.longitudeLatitude?.toString() ?: "0,0"
            }

            val mipWxOrder = MipWxOrder(userQueryResult, activePatient, createOrderResult.data!!)
            mipWxOrder.settlementIds = settlementIds
            mipWxOrderService.save(mipWxOrder)

            AjaxResult.success(createOrderResult.data)
        } else {
            AjaxResult.error(createOrderResult.message)
        }
    }

    @GetMapping("unlockableOrders")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun unlockableOrders(): AjaxResult {
        val activePatient = request.getCurrentPatient()

        val unlockableOrders = mipWxOrderService.queryUnlockableOrders(activePatient.idCardNo)
        return AjaxResult.success(unlockableOrders)
    }

    /**
     * 解锁订单
     */
    @PostMapping("unlockOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun unlockOrder(@RequestParam orderId: Long): AjaxResult {
        val activePatient = request.getCurrentPatient()
        val mipWxOrder = mipWxOrderService.getById(orderId)
            ?: return AjaxResult.error("订单不存在")

        // 验证订单是否属于当前患者
        if (mipWxOrder.patientIdCardNo != activePatient.idCardNo) {
            return AjaxResult.error("无权操作此订单，订单与当前就诊人信息不符")
        }

        // 验证订单是否可以解锁
        if (!mipWxOrderService.canUnlock(mipWxOrder)) {
            return AjaxResult.error("当前订单不支持解锁处方")
        }

        val unlockOrderResult = runBlocking {
            BSoftMipService.WEIXIN.unlockOrder(
                UnlockOrderForm(cfyjhm = mipWxOrder.settlementIds)
            )
        }
        return if (unlockOrderResult.isOk()) {
            mipWxOrderService.updateUnlockTime(orderId, LocalDateTime.now())
            AjaxResult.success()
        } else {
            AjaxResult.error(unlockOrderResult.message)
        }
    }

    /**
     * 支付订单
     * @param payOrderId 向医保上传费用明细时产生的支付订单号
     */
    @PostMapping("payOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun payOrder(
        @RequestParam payOrderId: String
    ): AjaxResult {
        val activePatient = request.getCurrentPatient()

        val mipWxOrder = mipWxOrderService.getOneByPayOrderId(payOrderId)
            ?: return AjaxResult.error("订单不存在")

        val request = WxInsurancePayUnifiedOrderRequest().apply {
            this.orderType = Constants.ORDER_TYPE_DiagPay
            this.openid = activePatient.openId
            this.hospOutTradeNo = PaymentKit.newOrderId(ServiceType.ZJ)
            this.hospitalName = com.ruoyi.common.constant.Constants.MERCHANT_NAME
            // 总共需要支付现金金额，对应 feeSumamt
            this.totalFee = PaymentKit.yuanToFen(mipWxOrder.feeSumAmount).toInt()
            // 现金需要支付的金额，对应 ownPayAmt
            this.cashFee = PaymentKit.yuanToFen(mipWxOrder.ownPayAmount).toInt()
            this.payType = if (this.cashFee > 0) 3 else 2
            // 医保支付金额，医保基金支付+个人账户支出，对应 fundPay + psnAcctPay
            this.insuranceFee =
                PaymentKit.yuanToFen(mipWxOrder.fundPayAmount + mipWxOrder.personalAccountPayAmount).toInt()
            this.spbillCreateIp = ServletUtil.getClientIP(request)
            this.notifyUrl = "https://zy.xj-etyy.com:8888/open/wx/onWxInsurancePay"
            this.returnUrl = "pages_yibao/OrderResult/OrderResult?hospOutTradeNo=${this.hospOutTradeNo}"
            this.body = com.ruoyi.common.constant.Constants.MERCHANT_NAME + "-诊间缴费"
            this.userCardType = 1
            this.userCardNo = MD5.of().digestHex(mipWxOrder.userCardNo)
            this.userName = mipWxOrder.userName
            this.longitude = mipWxOrder.longitudeLatitude.split(",")[0].trim()
            this.latitude = mipWxOrder.longitudeLatitude.split(",")[1].trim()
            this.gmtOutCreate = LocalDateTime.now().format(DateTimeFormatters.PURE_DATETIME_FORMATTER)
            this.serialNo = mipWxOrder.hisOrderId
            this.payAuthNo = mipWxOrder.payAuthNo
            this.payOrderId = mipWxOrder.payOrderId
            this.cityId = WeixinMipSetting.ma.cityId
            this.orgNo = WeixinMipSetting.ma.orgCode
            this.channelNo = WeixinMipSetting.ma.channelNo

            if (!mipWxOrder.isSelfPay) {
                this.extends = JacksonKit.writeValueAsString(
                    mapOf(
                        "rel_user_name_md5" to MD5.of().digestHex(mipWxOrder.patientName),
                        "rel_user_card_no_md5" to MD5.of().digestHex(mipWxOrder.patientIdCardNo)
                    )
                )
            }
        }
        return try {
            val wxInsurancePayUnifiedOrderResult = WeixinExt.getWxInsurancePayService().unifiedOrder(request)
            mipWxOrder.hospitalOutTradeNo = request.hospOutTradeNo
            mipWxOrder.medTransactionId = wxInsurancePayUnifiedOrderResult.medTransId
            mipWxOrderService.updateById(mipWxOrder)
            AjaxResult.success(
                mapOf(
                    "medTransactionId" to wxInsurancePayUnifiedOrderResult.medTransId,
                    "payUrl" to wxInsurancePayUnifiedOrderResult.payUrl,
                    "payAppId" to wxInsurancePayUnifiedOrderResult.payAppId
                )
            )
        } catch (e: Exception) {
            AjaxResult.error(e.message)
        }
    }

    @GetMapping("queryOrder")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun queryOrder(
        @RequestParam medTransId: String,
    ): AjaxResult {
        if (medTransId.isBlank()) {
            return AjaxResult.error("参数medTransId不能为空")
        }

        return try {
            val wxInsurancePayOrderQueryResult = mipWxOrderService.refreshOrderStatus(medTransId)
            AjaxResult.success(wxInsurancePayOrderQueryResult)
        } catch (e: Exception) {
            AjaxResult.error(e.message)
        }
    }

    data class RefundOrderForm(
        val payOrderId: String,
    )

    @PostMapping("refundOrder")
    fun refundOrder(
        @RequestBody form: RefundOrderForm,
    ): AjaxResult {
        try {
            val mipWxOrder: MipWxOrder = mipWxOrderService.getOneByPayOrderId(form.payOrderId)
                ?: return refundMipAlipayOrder(form.payOrderId)

            val wxInsurancePayOrderQueryResult = WeixinExt.getWxInsurancePayService().queryOrder(
                medTransId = mipWxOrder.medTransactionId,
                hospOutTradeNo = ""
            )
            if (wxInsurancePayOrderQueryResult.cashTradeStatus != "SUCCESS") {
                return AjaxResult.error("现金支付状态为${wxInsurancePayOrderQueryResult.cashTradeStatusDescription}，不可退款")
            }

            val refundResult = WeixinExt.refundWeixin(mipWxOrder)
            return AjaxResult.success(refundResult)
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return AjaxResult.error(e.message)
        }
    }

    private fun refundMipAlipayOrder(payOrderId: String): AjaxResult {
        val mipAlipayOrder: MipAlipayOrder = mipAlipayOrderService.getOneByPayOrderId(payOrderId)
            ?: return AjaxResult.error("订单不存在")

        if (PaymentKit.yuanToFen(mipAlipayOrder.ownPayAmount) <= 0) {
            return AjaxResult.success("现金支付金额为0")
        }

        val refundResponse: AlipayTradeRefundApplyResponse = AlipayExt.refundAlipay(mipAlipayOrder)
        return if (refundResponse.isSuccess) {
            AjaxResult.success(refundResponse)
        } else {
            AjaxResult.error(refundResponse.subCode + ":" + refundResponse.subMsg)
        }
    }

}
