package space.lzhq.ph.controller

import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.domain.SysConfig
import com.ruoyi.system.service.ISysConfigService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/open/config")
class ConfigController {

    @Autowired
    private lateinit var configService: ISysConfigService

    @GetMapping()
    fun getConfigs(): AjaxResult {
        val configs = configService.selectConfigList(SysConfig()).filter {
            it.isPublic
        }.associate { it.configKey to it.configValue }
        return AjaxResult.success(configs)
    }

    @GetMapping("/{configKey}")
    fun getConfigValueAsString(@PathVariable configKey: String): AjaxResult {
        // 检查配置是否允许公开访问
        val config = configService.selectConfigList(SysConfig()).find { it.configKey == configKey }
            ?: return AjaxResult.error("配置项不存在")

        if (!config.isPublic) {
            return AjaxResult.error("该配置项不允许公开访问")
        }

        val configValue = configService.selectConfigByKey(configKey)
        return AjaxResult.success(configValue)
    }

    @GetMapping("/bool/{configKey}")
    fun getConfigValueAsBoolean(@PathVariable configKey: String): AjaxResult {
        // 检查配置是否允许公开访问
        val config = configService.selectConfigList(SysConfig()).find { it.configKey == configKey }
            ?: return AjaxResult.error("配置项不存在")

        if (!config.isPublic) {
            return AjaxResult.error("该配置项不允许公开访问")
        }

        val value = configService.getBoolean(configKey, true)
        return AjaxResult.success(value)
    }

}