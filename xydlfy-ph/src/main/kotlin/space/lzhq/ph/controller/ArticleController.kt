package space.lzhq.ph.controller

import com.github.pagehelper.PageHelper
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.ui.ModelMap
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody
import space.lzhq.ph.domain.Article
import space.lzhq.ph.service.IArticleService

@Controller
@RequestMapping("/open/article")
class ArticleController : BaseController() {

    private val prefix = "ph/article"

    @Autowired
    private lateinit var articleService: IArticleService

    /**
     * 文章分页
     * @param categoryId 栏目ID
     * @param pageNumber 第几页
     * @param pageSize 每页几条
     */
    @GetMapping("paginate")
    @ResponseBody
    fun paginate(
        @RequestParam categoryId: Long,
        @RequestParam(defaultValue = "25") pageSize: Int,
        @RequestParam(value = "pageNum", defaultValue = "1") pageNumber: Int

    ): TableDataInfo {
        PageHelper.startPage<Article>(pageNumber, pageSize, "id asc")
        val list = articleService.selectArticleList(Article().apply { this.categoryId = categoryId })
        return getDataTable(list)
    }

    /**
     * 文章详情
     * @param id 文章ID
     */
    @GetMapping("show")
    @ResponseBody
    fun show(
        @RequestParam id: Long
    ): AjaxResult {
        val article = articleService.selectArticleById(id)
            ?: return AjaxResult.error("文章[#$id]不存在")
        return AjaxResult.success(article)
    }

    /**
     * 文章详情页
     * @param id 文章ID
     */
    @GetMapping("preview")
    fun preview(
        @RequestParam id: Long,
        attrs: ModelMap
    ): String {
        val article = articleService.selectArticleById(id)
        attrs["article"] = article
        return "$prefix/preview"
    }
}