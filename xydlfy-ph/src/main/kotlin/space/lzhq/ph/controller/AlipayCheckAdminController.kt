package space.lzhq.ph.controller

import com.ruoyi.common.annotation.Log
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import com.ruoyi.common.enums.BusinessType
import com.ruoyi.common.utils.poi.ExcelUtil
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.dromara.hutool.core.io.file.FileUtil
import org.mospital.alipay.AlipayBillItem
import org.mospital.bsoft.BSoftService
import org.mospital.bsoft.BillDetailForm
import org.mospital.common.IdUtil
import org.mospital.jackson.DateTimeFormatters
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.ModelAndView
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import space.lzhq.ph.domain.AlipayCheck
import space.lzhq.ph.ext.AlipayExt
import space.lzhq.ph.service.IAlipayCheckService
import java.math.BigDecimal
import java.time.LocalDate

@Controller
@RequestMapping("/ph/alipaycheck")
class AlipayCheckAdminController : BaseController() {

    @Autowired
    private lateinit var alipayCheckService: IAlipayCheckService

    @RequiresPermissions("ph:alipaycheck:view")
    @GetMapping
    fun alipaycheck(): String? {
        return "ph/alipaycheck/alipaycheck"
    }

    @RequiresPermissions("ph:alipaycheck:list")
    @PostMapping("/list")
    @ResponseBody
    fun list(alipayCheck: AlipayCheck?): TableDataInfo? {
        startPage()
        val list: List<AlipayCheck?> = alipayCheckService.selectAlipayCheckList(alipayCheck)
        return getDataTable(list)
    }

    /**
     * 导出支付宝对账列表
     */
    @RequiresPermissions("ph:alipaycheck:export")
    @Log(title = "支付宝对账", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    fun export(alipayCheck: AlipayCheck?): AjaxResult? {
        val list = alipayCheckService.selectAlipayCheckList(alipayCheck)
        val util = ExcelUtil(
            AlipayCheck::class.java
        )
        return util.exportExcel(list, "支付宝对账数据")
    }

    @RequiresPermissions("ph:alipaycheck:list")
    @Log(title = "对账", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    fun editSave(alipayCheck: AlipayCheck): AjaxResult {
        return toAjax(alipayCheckService.updateAlipayCheck(alipayCheck))
    }

    @RequiresPermissions("ph:alipaycheck:syncAlipayBill")
    @PostMapping("/syncAlipayBill/{id}")
    @ResponseBody
    fun syncAlipayBill(@PathVariable id: Long): AjaxResult {
        val billDate = IdUtil.idToLocalDate(id)
        return try {
            val billFile = AlipayExt.getOrDownloadBillFile(billDate)
            val alipayBillItems = AlipayBillItem.parseBillItems(billFile)
            val ok = alipayCheckService.syncAlipayBill(id, alipayBillItems)
            toAjax(ok)
        } catch (e: Exception) {
            val alipayBillItems = emptyList<AlipayBillItem>()
            alipayCheckService.syncAlipayBill(id, alipayBillItems)
            AjaxResult.error(e.message ?: "发生未知错误")
        }
    }

    @RequiresPermissions("ph:alipaycheck:downloadAlipayBill")
    @GetMapping("/downloadAlipayBill/{id}")
    @ResponseBody
    fun downloadAlipayBill(@PathVariable id: Long): ResponseEntity<StreamingResponseBody> {
        val billDate = IdUtil.idToLocalDate(id)
        return try {
            val billFile = AlipayExt.getOrDownloadBillFile(billDate)
            ResponseEntity.ok()
                .contentType(MediaType.parseMediaType("text/csv"))
                .contentLength(billFile.length())
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=${billFile.name}")
                .body(StreamingResponseBody {
                    FileUtil.writeToStream(billFile, it)
                })
        } catch (e: Exception) {
            ResponseEntity.ok()
                .contentType(MediaType.parseMediaType("text/plain"))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=error.txt")
                .body(StreamingResponseBody {
                    it.write((e.message ?: "发生未知错误").toByteArray())
                })
        }
    }

    @RequiresPermissions("ph:alipaycheck:syncHisBill")
    @PostMapping("/syncHisBill/{id}")
    @ResponseBody
    fun syncHisBill(@PathVariable id: Long): AjaxResult {
        val billResponse = BSoftService.getBillDetails(
            BillDetailForm(
                startDate = LocalDate.now().minusDays(1),
                endDate = LocalDate.now(),
            )
        )
        val ok = alipayCheckService.syncHisBill(id, billResponse)
        return toAjax(ok)
    }

    @RequiresPermissions("ph:alipaycheck:showDiffOrders")
    @GetMapping("/showDiffOrders/{id}")
    fun showDiffOrders(@PathVariable id: Long): ModelAndView {
        val billDate = IdUtil.idToLocalDate(id)
        return ModelAndView(
            "ph/alipaycheck/diffOrders",
            mapOf("id" to id, "billDate" to billDate.format(DateTimeFormatters.NORM_DATE_FORMATTER))
        )
    }

    data class DiffOrder(
        var orderType: String,
        var patientId: String,
        var orderNo: String,
        var alipayOrderTime: String,
        var hisOrderTime: String,
        var alipayAmount: BigDecimal,
        var hisAmount: BigDecimal,
        var diffAmount: BigDecimal,
    )

}
