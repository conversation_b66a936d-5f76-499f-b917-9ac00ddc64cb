package space.lzhq.ph.controller

import com.alipay.api.response.AlipayTradeCreateResponse
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import kotlinx.coroutines.runBlocking
import org.dromara.hutool.core.text.StrUtil
import org.dromara.hutool.http.server.servlet.ServletUtil
import org.mospital.alipay.AlipayService
import org.mospital.alipay.AlipaySetting
import org.mospital.bsoft.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.*
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.IAlipayPaymentService
import space.lzhq.ph.service.IAlipayRefundService
import space.lzhq.ph.service.IWxPaymentService
import space.lzhq.ph.service.IWxRefundService
import java.math.BigDecimal

/**
 * 诊间支付
 */
@RestController
@RequestMapping("/api/zhenjian")
class ZhenjianController(
    private var wxPaymentService: IWxPaymentService,
    private var alipayPaymentService: IAlipayPaymentService
) : BaseController() {

    @Autowired
    private lateinit var wxRefundService: IWxRefundService

    @Autowired
    private lateinit var alipayRefundService: IAlipayRefundService

    /**
     * 获取待结算费用清单
     */
    @GetMapping("pendingSettlements")
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getPendingSettlements(): AjaxResult {
        val activePatient: Patient = request.getCurrentPatient()
        val pendingSettlementResult: Result<List<JiuzhenRecord>> = runBlocking {
            BSoftService.me.getPendingSettlements(
                PendingSettlementForm(
                    patientId = activePatient.patientNo
                )
            )
        }

        return if (pendingSettlementResult.isOk()) {
            @Suppress("UNCHECKED_CAST")
            AjaxResult.success(toPendingSettlements(pendingSettlementResult.data as List<JiuzhenRecord>))
        } else {
            AjaxResult.error(pendingSettlementResult.message)
        }
    }

    /**
     * 创建微信支付订单，使用本接口返回的参数，拉起微信收银台
     * 用户支付成功后，调用`POST /api/menzhen/refreshWxpayOrder`接口，刷新订单状态
     * @param settlementIds 结算单号，多个用逗号分隔，来自接口`GET /api/zhenjian/pendingSettlements`
     * @param amount 金额，单位：元
     * @return 用于拉起微信收银台的参数
     * @deprecated 已废弃，请使用 checkBalanceAndCreateWeixinRechargeOrder 方法
     */
    @Deprecated("已废弃，请使用 checkBalanceAndCreateWeixinRechargeOrder 方法")
    @PostMapping("createWxpayOrder")
    @RequireSession(clientType = ClientType.WEIXIN)
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun createWxpayOrder(
        @RequestParam settlementIds: String,
        @RequestParam amount: BigDecimal,
    ): AjaxResult {
        if (settlementIds.isBlank()) {
            return AjaxResult.error("结算单号不能为空")
        }

        val settlementIdSeparator = StrUtil.COMMA
        val settlementIdList =
            settlementIds.split(settlementIdSeparator).map { it.trim() }.filter { it.isNotBlank() }.distinct()
        if (settlementIdList.any { !it.startsWith("CF") && !it.startsWith("YJ") }) {
            return AjaxResult.error("结算单号格式不正确")
        }

        val activePatient: Patient = request.getCurrentPatient()
        val patientResult = BSoftService.getPatientInfoByJzCardNo(jzCardNo = activePatient.jzCardNo)
        if (!patientResult.isOk()) {
            return AjaxResult.error("就诊卡[${activePatient.jzCardNo}]可能已挂失")
        }

        val ip: String = ServletUtil.getClientIP(request)
        val serviceType = ServiceType.ZJ
        val outTradeNo: String = PaymentKit.newOrderId(serviceType)
        val wxPayMpOrderResult = WeixinExt.pay(
            serviceType = serviceType,
            amount = PaymentKit.yuanToFen(amount).toInt(),
            ip = ip,
            outTradeNo = outTradeNo,
            patient = activePatient
        ) { request, _ ->
            wxPaymentService.createAndSave(serviceType, activePatient, request, settlementIds)
        }

        return AjaxResult.success(
            mapOf(
                "appId" to wxPayMpOrderResult.appId,
                "timeStamp" to wxPayMpOrderResult.timeStamp,
                "nonceStr" to wxPayMpOrderResult.nonceStr,
                "package" to wxPayMpOrderResult.packageValue,
                "signType" to wxPayMpOrderResult.signType,
                "paySign" to wxPayMpOrderResult.paySign,
                "zyOrderNo" to outTradeNo
            )
        )
    }

    /**
     * 检查余额并创建充值订单
     * 根据结算单号从HIS获取待缴费金额，检查患者就诊卡余额是否足够，如果余额不足则创建微信支付订单用于充值差额
     * @param settlementIds 结算单号，多个用逗号分隔
     * @return 余额不足时返回微信支付参数用于充值，余额充足时返回错误提示无需充值
     */
    @PostMapping("checkBalanceAndCreateWeixinRechargeOrder")
    @RequireSession(clientType = ClientType.WEIXIN)
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun checkBalanceAndCreateWeixinRechargeOrder(
        @RequestParam settlementIds: String,
    ): AjaxResult {
        if (settlementIds.isBlank()) {
            return AjaxResult.error("结算单号不能为空")
        }

        val settlementIdSeparator = StrUtil.COMMA
        val settlementIdList =
            settlementIds.split(settlementIdSeparator).map { it.trim() }.filter { it.isNotBlank() }.distinct()
        if (settlementIdList.any { !it.startsWith("CF") && !it.startsWith("YJ") }) {
            return AjaxResult.error("结算单号格式不正确")
        }

        val activePatient: Patient = request.getCurrentPatient()

        // 从HIS获取待结算费用清单
        val pendingSettlementResult: Result<List<JiuzhenRecord>> = runBlocking {
            BSoftService.me.getPendingSettlements(
                PendingSettlementForm(
                    patientId = activePatient.patientNo
                )
            )
        }

        if (!pendingSettlementResult.isOk()) {
            return AjaxResult.error("获取待结算费用失败: ${pendingSettlementResult.message}")
        }

        // 转换并筛选出指定的结算单
        @Suppress("UNCHECKED_CAST")
        val allPendingSettlements = toPendingSettlements(pendingSettlementResult.data as List<JiuzhenRecord>)
        val targetSettlements = allPendingSettlements.filter { it.no in settlementIdList }

        if (targetSettlements.isEmpty()) {
            return AjaxResult.error("未找到指定的结算单")
        }

        // 计算总金额
        val amount = targetSettlements.sumOf { it.amount }
        if (amount <= BigDecimal.ZERO) {
            return AjaxResult.error("待缴费金额不能小于等于0")
        }

        val patientResult = BSoftService.getPatientInfoByJzCardNo(jzCardNo = activePatient.jzCardNo)
        if (!patientResult.isOk()) {
            return AjaxResult.error("就诊卡[${activePatient.jzCardNo}]可能已挂失")
        }

        val balance: BigDecimal = patientResult.data?.cardBalance?.toBigDecimal() ?: BigDecimal.ZERO
        if (balance >= amount) {
            // 余额充足，无需充值
            return AjaxResult.success(
                "余额充足，无需充值",
                mapOf(
                    "needRecharge" to 0,
                    "totalAmount" to amount,
                    "balance" to balance,
                    "rechargeAmount" to BigDecimal.ZERO
                )
            )
        }

        // 余额不足，创建微信支付订单用于充值
        val rechargeAmount = amount - balance
        val ip: String = ServletUtil.getClientIP(request)
        val serviceType = ServiceType.MZ
        val outTradeNo: String = PaymentKit.newOrderId(serviceType)
        val wxPayMpOrderResult = WeixinExt.pay(
            serviceType = serviceType,
            amount = PaymentKit.yuanToFen(rechargeAmount).toInt(),
            ip = ip,
            outTradeNo = outTradeNo,
            patient = activePatient
        ) { request, _ ->
            wxPaymentService.createAndSave(serviceType, activePatient, request, settlementIds)
        }

        return AjaxResult.success(
            mapOf(
                "appId" to wxPayMpOrderResult.appId,
                "timeStamp" to wxPayMpOrderResult.timeStamp,
                "nonceStr" to wxPayMpOrderResult.nonceStr,
                "package" to wxPayMpOrderResult.packageValue,
                "signType" to wxPayMpOrderResult.signType,
                "paySign" to wxPayMpOrderResult.paySign,
                "zyOrderNo" to outTradeNo,
                "needRecharge" to 1,
                "totalAmount" to amount,
                "balance" to balance,
                "rechargeAmount" to rechargeAmount
            )
        )
    }

    /**
     * 检查余额并创建支付宝充值订单
     * 根据结算单号从HIS获取待缴费金额，检查患者就诊卡余额是否足够，如果余额不足则创建支付宝支付订单用于充值差额
     * @param settlementIds 结算单号，多个用逗号分隔
     * @return 余额不足时返回支付宝支付参数用于充值，余额充足时返回错误提示无需充值
     */
    @PostMapping("checkBalanceAndCreateAlipayRechargeOrder")
    @RequireSession(clientType = ClientType.ALIPAY)
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun checkBalanceAndCreateAlipayRechargeOrder(
        @RequestParam settlementIds: String,
    ): AjaxResult {
        if (settlementIds.isBlank()) {
            return AjaxResult.error("结算单号不能为空")
        }

        val settlementIdSeparator = StrUtil.COMMA
        val settlementIdList =
            settlementIds.split(settlementIdSeparator).map { it.trim() }.filter { it.isNotBlank() }.distinct()
        if (settlementIdList.any { !it.startsWith("CF") && !it.startsWith("YJ") }) {
            return AjaxResult.error("结算单号格式不正确")
        }

        val activePatient: Patient = request.getCurrentPatient()

        // 从HIS获取待结算费用清单
        val pendingSettlementResult: Result<List<JiuzhenRecord>> = runBlocking {
            BSoftService.me.getPendingSettlements(
                PendingSettlementForm(
                    patientId = activePatient.patientNo
                )
            )
        }

        if (!pendingSettlementResult.isOk()) {
            return AjaxResult.error("获取待结算费用失败: ${pendingSettlementResult.message}")
        }

        // 转换并筛选出指定的结算单
        @Suppress("UNCHECKED_CAST")
        val allPendingSettlements = toPendingSettlements(pendingSettlementResult.data as List<JiuzhenRecord>)
        val targetSettlements = allPendingSettlements.filter { it.no in settlementIdList }

        if (targetSettlements.isEmpty()) {
            return AjaxResult.error("未找到指定的结算单")
        }

        // 计算总金额
        val amount = targetSettlements.sumOf { it.amount }
        if (amount <= BigDecimal.ZERO) {
            return AjaxResult.error("待缴费金额不能小于等于0")
        }

        val patientResult = BSoftService.getPatientInfoByJzCardNo(jzCardNo = activePatient.jzCardNo)
        if (!patientResult.isOk()) {
            return AjaxResult.error("就诊卡[${activePatient.jzCardNo}]可能已挂失")
        }

        val balance: BigDecimal = patientResult.data?.cardBalance?.toBigDecimal() ?: BigDecimal.ZERO
        if (balance >= amount) {
            // 余额充足，无需充值
            return AjaxResult.success(
                "余额充足，无需充值",
                mapOf(
                    "needRecharge" to 0,
                    "totalAmount" to amount,
                    "balance" to balance,
                    "rechargeAmount" to BigDecimal.ZERO
                )
            )
        }

        // 余额不足，创建支付宝支付订单用于充值
        val rechargeAmount = amount - balance
        val serviceType = ServiceType.MZ
        val outTradeNo = PaymentKit.newOrderId(serviceType)
        val remark = AlipaySetting.maMerchantName + '-' + serviceType.description
        val alipayPayment = AlipayPayment(
            activePatient,
            serviceType,
            outTradeNo,
            PaymentKit.yuanToFen(rechargeAmount),
            remark
        )
        alipayPayment.settlementIds = settlementIds
        val ok = 1 == alipayPaymentService.insertAlipayPayment(alipayPayment)
        if (!ok) {
            return AjaxResult.error("操作失败，请稍后重试")
        }

        val alipayTradeCreateResponse: AlipayTradeCreateResponse = AlipayService.createOrder(
            totalAmount = rechargeAmount,
            openid = activePatient.openId,
            outTradeNo = outTradeNo,
            subject = remark,
            body = remark
        )

        return if (alipayTradeCreateResponse.isSuccess) {
            AjaxResult.success(
                mapOf(
                    "alipayResponse" to alipayTradeCreateResponse,
                    "zyOrderNo" to outTradeNo,
                    "needRecharge" to 1,
                    "totalAmount" to amount,
                    "balance" to balance,
                    "rechargeAmount" to rechargeAmount
                )
            )
        } else {
            AjaxResult.error(alipayTradeCreateResponse.subCode + "-" + alipayTradeCreateResponse.subMsg)
        }
    }

    /**
     * 创建支付宝诊间缴费订单，使用本接口返回的参数，拉起支付宝收银台
     * 用户支付成功后，调用`POST /api/menzhen/refreshAlipayOrder`接口，刷新订单状态
     * @param settlementIds 结算单号，多个用逗号分隔，来自接口`GET /api/zhenjian/pendingSettlements`
     * @param amount 金额，单位：元
     * @return 用于支付宝收银台的参数
     * @deprecated 已废弃，请使用 checkBalanceAndCreateAlipayRechargeOrder 方法
     */
    @Deprecated("已废弃，请使用 checkBalanceAndCreateAlipayRechargeOrder 方法")
    @PostMapping("createAlipayOrder")
    @RequireSession(clientType = ClientType.ALIPAY)
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun createAlipayOrder(
        @RequestParam settlementIds: String,
        @RequestParam amount: BigDecimal,
    ): AjaxResult {
        if (settlementIds.isBlank()) {
            return AjaxResult.error("结算单号不能为空")
        }

        val settlementIdSeparator = StrUtil.COMMA
        val settlementIdList =
            settlementIds.split(settlementIdSeparator).map { it.trim() }.filter { it.isNotBlank() }.distinct()
        if (settlementIdList.any { !it.startsWith("CF") && !it.startsWith("YJ") }) {
            return AjaxResult.error("结算单号格式不正确")
        }

        val activePatient: Patient = request.getCurrentPatient()
        val patientResult = BSoftService.getPatientInfoByJzCardNo(jzCardNo = activePatient.jzCardNo)
        if (!patientResult.isOk()) {
            return AjaxResult.error("就诊卡[${activePatient.jzCardNo}]可能已挂失")
        }

        val serviceType = ServiceType.ZJ
        val outTradeNo = PaymentKit.newOrderId(serviceType)
        val remark = AlipaySetting.maMerchantName + '-' + serviceType.description
        val alipayPayment = AlipayPayment(
            activePatient,
            serviceType,
            outTradeNo,
            PaymentKit.yuanToFen(amount),
            remark
        )
        alipayPayment.settlementIds = settlementIds
        val ok = 1 == alipayPaymentService.insertAlipayPayment(alipayPayment)
        if (!ok) {
            return AjaxResult.error("操作失败，请稍后重试")
        }

        val alipayTradeCreateResponse: AlipayTradeCreateResponse = AlipayService.createOrder(
            totalAmount = amount,
            openid = activePatient.openId,
            outTradeNo = outTradeNo,
            subject = remark,
            body = remark
        )
        return if (alipayTradeCreateResponse.isSuccess) {
            AjaxResult.success(alipayTradeCreateResponse)
        } else {
            AjaxResult.error(alipayTradeCreateResponse.subCode + "-" + alipayTradeCreateResponse.subMsg)
        }
    }

    /**
     * 诊间支付退款
     * @param refundForm 退款表单
     * @return 退款结果
     * @deprecated 已废弃，留待以后删除
     */
    @Deprecated("已废弃，留待以后删除")
    @PostMapping("refund")
    fun refund(@RequestBody refundForm: ZhenjianRefundForm): AjaxResult {
        val wxPayment: WxPayment? = wxPaymentService.selectOneByWxPayNo(refundForm.orderNumber)
        if (wxPayment != null) {
            return refundWeixin(refundForm, wxPayment)
        }

        val alipayPayment: AlipayPayment? = alipayPaymentService.selectAlipayPaymentByOutTradeNo(refundForm.orderNumber)
        if (alipayPayment != null) {
            return refundAlipay(refundForm, alipayPayment)
        }

        return AjaxResult.error("订单不存在[${refundForm.orderNumber}]")
    }

    /**
     * 微信诊间支付退款
     */
    private fun refundWeixin(refundForm: ZhenjianRefundForm, payment: WxPayment): AjaxResult {
        val cents: Long = PaymentKit.yuanToFen(refundForm.amount)
        if (cents <= 0L) {
            return AjaxResult.error("退款金额必须大于0")
        }

        // 验证退款金额限制
        if (cents > payment.unrefund) {
            return AjaxResult.error("退款金额超出订单限制")
        }

        // 创建退款记录
        val refund: WxRefund = wxRefundService.createAndSave(payment, refundForm.amount)
        wxPaymentService.updateOnRefund(payment, cents)

        // 调用退款接口
        try {
            WeixinExt.refund(
                totalAmount = payment.amount.toInt(),
                amount = refund.amount.toInt(),
                wxPayNo = payment.wxPayNo,
                zyRefundNo = refund.zyRefundNo,
                wxPayService = WeixinExt.getWxPayServiceByMchId(refund.mchId)!!
            )

            return AjaxResult.success(
                mapOf(
                    "patientId" to refund.patientNo,
                    "totalAmount" to PaymentKit.fenToYuan(refund.totalAmount),
                    "refundAmount" to PaymentKit.fenToYuan(refund.amount),
                    "zyPayNo" to refund.zyPayNo,
                    "zyRefundNo" to refund.zyRefundNo,
                    "tradeNo" to refund.wxPayNo,
                )
            )
        } catch (e: Exception) {
            logger.error(e.message, e)
            return AjaxResult.error("退款失败: " + e.message)
        }
    }

    /**
     * 支付宝诊间支付退款
     */
    private fun refundAlipay(refundForm: ZhenjianRefundForm, payment: AlipayPayment): AjaxResult {
        val cents: Long = PaymentKit.yuanToFen(refundForm.amount)
        if (cents <= 0L) {
            return AjaxResult.error("退款金额必须大于0")
        }

        // 验证是否为诊间支付订单
        if (payment.type != ServiceType.ZJ.code) {
            return AjaxResult.error("此订单不是诊间支付订单")
        }

        // 验证退款金额限制
        if (cents > payment.unrefund) {
            return AjaxResult.error("退款金额超出订单限制")
        }

        // 创建退款记录
        val refund = AlipayRefund(payment, PaymentKit.yuanToFen(refundForm.amount), null)
        alipayRefundService.insertAlipayRefund(refund)

        // 更新原支付订单
        payment.exrefund += cents
        payment.unrefund = payment.totalAmount - payment.exrefund
        alipayPaymentService.updateAlipayPayment(payment)

        // 执行支付宝退款
        alipayRefundService.refundAlipay(refund)
        return AjaxResult.success(
            mapOf(
                "patientId" to refund.patientId,
                "totalAmount" to PaymentKit.fenToYuan(payment.totalAmount),
                "refundAmount" to PaymentKit.fenToYuan(refund.amount),
                "zyPayNo" to refund.outTradeNo,
                "zyRefundNo" to refund.outRefundNo,
                "tradeNo" to refund.tradeNo,
            )
        )
    }
}