package space.lzhq.ph.controller

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.service.ISysConfigService
import kotlinx.coroutines.*
import org.dromara.hutool.core.data.IdcardUtil
import org.mospital.bsoft.*
import org.mospital.common.TokenManager
import org.mospital.jackson.DateTimeFormatters
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.configuration.WxSubscribeMessageConfiguration
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.Reservation.*
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.ext.toAjaxResult
import space.lzhq.ph.service.IReservationService
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@RestController
@RequestMapping("/api/reservation")
class ReservationApiController : BaseController() {

    @Autowired
    private lateinit var subscribeMessageConfiguration: WxSubscribeMessageConfiguration

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    @Autowired
    private lateinit var reservationService: IReservationService

    @PostMapping
    @RequireActivePatient
    fun reserve(
        @RequestParam ticketToken: String,
        @RequestParam registerType: Int,
        @RequestParam departmentCode: String,
        @RequestParam departmentName: String,
        @RequestParam doctorCode: String,
        @RequestParam doctorName: String,
        @RequestParam feeNo: String,
        @RequestParam(defaultValue = "1") pedRatio: String,
        @RequestParam registerFee: String,
        @RequestParam aliAuthCode: String?,
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        if (currentPatient.clientTypeEnum == ClientType.WEIXIN &&
            !sysConfigService.getBoolean("bool-wxxcx-yuyue-guahao", true)
        ) {
            return AjaxResult.error("预约挂号功能正在升级维护，暂停使用，敬请谅解")
        }
        if (currentPatient.clientTypeEnum == ClientType.ALIPAY &&
            !sysConfigService.getBoolean("bool-zfbxcx-yuyue-guahao", true)
        ) {
            return AjaxResult.error("预约挂号功能正在升级维护，暂停使用，敬请谅解")
        }

        val fixedRegisterType: RegisterType = RegisterType.fromCode(registerType)
            ?: return AjaxResult.error("参数错误[registerType=$registerType]")

        //小于18岁的要是使用浮动比例
        val fee = if (currentPatient.idCardNo.isBlank() || IdcardUtil.getAge(currentPatient.idCardNo) < 18) {
            registerFee.toBigDecimal().multiply(pedRatio.toBigDecimal()).toString()
        } else {
            registerFee
        }

        val ticket: ReservationTicket = TokenManager.parseToken(ticketToken, jacksonTypeRef())
            ?: return AjaxResult.error("参数错误[ticketToken")

        if (ticket.isMaleOnly && !currentPatient.isMale()) {
            return AjaxResult.error("该号源仅限男性预约")
        }
        if (ticket.isFemaleOnly && !currentPatient.isFemale()) {
            return AjaxResult.error("该号源仅限女性预约")
        }
        if (ticket.isChildOnly && currentPatient.age != null && currentPatient.age >= 18) {
            return AjaxResult.error("该号源仅限儿童预约")
        }

        val reserveResult: Result<ReserveResult> = runBlocking {
            BSoftService.me.reserve(
                ReserveForm(
                    deptId = doctorCode,
                    doctorId = "",
                    patientId = currentPatient.patientNo,
                    registerType = fixedRegisterType.code,
                    date = ticket.date,
                    period = ticket.period,
                    feeNo = feeNo,
                    registerFee = fee
                )
            )
        }
        val reservation = space.lzhq.ph.domain.Reservation(currentPatient)
        reservation.operationType = OPERATION_TYPE_RESERVE
        reservation.operationTime = LocalDateTime.now().withNano(0)
        reservation.jzDate = ticket.date
        reservation.reservationType = fixedRegisterType.code
        reservation.departmentCode = departmentCode
        reservation.departmentName = departmentName
        reservation.doctorCode = doctorCode
        reservation.doctorName = doctorName
        reservation.status = RESERVATION_STATUS_INIT
        if (reserveResult.isOk() && reserveResult.data != null) {
            val hisReservation: ReserveResult = reserveResult.data!!
            reservation.jzTime =
                LocalDateTime.parse(hisReservation.reservationTime, DateTimeFormatters.NORM_DATETIME_FORMATTER)
            reservation.departmentLocation = hisReservation.deptAddress
            reservation.visitNumber = hisReservation.visitNumber
            reservation.reservationNumber = hisReservation.reservationNumber
            reservation.operationResult = OPERATION_RESULT_SUCCESS
            reservation.operationDesc = "预约成功"
        } else {
            reservation.jzTime = null
            reservation.departmentLocation = ""
            reservation.visitNumber = ""
            reservation.reservationNumber = ""
            reservation.operationResult = OPERATION_RESULT_FAIL
            reservation.operationDesc = "预约失败：${reserveResult.code}-${reserveResult.message}"
        }
        reservationService.save(reservation)

        CoroutineScope(Dispatchers.IO).launch {
            if (reserveResult.isOk()) {
                if (currentPatient.clientTypeEnum == ClientType.WEIXIN) {
                    WeixinExt.sendSubscribeMessage(
                        messageId = subscribeMessageConfiguration.yuyueId,
                        messagePage = subscribeMessageConfiguration.yuyuePage,
                        messageDataList = listOf(
                            // 就诊人
                            WeixinExt.newWxMaSubscribeMessageThingData("thing22", currentPatient.name),
                            // 就诊医师
                            WeixinExt.newWxMaSubscribeMessageThingData("thing19", reservation.doctorName),
                            // 候诊时间
                            WxMaSubscribeMessage.MsgData(
                                "time20",
                                reservation.jzTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))
                            ),
                            // 就诊地点
                            WeixinExt.newWxMaSubscribeMessageThingData("thing4", reservation.departmentLocation),
                            // 温馨提醒
                            WeixinExt.newWxMaSubscribeMessageThingData("thing1", "您好，您的挂号已成功"),

                            ),
                        openid = currentPatient.openId,
                        companionId = "ph_yuyue_history#${reservation.id}"
                    )
                }
            }
        }

        return reserveResult.toAjaxResult()
    }

    /**
     * 预约历史 DTO，包含扩展属性
     */
    data class ReservationHistoryDTO(
        val ampm: AMPM,
        val deptId: String?,
        val deptName: String?,
        val startTime: String,
        val endTime: String,
        val registerFlag: String,
        val registerType: RegisterType,
        val reservationNumber: String,
        val reservationTime: LocalDateTime,
        val sourceId: String,
        val address: String?,
        val status: String
    ) {
        val cancelDeadline: LocalDateTime
            get() = this.reservationTime.toLocalDate().minusDays(1).atTime(18, 0)

        @SuppressWarnings("unused")
        val canCancel: Boolean
            get() = registerFlag == "0" && LocalDateTime.now().isBefore(this.cancelDeadline)
    }

    /**
     * 预约历史
     */
    @GetMapping("history")
    @RequireActivePatient
    fun history(): AjaxResult {
        val currentPatient = request.getCurrentPatient()
        val historyResult: Result<List<Reservation>> =
            BSoftService.getReservationList(patientId = currentPatient.patientNo, operationType = "")

        return if (historyResult.isOk()) {
            val reservationDTOs = historyResult.data?.map { reservation ->
                ReservationHistoryDTO(
                    ampm = reservation.ampm,
                    deptId = reservation.deptId,
                    deptName = reservation.deptName,
                    startTime = reservation.startTime,
                    endTime = reservation.endTime,
                    registerFlag = reservation.registerFlag,
                    registerType = reservation.registerType,
                    reservationNumber = reservation.reservationNumber,
                    reservationTime = reservation.reservationTime,
                    sourceId = reservation.sourceId,
                    address = reservation.address,
                    status = reservation.status
                )
            } ?: emptyList()

            AjaxResult.success(reservationDTOs)
        } else {
            AjaxResult.error(historyResult.message)
        }
    }

    @GetMapping("detail")
    @RequireActivePatient
    fun detail(
        @RequestParam(name = "id", required = false) reservationId: Long?,
        @RequestParam(required = false) reservationNumber: String?,
        @RequestParam(required = false) registerType: Int?,
    ): AjaxResult {
        var reservation: space.lzhq.ph.domain.Reservation? = null
        if (reservationId != null) {
            reservation = reservationService.getById(reservationId)
        } else if (reservationNumber != null && registerType != null) {
            reservation = reservationService.getOneByReservationNumber(reservationNumber)
        }
        val currentPatient = request.getCurrentPatient()
        if (reservation != null && reservation.patientId != currentPatient.patientNo) {
            return AjaxResult.error("无权操作")
        }
        return if (reservation == null) {
            AjaxResult.error("查询失败")
        } else {
            AjaxResult.success(reservation)
        }
    }

    /**
     * 取消预约
     * @param reservationNumber 预约流水号
     */
    @PostMapping("cancel")
    @RequireActivePatient
    fun cancel(
        @RequestParam registerType: Int,
        @RequestParam reservationNumber: String,
    ): AjaxResult {
        val fixedRegisterType: RegisterType = RegisterType.fromCode(registerType)
            ?: return AjaxResult.error("参数错误[registerType=$registerType]")

        val currentPatient = request.getCurrentPatient()
        val cancelReservationResult: ReservationCancelResult = runBlocking {
            BSoftService.me.cancelReservation(
                ReservationCancelForm(
                    patientId = currentPatient.patientNo,
                    registerType = fixedRegisterType.code,
                    reservationNumber = reservationNumber
                )
            )
        }
        return if (cancelReservationResult.isOk()) {
            CoroutineScope(Dispatchers.IO).launch {
                val reservation: space.lzhq.ph.domain.Reservation? =
                    reservationService.getOneByReservationNumber(reservationNumber)
                if (reservation != null) {
                    reservation.status = RESERVATION_STATUS_CANCELED
                    reservationService.updateById(reservation)

                    val cancelReservation: space.lzhq.ph.domain.Reservation = reservation.toCancel()
                    cancelReservation.operationResult = OPERATION_RESULT_SUCCESS
                    cancelReservation.operationDesc = "取消预约成功"
                    reservationService.save(cancelReservation)

                    if (currentPatient.clientTypeEnum == ClientType.WEIXIN) {
                        WeixinExt.sendSubscribeMessage(
                            messageId = subscribeMessageConfiguration.cancelYuyueId,
                            messagePage = subscribeMessageConfiguration.cancelYuyuePage,
                            messageDataList = listOf(
                                // 就诊人
                                WeixinExt.newWxMaSubscribeMessagePhraseData("name1", currentPatient.name),
                                // 就诊科室
                                WeixinExt.newWxMaSubscribeMessageThingData("thing4", cancelReservation.departmentName),
                                // 就诊医生
                                WeixinExt.newWxMaSubscribeMessageNameData("name3", cancelReservation.doctorName),
                                // 就诊时间
                                WxMaSubscribeMessage.MsgData(
                                    "date2",
                                    cancelReservation.jzTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))
                                ),
                                // 就诊地点
                                WeixinExt.newWxMaSubscribeMessageThingData(
                                    "thing15",
                                    cancelReservation.departmentLocation
                                )
                            ),
                            openid = currentPatient.openId,
                            companionId = "ph_yuyue_history#${cancelReservation.id}"
                        )
                    }
                }
            }
            AjaxResult.success()
        } else {
            AjaxResult.error(cancelReservationResult.message)
        }
    }

}