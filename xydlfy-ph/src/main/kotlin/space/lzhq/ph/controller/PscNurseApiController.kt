package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.mospital.bsoft.BSoftService
import org.mospital.bsoft.Nurse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequirePscNurseSession
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.domain.PscNurseSession
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.ext.getCurrentPscNurse
import space.lzhq.ph.service.IPscNurseService
import space.lzhq.ph.service.IPscNurseSessionService

@RestController
@RequestMapping("/api/psc/nurse")
class PscNurseApiController : BaseController() {

    @Autowired
    private lateinit var pscNurseService: IPscNurseService

    @Autowired
    private lateinit var pscNurseSessionService: IPscNurseSessionService

    /**
     * 登录
     * @param employeeNo 工号
     * @param name 姓名
     */
    @PostMapping("login")
    @RequireSession
    fun login(
        @RequestParam employeeNo: String,
        @RequestParam name: String,
    ): AjaxResult {
        val session = request.getClientSession()!!
        val existedPscNurseSession =
            pscNurseSessionService.selectPscNurseSessionByEmployeeNoAndClientType(employeeNo, session.clientType)
        if (existedPscNurseSession != null) {
            return if (existedPscNurseSession.openId == session.openId) {
                AjaxResult.success(existedPscNurseSession)
            } else {
                AjaxResult.error("系统检测到您已在别的设备登录")
            }
        }

        val nursesResult = BSoftService.getNurses()
        if (!nursesResult.isOk() || nursesResult.data == null) {
            return AjaxResult.error("登录失败，请稍后重试")
        }

        val nurse: Nurse = nursesResult.data!!.firstOrNull { it.userId == employeeNo }
            ?: return AjaxResult.error("工号或姓名不正确")
        if (nurse.userName != name) {
            return AjaxResult.error("工号或姓名不正确")
        }

        val pscNurseSession = PscNurseSession(nurse, session)
        pscNurseSessionService.insertPscNurseSession(pscNurseSession)
        return AjaxResult.success(pscNurseSession)
    }

    /**
     * 注销
     */
    @PostMapping("logout")
    @RequireSession
    fun logout(): AjaxResult {
        val session = request.getClientSession()!!
        pscNurseSessionService.deletePscNurseSessionByOpenId(session.openId);
        return AjaxResult.success()
    }

    @GetMapping("me")
    @RequirePscNurseSession
    fun me(): AjaxResult {
        return AjaxResult.success(request.getCurrentPscNurse())
    }
}
