package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import kotlinx.coroutines.runBlocking
import org.apache.commons.lang3.StringUtils
import org.dromara.hutool.core.data.IdcardUtil
import org.dromara.hutool.core.lang.Validator
import org.dromara.hutool.core.text.StrUtil
import org.mospital.bsoft.*
import org.mospital.common.Ret
import org.mospital.erhc.guanxin.ActiveQrCode
import org.mospital.erhc.guanxin.HealthCardKit
import org.mospital.erhc.guanxin.PersonInfo
import org.mospital.erhc.guanxin.SecureUtil
import org.mospital.erhc.guanxin.getActiveQrCode
import org.mospital.jackson.JacksonKit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.Session
import space.lzhq.ph.domain.UserInfo
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IAlipayUserInfoService
import space.lzhq.ph.service.IPatientService

@RestController
@RequestMapping("/api/erhcCard")
class ErhcCardApiController : BaseController() {

    @Autowired
    private lateinit var patientService: IPatientService

    @Autowired
    private lateinit var alipayUserInfoService: IAlipayUserInfoService

    /**
     * 获取健康卡二维码
     * @param patientId 患者ID
     */
    @GetMapping("getQrCode")
    @RequireSession
    fun getQrCode(@RequestParam patientId: String): AjaxResult {
        val session = request.getClientSession()!!
        val patients = patientService.selectPatientListByOpenId(session.openId)
        val patient = patients.firstOrNull { it.patientNo == patientId && it.hasErhcCard() }
            ?: return AjaxResult.error("指定就诊人不存在或者未关联健康卡")

        val qrCodeRet: Ret = getActiveQrCode(patient.erhcCardNo)
        return if (qrCodeRet.isOk()) {
            val activeQrCode = qrCodeRet.getAs<ActiveQrCode>()
            val activeQrCodeMap = JacksonKit.toMap(activeQrCode)!!.toMutableMap()
            activeQrCodeMap["decryptedName"] = SecureUtil.decrypt(dest = activeQrCode.name)
            AjaxResult.success(activeQrCodeMap)
        } else {
            AjaxResult.error(qrCodeRet.getMsg())
        }
    }

    /**
     * 查询或注册健康卡
     * @param idCardNo 身份证号
     * @param name 姓名
     * @param mobile 手机号
     * @param nationCode 民族代码，如 01 表示汉族，参考 /open/dict/queryByType?type=minzu
     */
    @PostMapping("getOrRegister")
    @RequireSession
    fun getOrRegister(
        @RequestParam idCardNo: String,
        @RequestParam name: String,
        @RequestParam mobile: String,
        @RequestParam nationCode: String,
    ): AjaxResult {
        val session: Session = request.getClientSession()!!
        val userInfo: UserInfo = alipayUserInfoService.fillUserInfo(session, UserInfo(idCardNo, name, mobile))

        if (!IdcardUtil.isValidCard(userInfo.idCardNo)) {
            return AjaxResult.error("身份证号不正确")
        }

        if (!Validator.isMobile(userInfo.mobile)) {
            return AjaxResult.error("手机号不正确")
        }

        val erhcPatients = patientService.selectPatientListByIdCard(userInfo.idCardNo)
            .filter { it.hasErhcCard() && it.clientTypeEnum == session.clientTypeEnum }
        if (erhcPatients.isNotEmpty()) {
            val msg = if (erhcPatients.any { it.openId == session.openId }) {
                "您已持有电子健康卡，无需重复领取"
            } else {
                "您的电子健康卡已绑定在其它${session.clientTypeEnum.description}上，不能重复领取"
            }
            return AjaxResult.error(msg)
        }

        val healthCardRet: Ret = HealthCardKit.getOrCreateHealthCard(
            idCardNo = userInfo.idCardNo,
            name = userInfo.name,
            mobile = userInfo.mobile,
            nationCode = StrUtil.padPre(nationCode, 2, "0"),
        )
        return if (healthCardRet.isOk()) {
            val healthCard: HealthCardKit.HealthCard = healthCardRet.getAs()
            AjaxResult.success(healthCard)
        } else {
            AjaxResult.error(healthCardRet.getMsg())
        }
    }

    /**
     * 查询或注册就诊卡
     * @param idCardNo 身份证号
     * @param name 姓名
     * @param mobile 手机号
     * @param nation 民族名称，如 汉族，参考 /open/dict/queryByType?type=minzu
     * @param careerCode 职业代码，如 11 表示国家公务员，参考 /open/dict/queryByType?type=his_profession
     */
    @PostMapping("getOrRegisterJzCard")
    @RequireSession
    fun getOrRegisterJzCard(
        @RequestParam idCardNo: String,
        @RequestParam name: String,
        @RequestParam mobile: String,
        @RequestParam nation: String,
        @RequestParam(required = false, defaultValue = "90") careerCode: String,
    ): AjaxResult {
        val session: Session = request.getClientSession()!!
        val userInfo: UserInfo = alipayUserInfoService.fillUserInfo(session, UserInfo(idCardNo, name, mobile))


        if (!IdcardUtil.isValidCard(userInfo.idCardNo)) {
            return AjaxResult.error("身份证号不正确")
        }

        if (!Validator.isMobile(userInfo.mobile)) {
            return AjaxResult.error("手机号不正确")
        }

        if (StringUtils.isBlank(userInfo.name)) {
            return AjaxResult.error("姓名不能为空")
        }

        var patientResult: Result<PatientInfo> = BSoftService.getPatientInfoByIdCardNo(userInfo.idCardNo)
        if (patientResult.isOk()) {
            if (!HisExt.matchesChinese(patientResult.data?.patientName ?: "", userInfo.name)) {
                return AjaxResult.error("姓名不正确")
            }
            return AjaxResult.success(patientResult.data)
        }

        val createAccountAndCardResult: Result<AccountAndCard> = runBlocking {
            BSoftService.me.createAccountAndCard(
                AccountAndCardForm(
                    patientIdCard = userInfo.idCardNo,
                    patientName = userInfo.name,
                    patientNation = nation,
                    patientPhone = userInfo.mobile,
                    cardNo = userInfo.idCardNo,
                    careerCode = careerCode,
                    debitAmount = "0",
                )
            )
        }
        if (!createAccountAndCardResult.isOk()) {
            return AjaxResult.error(createAccountAndCardResult.message)
        }

        patientResult = BSoftService.getPatientInfoByIdCardNo(userInfo.idCardNo)
        check(patientResult.isOk()) { patientResult.message }
        return AjaxResult.success(patientResult.data)
    }

    /**
     * 关联健康卡和就诊卡
     * @param erhcCardNo 健康卡号
     * @param empi 健康卡主索引
     * @param patientId 患者ID
     */
    @PostMapping("bind")
    @RequireSession
    fun bind(
        @RequestParam erhcCardNo: String,
        @RequestParam empi: String,
        @RequestParam idCardNo: String,
    ): AjaxResult {
        val patientResult: Result<PatientInfo> = BSoftService.getPatientInfoByIdCardNo(idCardNo)
        if (!patientResult.isOk()) {
            return AjaxResult.success(patientResult.message)
        }

        val erhcCardInfoRet = HealthCardKit.getPersonInfoByErhcCardNo(erhcCardNo)
        if (!erhcCardInfoRet.isOk()) {
            return AjaxResult.error(erhcCardInfoRet.getMsg())
        }

        val erhcCardInfo = erhcCardInfoRet.getAs<PersonInfo>()
        if (erhcCardInfo.empi != empi) {
            return AjaxResult.error("健康卡或就诊卡信息不正确")
        }

        val patientInfo: PatientInfo = patientResult.data!!
        if (erhcCardInfo.decryptedIdCode != patientInfo.patientIdCard) {
            return AjaxResult.error("健康卡或就诊卡信息不正确")
        }

        if (patientInfo.erhcCardNo.isBlank()) {
            val createVmcardQrCodeResult: Result<VmcardQrCode> = runBlocking {
                BSoftService.me.createVmcardQrCode(
                    VmcardQrCodeForm(
                        erhcCardNo = erhcCardNo,
                        empi = empi,
                        patientId = patientInfo.patientId,
                        cardNo = patientInfo.cardNo
                    )
                )
            }

            if (!createVmcardQrCodeResult.isOk() && !createVmcardQrCodeResult.message.endsWith("已关联电子健康卡，请绑定该卡使用")) {
                return AjaxResult.error(createVmcardQrCodeResult.message)
            }
        }

        val session: Session = request.getClientSession()!!
        var patient = patientService.selectPatientListByOpenId(session.openId).firstOrNull {
            it.jzCardNo == patientInfo.cardNo && it.clientTypeEnum == session.clientTypeEnum
        }
        if (patient != null) {
            patient.erhcCardNo = erhcCardNo
            patient.empi = empi
            patientService.updatePatient(patient)
        } else {
            patient = Patient(session, patientInfo, "")
            patient.erhcCardNo = erhcCardNo
            patient.empi = empi
            patient.active = if (patientService.selectPatientListByOpenId(session.openId).isEmpty()) 1 else 0
            patientService.insertPatient(patient)
        }
        return AjaxResult.success(patient.maskSensitive())
    }

}
