package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.system.service.ISysConfigService
import jakarta.servlet.http.HttpServletRequest
import org.dromara.hutool.core.data.IdcardUtil
import org.dromara.hutool.core.date.DateUtil
import org.dromara.hutool.core.lang.Validator
import org.mospital.bsoft.*
import org.mospital.common.StringKit
import org.mospital.jackson.DateTimeFormatters
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.Session
import space.lzhq.ph.domain.UserInfo
import space.lzhq.ph.domain.ZhuyuanPatient
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.ext.toAjaxResult
import space.lzhq.ph.service.IAlipayUserInfoService
import space.lzhq.ph.service.IPatientService
import space.lzhq.ph.service.IZhuyuanPatientService
import java.math.RoundingMode
import java.time.LocalDate

@Suppress("DuplicatedCode")
@RestController
@RequestMapping("/api/patient")
class PatientApiController : BaseController() {

    @Autowired
    private lateinit var patientService: IPatientService

    @Autowired
    private lateinit var zhuyuanPatientService: IZhuyuanPatientService

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    @Autowired
    private lateinit var alipayUserInfoService: IAlipayUserInfoService

    /**
     * 添加就诊卡
     * @param jzCardNo 就诊卡号
     * @param name 姓名
     * @param mobile 手机号
     */
    @PostMapping("addJzCard")
    @RequireSession
    fun addJzCard(
        @RequestParam jzCardNo: String,
        @RequestParam name: String,
        @RequestParam mobile: String,
    ): AjaxResult {
        val session: Session = request.getClientSession()!!
        val userInfo: UserInfo = alipayUserInfoService.fillUserInfo(session, UserInfo("", name, mobile))

        if (!Validator.isMobile(userInfo.mobile)) {
            return AjaxResult.error("手机号不正确")
        }

//        if (patientService.selectPatientByJzCardNoAndClientType(jzCardNo, session.clientTypeEnum) != null) {
//            return AjaxResult.error("就诊卡已被绑定")
//        }

        val patientsSameOpenid: MutableList<Patient> = patientService.selectPatientListByOpenId(session.openId)

        val maxPatientsPerClient: Int = sysConfigService.maxPatientPerClient
        if (maxPatientsPerClient > 0 && patientsSameOpenid.size >= maxPatientsPerClient) {
            return AjaxResult.error("最多添加 $maxPatientsPerClient 个就诊人")
        }

        val patientInfoResult: Result<PatientInfo> = BSoftService.getPatientInfoByJzCardNo(
            jzCardNo = jzCardNo,
        )
        if (!patientInfoResult.isOk()) {
            return patientInfoResult.toAjaxResult()
        }

        val menzhenPatient: PatientInfo = patientInfoResult.data!!
        if (!HisExt.matchesChinese(menzhenPatient.patientName, userInfo.name)) {
            return AjaxResult.error("姓名不正确")
        }
        if (menzhenPatient.patientPhone != null) {
            if (userInfo.mobile != menzhenPatient.patientPhone!!.trim()) {
                return AjaxResult.error("手机号不正确")
            }
        }

        if (patientService.selectPatientByJzCardNoAndClientTypeAndOpenid(
                jzCardNo,
                session.clientTypeEnum,
                session.openId
            ).isNotEmpty()
        ) {
            return AjaxResult.error("就诊卡已被绑定")
        }

        if (menzhenPatient.patientIdCard.isNullOrBlank()) {
            return AjaxResult.error("证件号为空，请到窗口更新您的证件信息")
        }
        if (!IdcardUtil.isValidCard(menzhenPatient.patientIdCard)) {
            return AjaxResult.error("【${menzhenPatient.patientIdCard}】不是有效的身份证号，请到窗口更新您的身份证信息")
        }

        val patient = Patient(session, menzhenPatient, userInfo.mobile)
        // 如果这是用户添加的第一张就诊卡，就把这张卡设置为默认就诊卡
        patient.active = if (patientsSameOpenid.isEmpty()) 1 else 0
        patientService.insertPatient(patient)
        return AjaxResult.success(patient.maskSensitive())
    }

    /**
     * 添加儿童卡  需添加监护人信息
     */
    @Deprecated("已废弃，请使用 createChildCard/v1 接口")
    @PostMapping("createChildCard")
    @RequireSession
    fun createChildCard(
        @RequestParam("nation", defaultValue = "") patientNation: String,
        @RequestParam("name") patientName: String,
        @RequestParam("mobile", defaultValue = "") patientPhone: String,
        @RequestParam("guarderIdCardNo") guarderIDCard: String,
        @RequestParam guarderName: String,
        @RequestParam("guarderMobile") guarderPhoneNumber: String,
        @RequestParam(defaultValue = "") address: String,
        @RequestParam(defaultValue = "") childBirthDay: String,
        @RequestParam("idCardNo", defaultValue = "") childIdCard: String
    ): AjaxResult {
        if (!childIdCard.isNullOrBlank() && !IdcardUtil.isValidCard(childIdCard)) {
            return AjaxResult.error("儿童身份证号不正确")
        }

        if (!IdcardUtil.isValidCard(guarderIDCard)) {
            return AjaxResult.error("监护人身份证号不正确")
        }

        val card = AccountAndCardForm(
            patientName = patientName,
            patientNation = patientNation,
            patientIdCard = childIdCard,
            debitAmount = "0",
            patientPhone = patientPhone.ifBlank { guarderPhoneNumber },
            childBirthDay = childBirthDay,
            guarderIDCard = guarderIDCard,
            guarderName = guarderName,
            guarderPhoneNumber = guarderPhoneNumber,
            address = address
        )
        val result = BSoftService.createAccountAndCard(card)
        if (result.isOk()) {
            val data = result.data
            val patientInfoResult: Result<PatientInfo> = BSoftService.getPatientInfoByJzCardNo(
                jzCardNo = data!!.cardNo,
            )
            if (!patientInfoResult.isOk()) {
                return patientInfoResult.toAjaxResult()
            }
            val menzhenPatient: PatientInfo = patientInfoResult.data!!

            val session: Session = request.getClientSession()!!
            val userInfo: UserInfo =
                alipayUserInfoService.fillUserInfo(
                    session,
                    UserInfo(guarderIDCard, data.patientName, menzhenPatient.patientPhone)
                )

            if (menzhenPatient.patientPhone != null) {
                if (userInfo.mobile != menzhenPatient.patientPhone!!.trim()) {
                    return AjaxResult.error("手机号不正确")
                }
            }
            val patientsSameOpenid: MutableList<Patient> = patientService.selectPatientListByOpenId(session.openId)

            val maxPatientsPerClient: Int = sysConfigService.maxPatientPerClient
            if (maxPatientsPerClient > 0 && patientsSameOpenid.size >= maxPatientsPerClient) {
                return AjaxResult.error("最多添加 $maxPatientsPerClient 个就诊人")
            }
            val patient =
                Patient(session, menzhenPatient, userInfo.mobile, guarderPhoneNumber, guarderName, guarderIDCard)
            // 如果这是用户添加的第一张就诊卡，就把这张卡设置为默认就诊卡
            patient.active = if (patientsSameOpenid.isEmpty()) 1 else 0
            patientService.insertPatient(patient)
            return AjaxResult.success(patient.maskSensitive())
        }
        return AjaxResult.error(result.message)
    }

    /**
     * 添加儿童卡  需添加监护人信息
     * @param childGender 儿童性别 1=男，2=女
     */
    @PostMapping("createChildCard/v1")
    @RequireSession
    fun createChildCardV1(
        @RequestParam("nation", defaultValue = "") patientNation: String,
        @RequestParam("name") patientName: String,
        @RequestParam("mobile", defaultValue = "") patientPhone: String,
        @RequestParam("guarderIdCardNo") guarderIDCard: String,
        @RequestParam guarderName: String,
        @RequestParam("guarderMobile") guarderPhoneNumber: String,
        @RequestParam(defaultValue = "") address: String,
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") childBirthDay: LocalDate,
        @RequestParam childGender: Int
    ): AjaxResult {
        if (childGender !in 1..2) {
            return AjaxResult.error("儿童性别不正确")
        }

        if (!IdcardUtil.isValidCard(guarderIDCard)) {
            return AjaxResult.error("监护人身份证号不正确")
        }

        val card = AccountAndCardForm(
            patientName = patientName,
            patientNation = patientNation,
            debitAmount = "0",
            patientPhone = patientPhone.ifBlank { guarderPhoneNumber },
            childBirthDay = childBirthDay.format(DateTimeFormatters.PURE_DATE_FORMATTER),
            guarderIDCard = guarderIDCard,
            guarderName = guarderName,
            guarderPhoneNumber = guarderPhoneNumber,
            address = address,
            patientSex = childGender
        )
        val result = BSoftService.createAccountAndCard(card)
        if (result.isOk()) {
            val data = result.data
            val patientInfoResult: Result<PatientInfo> = BSoftService.getPatientInfoByJzCardNo(
                jzCardNo = data!!.cardNo,
            )
            if (!patientInfoResult.isOk()) {
                return patientInfoResult.toAjaxResult()
            }
            val menzhenPatient: PatientInfo = patientInfoResult.data!!

            val session: Session = request.getClientSession()!!
            val userInfo: UserInfo =
                alipayUserInfoService.fillUserInfo(
                    session,
                    UserInfo(guarderIDCard, data.patientName, menzhenPatient.patientPhone)
                )

            if (menzhenPatient.patientPhone != null) {
                if (userInfo.mobile != menzhenPatient.patientPhone!!.trim()) {
                    return AjaxResult.error("手机号不正确")
                }
            }
            val patientsSameOpenid: MutableList<Patient> = patientService.selectPatientListByOpenId(session.openId)

            val maxPatientsPerClient: Int = sysConfigService.maxPatientPerClient
            if (maxPatientsPerClient > 0 && patientsSameOpenid.size >= maxPatientsPerClient) {
                return AjaxResult.error("最多添加 $maxPatientsPerClient 个就诊人")
            }
            val patient =
                Patient(session, menzhenPatient, userInfo.mobile, guarderPhoneNumber, guarderName, guarderIDCard)
            // 如果这是用户添加的第一张就诊卡，就把这张卡设置为默认就诊卡
            patient.active = if (patientsSameOpenid.isEmpty()) 1 else 0
            patientService.insertPatient(patient)
            return AjaxResult.success(patient.maskSensitive())
        }
        return AjaxResult.error(result.message)
    }


    /**
     * 添加儿童卡  需添加监护人信息
     */
    @PostMapping("addChildCard")
    @RequireSession
    fun addChildCard(
        @RequestParam jzCardNo: String,
        @RequestParam name: String,
        @RequestParam guarderPhoneNumber: String,
        @RequestParam guarderName: String,
        @RequestParam guarderIDCard: String
    ): AjaxResult {
        val session: Session = request.getClientSession()!!
        val userInfo: UserInfo =
            alipayUserInfoService.fillUserInfo(session, UserInfo(guarderIDCard, name, guarderPhoneNumber))

        if (!Validator.isMobile(userInfo.mobile) || !Validator.isMobile(guarderPhoneNumber)) {
            return AjaxResult.error("手机号不正确")
        }

//        if (patientService.selectPatientByJzCardNoAndClientType(jzCardNo, session.clientTypeEnum) != null) {
//            return AjaxResult.error("就诊卡已被绑定")
//        }

        val patientsSameOpenid: MutableList<Patient> = patientService.selectPatientListByOpenId(session.openId)

        val maxPatientsPerClient: Int = sysConfigService.maxPatientPerClient
        if (maxPatientsPerClient > 0 && patientsSameOpenid.size >= maxPatientsPerClient) {
            return AjaxResult.error("最多添加 $maxPatientsPerClient 个就诊人")
        }

        val patientInfoResult: Result<PatientInfo> = BSoftService.getPatientInfoByJzCardNo(
            jzCardNo = jzCardNo,
        )
        if (!patientInfoResult.isOk()) {
            return patientInfoResult.toAjaxResult()
        }

        val menzhenPatient: PatientInfo = patientInfoResult.data!!
        if (!HisExt.matchesChinese(menzhenPatient.patientName, userInfo.name)) {
            return AjaxResult.error("姓名不正确")
        }
        if (menzhenPatient.patientPhone != null) {
            if (userInfo.mobile != menzhenPatient.patientPhone!!.trim()) {
                return AjaxResult.error("手机号不正确")
            }
        }

        val patient = Patient(session, menzhenPatient, userInfo.mobile, guarderPhoneNumber, guarderName, guarderIDCard)
        // 如果这是用户添加的第一张就诊卡，就把这张卡设置为默认就诊卡
        patient.active = if (patientsSameOpenid.isEmpty()) 1 else 0
        patientService.insertPatient(patient)
        return AjaxResult.success(patient.maskSensitive())
    }

    /**
     * 获取就诊人列表
     */
    @GetMapping("list")
    @RequireSession
    fun list(): AjaxResult {
        val session = request.getClientSession()!!
        val patientsSameOpenid = patientService.selectPatientListByOpenId(session.openId)
        return AjaxResult.success(patientsSameOpenid.onEach { it.maskSensitive() })
    }

    /**
     * 获取默认就诊人
     */
    @GetMapping("active")
    @RequireActivePatient
    fun active(): AjaxResult {
        return AjaxResult.success(request.getCurrentPatient().maskSensitive())
    }

    /**
     * 根据就诊卡号查询患者信息
     * @param jzCardNo 就诊卡号
     */
    @GetMapping("jzCard")
    @RequireSession
    fun jzCard(@RequestParam jzCardNo: String): AjaxResult {
        val session = request.getClientSession()!!
        val patient = patientService.selectPatientByJzCardNoAndClientType(jzCardNo, session.clientTypeEnum)
            ?: return AjaxResult.error("查不到指定的就诊人信息")

        if (session.openId != patient.openId) {
            return AjaxResult.error("您无权访问该患者信息")
        }

        return AjaxResult.success(patient.maskSensitive())
    }

    /**
     * 获取门诊余额
     */
    @GetMapping("menzhenBalance")
    @RequireSession
    fun getMenzhenBalance(@RequestParam id: String): AjaxResult {
        val patient = patientService.selectPatientById(id.toLong())
        val patientInfoResult: Result<PatientInfo> = BSoftService.getPatientInfoByJzCardNo(
            jzCardNo = patient.jzCardNo,
        )
        return if (!patientInfoResult.isOk()) {
            AjaxResult.error("查询门诊余额失败")
        } else {
            val session = request.getClientSession()!!
            val patientsSameOpenid = patientService.selectPatientListByOpenId(session.openId)
            val patientInfo = patientInfoResult.data!!

            if (patientsSameOpenid.none { it.patientNo == patientInfo.patientId }) {
                return AjaxResult.error("您无权访问该患者信息")
            }

            AjaxResult.success(patientInfo.cardBalance.toBigDecimal().setScale(2, RoundingMode.HALF_UP))
        }
    }

    /**
     * 切换到指定就诊卡
     * @param jzCardNo 身份证号
     */
    @PostMapping("switchToJzCard")
    @RequireSession
    fun switchToJzCard(@RequestParam jzCardNo: String): AjaxResult {
        val session = request.getClientSession()!!
        val patientsSameOpenid = patientService.selectPatientListByOpenId(session.openId)

        var oldActive: Patient? = null
        var newActive: Patient? = null
        patientsSameOpenid.forEach {
            if (it.jzCardNo == jzCardNo) {
                it.active = 1
                newActive = it
            } else {
                if (it.active == 1) {
                    it.active = 0
                    oldActive = it
                }
            }
        }

        if (newActive == null) {
            return AjaxResult.error("查不到指定的就诊人信息")
        }

        if (oldActive != null) {
            patientService.updatePatient(oldActive)
        }

        patientService.updatePatient(newActive)
        return AjaxResult.success(newActive.maskSensitive())
    }

    /**
     * 根据就诊卡号移除就诊人
     * @param jzCardNo 就诊卡号
     */
    @PostMapping("removeJzCard")
    @RequireSession
    fun removeJzCard(@RequestParam jzCardNo: String): AjaxResult {
        if (jzCardNo.isBlank()) {
            return AjaxResult.error("就诊卡号不能为空")
        }

        val session = request.getClientSession()!!
        val patientsSameOpenid = patientService.selectPatientListByOpenId(session.openId)
        val target = patientsSameOpenid.first { jzCardNo == it.jzCardNo }
            ?: return AjaxResult.error("查不到指定的就诊人信息")
        if (target.active == 1 && patientsSameOpenid.size > 1) {
            return AjaxResult.error("为避免影响使用，请先切换到其它就诊人，再来移除此就诊人")
        }
        patientService.deletePatientById(target.id)
        return AjaxResult.success()
    }

    /**
     * 查询住院历史
     */
    @GetMapping("zhuyuanHistory")
    fun zhuyuanHistory(
        @RequestParam idCardNo: String,
        @RequestParam name: String,
    ): AjaxResult {

        val zhuyuanHistoryListResult: Result<List<ZhuyuanHistory>> =
            BSoftService.getZhuyuanHistoryListByIdCardNo(idCardNo)

        if (!zhuyuanHistoryListResult.isOk() && "未查询到住院记录！" != zhuyuanHistoryListResult.message) {
            return AjaxResult.error(zhuyuanHistoryListResult.message)
        }

        val list: List<ZhuyuanHistory> = zhuyuanHistoryListResult.data ?: emptyList()
        list.forEach {
            it.patientName = StringKit.hideName(it.patientName)
            it.patientMobile = StringKit.hideMobile(it.patientMobile)
            it.patientIdCard = StringKit.hideIdCardNo(it.patientIdCard)
        }

        return AjaxResult.success(list.filter { HisExt.matchesChinese(it.patientName, name) })
    }

    /**
     * 切换住院号，如果没有住院号进行绑定，如果有住院号进行替换
     * @deprecated 此接口已废弃，请使用 switchZhuyuanNoV2 接口，该接口使用姓名参数替代身份证号，更适合儿童患者群体
     */
    @PostMapping("switchZhuyuanNo")
    @RequireSession
    @Deprecated("使用 switchZhuyuanNoV2 接口替代，该接口使用姓名参数替代身份证号")
    fun switchZhuyuanNo(
        @RequestParam admissionNumber: String,
        @RequestParam idCardNo: String,
        @RequestParam mobile: String,
        @RequestParam mode: String = "switch",
        request: HttpServletRequest,
    ): AjaxResult {
        if (!IdcardUtil.isValidCard(idCardNo)) {
            return AjaxResult.error("身份证号不正确")
        }
        if (!Validator.isMobile(mobile)) {
            return AjaxResult.error("手机号不正确")
        }

        val zhuyuanPatientResult = BSoftService.getZhuyuanHistoryListByNum(admissionNumber)
        if (!zhuyuanPatientResult.isOk() || zhuyuanPatientResult.data.isNullOrEmpty()) {
            return AjaxResult.error("查不到当前就诊人的指定住院记录[住院号=$admissionNumber]")
        }
        val zyPatient: ZhuyuanHistory = zhuyuanPatientResult.data!!.first()
        if (zyPatient.patientIdCard != idCardNo || zyPatient.patientPhone != mobile) {
            return AjaxResult.error("您的身份证号或手机号与当前住院号信息不匹配")
        }

        if (mode == "query") {
            return AjaxResult.success(zyPatient)
        }

        val session = request.getClientSession()!!

        val zhuyuanPatientList =
            zhuyuanPatientService.selectZhuyuanPatientList(ZhuyuanPatient().apply {
                this.openId = session.openId
            })
        val currentPatient = zhuyuanPatientList.firstOrNull()?.apply {
            this.comeTime = DateUtil.parse(zyPatient.admissionTime as CharSequence)
            this.admissionNumber = zyPatient.admissionNumber
            this.idCardNo = zyPatient.patientIdCard
            this.patientNo = zyPatient.patientId
            this.bedNo = zyPatient.patientBedNumber
            this.zhuyuanNo = zyPatient.admissionNo
            this.name = zyPatient.patientName
            this.gender = zyPatient.patientSex.toInt()
            this.departmentCode = zyPatient.patientDepartMent
            this.departmentName = zyPatient.departmentName
            zhuyuanPatientService.updateZhuyuanPatient(this)
        } ?: ZhuyuanPatient().apply {
            this.openId = session.openId
            this.idCardNo = zyPatient.patientIdCard
            this.patientNo = zyPatient.patientId
            this.zhuyuanNo = zyPatient.admissionNo
            this.name = zyPatient.patientName
            this.gender = zyPatient.patientSex.toInt()
            this.departmentCode = zyPatient.patientDepartMent
            this.departmentName = zyPatient.departmentName
            this.bedNo = zyPatient.patientBedNumber
            this.comeTime = DateUtil.parse(zyPatient.admissionTime as CharSequence)
            this.admissionNumber = zyPatient.admissionNumber
            this.unionId = session.unionId
            zhuyuanPatientService.insertZhuyuanPatient(this)
        }

        return AjaxResult.success(currentPatient.apply {
            this.name = StringKit.hideName(this.name)
            this.idCardNo = StringKit.hideIdCardNo(this.idCardNo)
        })
    }

    /**
     * 切换住院号V2版本，使用姓名参数替代身份证号，更适合儿童患者群体
     * 如果没有住院号进行绑定，如果有住院号进行替换
     * @param admissionNumber 住院号
     * @param patientName 患者姓名
     * @param mobile 手机号
     * @param mode 操作模式，默认为"switch"（切换），还支持"query"（查询）
     */
    @PostMapping("switchZhuyuanNoV2")
    @RequireSession
    fun switchZhuyuanNoV2(
        @RequestParam admissionNumber: String,
        @RequestParam patientName: String,
        @RequestParam mobile: String,
        @RequestParam mode: String = "switch",
        request: HttpServletRequest,
    ): AjaxResult {
        if (patientName.isBlank()) {
            return AjaxResult.error("患者姓名不能为空")
        }
        if (!Validator.isMobile(mobile)) {
            return AjaxResult.error("手机号不正确")
        }

        val zhuyuanPatientResult = BSoftService.getZhuyuanHistoryListByNum(admissionNumber)
        if (!zhuyuanPatientResult.isOk() || zhuyuanPatientResult.data.isNullOrEmpty()) {
            return AjaxResult.error("查不到当前就诊人的指定住院记录[住院号=$admissionNumber]")
        }
        val zyPatient: ZhuyuanHistory = zhuyuanPatientResult.data!!.first()
        
        // 使用HisExt.matchesChinese进行中文姓名匹配，支持模糊匹配
        if (!HisExt.matchesChinese(zyPatient.patientName, patientName) || zyPatient.patientPhone != mobile) {
            return AjaxResult.error("您的姓名或手机号与当前住院号信息不匹配")
        }

        if (mode == "query") {
            return AjaxResult.success(zyPatient)
        }

        val session = request.getClientSession()!!

        val zhuyuanPatientList =
            zhuyuanPatientService.selectZhuyuanPatientList(ZhuyuanPatient().apply {
                this.openId = session.openId
            })
        val currentPatient = zhuyuanPatientList.firstOrNull()?.apply {
            this.comeTime = DateUtil.parse(zyPatient.admissionTime as CharSequence)
            this.admissionNumber = zyPatient.admissionNumber
            this.idCardNo = zyPatient.patientIdCard
            this.patientNo = zyPatient.patientId
            this.bedNo = zyPatient.patientBedNumber
            this.zhuyuanNo = zyPatient.admissionNo
            this.name = zyPatient.patientName
            this.gender = zyPatient.patientSex.toInt()
            this.departmentCode = zyPatient.patientDepartMent
            this.departmentName = zyPatient.departmentName
            zhuyuanPatientService.updateZhuyuanPatient(this)
        } ?: ZhuyuanPatient().apply {
            this.openId = session.openId
            this.idCardNo = zyPatient.patientIdCard
            this.patientNo = zyPatient.patientId
            this.zhuyuanNo = zyPatient.admissionNo
            this.name = zyPatient.patientName
            this.gender = zyPatient.patientSex.toInt()
            this.departmentCode = zyPatient.patientDepartMent
            this.departmentName = zyPatient.departmentName
            this.bedNo = zyPatient.patientBedNumber
            this.comeTime = DateUtil.parse(zyPatient.admissionTime as CharSequence)
            this.admissionNumber = zyPatient.admissionNumber
            this.unionId = session.unionId
            zhuyuanPatientService.insertZhuyuanPatient(this)
        }

        return AjaxResult.success(currentPatient.apply {
            this.name = StringKit.hideName(this.name)
            this.idCardNo = StringKit.hideIdCardNo(this.idCardNo)
        })
    }

}
