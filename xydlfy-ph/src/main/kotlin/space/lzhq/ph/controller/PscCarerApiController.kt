package space.lzhq.ph.controller

import org.dromara.hutool.core.lang.Validator
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequirePscCarerSession
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.domain.PscCarer
import space.lzhq.ph.domain.PscCarerSession
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.ext.getCurrentPscCarer
import space.lzhq.ph.service.IPscCarerService
import space.lzhq.ph.service.IPscCarerSessionService

@RestController
@RequestMapping("/api/psc/carer")
class PscCarerApiController : BaseController() {

    @Autowired
    private lateinit var pscCarerService: IPscCarerService

    @Autowired
    private lateinit var pscCarerSessionService: IPscCarerSessionService

    /**
     * 登录
     * @param employeeNo 工号
     * @param mobile 手机号
     */
    @PostMapping("login")
    @RequireSession
    fun login(
        @RequestParam("employeeNo") employeeNo: String,
        @RequestParam("mobile") mobile: String,
    ): AjaxResult {
        val errorMessage = "工号或手机号不正确"
        if (!Validator.isMobile(mobile)) {
            return AjaxResult.error(errorMessage)
        }

        val pscCarer: PscCarer = pscCarerService.selectPscCarerByEmployeeNo(employeeNo)
            ?: return AjaxResult.error(errorMessage)
        if (pscCarer.mobile != mobile) {
            return AjaxResult.error(errorMessage)
        }

        val session = request.getClientSession()!!
        val existedPscCarerSession =
            pscCarerSessionService.selectPscCarerSessionByEmployeeNoAndClientType(employeeNo, session.clientType)
        if (existedPscCarerSession != null) {
            return if (existedPscCarerSession.openId == session.openId) {
                AjaxResult.success(existedPscCarerSession)
            } else {
                AjaxResult.error("系统检测到您已在别的设备登录")
            }
        }

        val pscCarerSession = PscCarerSession(pscCarer, session)
        pscCarerSessionService.insertPscCarerSession(pscCarerSession)
        return AjaxResult.success(pscCarerSession)
    }

    /**
     * 注销
     */
    @PostMapping("logout")
    @RequireSession
    fun logout(): AjaxResult {
        val session = request.getClientSession()!!
        pscCarerSessionService.deletePscCarerSessionByOpenId(session.openId);
        return AjaxResult.success()
    }

    @GetMapping("me")
    @RequirePscCarerSession
    fun me(): AjaxResult {
        return AjaxResult.success(request.getCurrentPscCarer())
    }
}
