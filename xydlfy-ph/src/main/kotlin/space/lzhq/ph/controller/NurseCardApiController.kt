package space.lzhq.ph.controller

import com.github.pagehelper.PageHelper
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.enums.BaseEnum
import org.mospital.bsoft.BSoftService
import org.mospital.bsoft.PatientInfo
import org.mospital.bsoft.Result
import org.mospital.bsoft.ZhuyuanHistory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.domain.NurseCard
import space.lzhq.ph.domain.NurseCardStatus
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.domain.Relationship
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.INurseCardService
import java.time.format.DateTimeFormatter

@RestController
@RequestMapping("/api/nurseCard")
class NurseCardApiController : BaseController() {

    @Autowired
    private lateinit var nurseCardService: INurseCardService

    private fun queryZhuyuanPatient(idCardNo: String, zhuyuanNo: String): ZhuyuanHistory? {
        val zhuyuanHistoryListResult: Result<List<ZhuyuanHistory>> =
            BSoftService.getZhuyuanHistoryListByIdCardNo(idCardNo)
        return zhuyuanHistoryListResult.data?.firstOrNull { it.admissionNo == zhuyuanNo }
    }

    /**
     * 查询住院患者
     */
    @GetMapping("/getZhuyuanPatient")
    fun getZhuyuanPatient(
        @RequestParam idCardNo: String,
        @RequestParam zhuyuanNo: String,
    ): AjaxResult {
        val zhuyuanHistory: ZhuyuanHistory? = queryZhuyuanPatient(idCardNo, zhuyuanNo)
        return if (zhuyuanHistory == null) {
            AjaxResult.error("查找住院患者失败")
        } else if (zhuyuanHistory.leaveTime != null) {
            AjaxResult.error("该患者已于 ${zhuyuanHistory.leaveTime!!.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm "))} 出院")
        } else {
            AjaxResult.success(zhuyuanHistory)
        }
    }

    /**
     * 申领电子陪护证
     */
    @PostMapping("/apply")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun apply(
        @RequestParam idCardNo: String,
        @RequestParam zhuyuanNo: String,
        @RequestParam relationshipCode: Int,
        @RequestParam nurseNatImage: String,
        @RequestParam nursePhoto: String,
    ): AjaxResult {
        val relationship: Relationship = BaseEnum.ofCode(Relationship::class.java, relationshipCode)
            ?: return AjaxResult.error("关系类型错误")
        val zhuyuanHistory: ZhuyuanHistory = queryZhuyuanPatient(idCardNo, zhuyuanNo)
            ?: return AjaxResult.error("查找住院患者失败")

        if (zhuyuanHistory.leaveTime != null) {
            return AjaxResult.error("该患者已于 ${zhuyuanHistory.leaveTime!!.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm "))} 出院")
        }

        val patientInfoResult: Result<PatientInfo> = BSoftService.getPatientInfoByPatientId(
            patientId = zhuyuanHistory.patientId,
        )
        if (patientInfoResult.isOk()) {
            zhuyuanHistory.patientMobile = patientInfoResult.data?.patientPhone ?: ""
        }

        val activePatient: Patient = request.getCurrentPatient()
        val nurseCard = NurseCard(zhuyuanHistory, activePatient, relationship, nurseNatImage, nursePhoto)
        val ret = nurseCardService.insertNurseCard(nurseCard)
        if (ret > 0) {
            return AjaxResult.success(nurseCard)
        } else {
            return AjaxResult.error("申领失败，请稍后再试")
        }
    }

    /**
     * 查询电子陪护证申请记录
     */
    @GetMapping("/list")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getList(): AjaxResult {
        val activePatient: Patient = request.getCurrentPatient()
        PageHelper.orderBy("id desc")
        val nurseCardList: List<NurseCard> =
            nurseCardService.selectNurseCardList(NurseCard().apply { this.nurseIdCardNo = activePatient.idCardNo })
        return AjaxResult.success(nurseCardList)
    }

    /**
     * 查询电子陪护证详情
     */
    @GetMapping("/detail")
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getDetail(@RequestParam id: Long?): AjaxResult {
        if (id != null) {
            val nurseCard: NurseCard = nurseCardService.selectNurseCardById(id)
            return if (nurseCard.nurseIdCardNo == request.getCurrentPatient().idCardNo) {
                AjaxResult.success(nurseCard)
            } else {
                AjaxResult.error("您无权查看该电子陪护证")
            }
        } else {
            val activePatient: Patient = request.getCurrentPatient()
            val nurseCardList: List<NurseCard> =
                nurseCardService.selectNurseCardList(NurseCard().apply { this.nurseIdCardNo = activePatient.idCardNo })
            val nurseCard: NurseCard? = nurseCardList.firstOrNull { it.status == NurseCardStatus.NORMAL }
            return if (nurseCard != null) {
                AjaxResult.success(nurseCard)
            } else {
                AjaxResult.error("您目前没有可用的电子陪护证")
            }
        }
    }

}