package space.lzhq.ph.controller

import com.ruoyi.common.core.domain.AjaxResult
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.service.ITipService
import java.util.*

@RestController
@RequestMapping("/open/tip")
class TipController(
    private val tipService: ITipService
) {

    @GetMapping
    fun index(@RequestParam codes: String?): AjaxResult {
        val tipMap = tipService
            .selectByCodeList(codes?.split(","))
            .filter { (it.effectiveTime..it.expireTime).contains(Date()) }
            .associateBy { it.code }
        return AjaxResult.success(tipMap)
    }

}