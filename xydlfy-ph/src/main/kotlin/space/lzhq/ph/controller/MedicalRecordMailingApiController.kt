package space.lzhq.ph.controller

import org.dromara.hutool.http.server.servlet.ServletUtil
import com.github.pagehelper.PageHelper
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.common.PaymentKit
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.MedicalRecordMailing
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.ext.getCurrentPatient
import space.lzhq.ph.service.IMedicalRecordMailingService
import space.lzhq.ph.service.IWxPaymentService
import java.util.*

/**
 * 病历邮寄Controller
 *
 */
@RestController
@RequestMapping("/api/medicalRecordMailing")
class MedicalRecordMailingApiController(
    private val wxPaymentService: IWxPaymentService,
    private val medicalRecordMailingService: IMedicalRecordMailingService
) : BaseController() {

    /**
     * 病历申请记录分页展示
     */
    @RequireActivePatient
    @GetMapping("page")
    fun page(
        @RequestParam(defaultValue = "25") pageSize: Int,
        @RequestParam(value = "pageNum", defaultValue = "1") pageNumber: Int
    ): TableDataInfo {
        PageHelper.startPage<MedicalRecordMailing>(pageNumber, pageSize, "id desc")

        val currentPatient: Patient = request.getCurrentPatient()
        val recordMailings = medicalRecordMailingService.lambdaQuery()
            .eq(MedicalRecordMailing::getPatientNo, currentPatient.patientNo)
            .list()
        return getDataTable(recordMailings)
    }

    /**
     * 病历申请
     */
    @RequireActivePatient
    @PostMapping("applyMedicalRecord")
    fun applyMedicalRecord(
        @RequestParam zhuyuanNo: String,
        @RequestParam address: String,
    ): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()

        val recordMailing = MedicalRecordMailing().apply {
            this.patientNo = currentPatient.patientNo
            this.jzCardNo = currentPatient.jzCardNo
            this.zhuyuanNo = zhuyuanNo
            this.name = currentPatient.name
            this.mobile = currentPatient.mobile
            this.idCardNo = currentPatient.idCardNo
            this.openid = currentPatient.openId
            this.address = address
            this.status = MedicalRecordMailing.STATUS_APPLIED
            this.createTime = Date()
            this.updateTime = Date()
        }


        medicalRecordMailingService.save(recordMailing)
        return AjaxResult.success()
    }

    /**
     * 病历复印支付
     */
    @RequireActivePatient
    @PostMapping("pay")
    fun pay(@RequestParam id: Long): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        val recordMailing = medicalRecordMailingService.getById(id) ?: return AjaxResult.error("不存在的病历ID")
        if (recordMailing.patientNo != currentPatient.patientNo) return AjaxResult.error("您没有支付该病历的权限！")
        if (recordMailing.status != MedicalRecordMailing.STATUS_CONFIRMED) return AjaxResult.error("只有病例状态为已已确认病历信息时才允许支付！")

        // 开始支付逻辑
        val ip: String = ServletUtil.getClientIP(request)
        val serviceType = ServiceType.BL
        val outTradeNo: String = PaymentKit.newOrderId(serviceType)
        val amount: Long = recordMailing.copyingFee + recordMailing.expressFee
        val wxPayMpOrderResult = WeixinExt.pay(
            serviceType = serviceType,
            amount = amount.toInt(),
            ip = ip,
            outTradeNo = outTradeNo,
            patient = currentPatient,
            remark = "病历邮寄-${recordMailing.zhuyuanNo}",
        ) { request, _ ->
            wxPaymentService.createAndSave(serviceType, currentPatient, request)
            recordMailing.zyPayNo = request.outTradeNo
            medicalRecordMailingService.updateById(recordMailing)
        }

        return AjaxResult.success(
            mapOf(
                "appId" to wxPayMpOrderResult.appId,
                "timeStamp" to wxPayMpOrderResult.timeStamp,
                "nonceStr" to wxPayMpOrderResult.nonceStr,
                "package" to wxPayMpOrderResult.packageValue,
                "signType" to wxPayMpOrderResult.signType,
                "paySign" to wxPayMpOrderResult.paySign,
                "zyOrderNo" to outTradeNo
            )
        )
    }

}