package space.lzhq.ph.controller

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.ruoyi.common.core.domain.AjaxResult
import jakarta.servlet.http.HttpServletRequest
import org.dromara.hutool.core.codec.binary.Base64
import org.dromara.hutool.core.data.IdcardUtil
import org.mospital.common.TokenManager
import org.mospital.donggang.einvoice.DetailForm
import org.mospital.donggang.einvoice.EInvoiceService
import org.mospital.donggang.einvoice.ListForm
import org.mospital.donggang.einvoice.ListResult
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import space.lzhq.ph.annotation.RequireActivePatient
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.domain.Patient
import space.lzhq.ph.dto.EInvoiceDto
import space.lzhq.ph.ext.getCurrentPatient
import java.time.LocalDateTime

@Controller
class InvoiceController {

    @GetMapping("/api/invoice")
    @ResponseBody
    @RequireSession
    @RequireActivePatient(shouldBeMenzhenPatient = true)
    fun getInvoices(request: HttpServletRequest): AjaxResult {
        val currentPatient: Patient = request.getCurrentPatient()
        if (!IdcardUtil.isValidCard(currentPatient.idCardNo)) {
            return AjaxResult.error("当前患者未预留有效身份证号，无法查询电子发票")
        }

        val startLocalDateTime = LocalDateTime.now().minusMonths(12)
        val form = ListForm(
            businessBeginTime = startLocalDateTime,
            payerCode = currentPatient.idCardNo
        )
        val result: List<EInvoiceDto> = EInvoiceService.list(form)
            .records
            .filterNot { it.mode == "2" }
            .sortedByDescending { it.businessTime }
            .map { EInvoiceDto(it) }
        return AjaxResult.success(result)
    }

    @GetMapping("/open/invoice/download")
    fun download(
        @RequestParam invoiceToken: String
    ): ResponseEntity<StreamingResponseBody> {
        val einvoice: ListResult.EInvoice = TokenManager.parseToken(invoiceToken, jacksonTypeRef())
            ?: return ResponseEntity.notFound().build()
        val detailForm = DetailForm(
            serialNumber = einvoice.serialNumber,
            format = "IMG"
        )
        val detail = EInvoiceService.detail(detailForm)
        return ResponseEntity.ok()
            .contentType(MediaType.parseMediaType("image/jpeg"))
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=${einvoice.serialNumber}.jpeg")
            .body(StreamingResponseBody {
                Base64.decodeToStream(detail.layoutFile, it, true)
            })
    }

}