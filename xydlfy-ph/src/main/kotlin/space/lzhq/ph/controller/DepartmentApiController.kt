package space.lzhq.ph.controller

import com.github.pagehelper.PageHelper
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.domain.Department
import space.lzhq.ph.service.IDepartmentService

@RestController
@RequestMapping("/open/department")
class DepartmentApiController : BaseController() {

    @Autowired
    private lateinit var departmentService: IDepartmentService

    @GetMapping("/paginate")
    @ResponseBody
    fun paginate(
        @RequestParam pageNo: Int?,
        @RequestParam pageSize: Int?,
        @RequestParam kw: String?
    ): TableDataInfo {
        PageHelper.startPage<Department>(pageNo ?: 1, pageSize ?: 25, "sort_no desc, simple_pinyin asc")
        val list = departmentService.selectDepartmentListByKeyword(kw)
        return getDataTable(list)
    }

    @GetMapping("/show/{id}")
    @ResponseBody
    fun show(@PathVariable id: String): AjaxResult {
        val department: Department? = departmentService.selectDepartmentById(id)

        return if (department == null) {
            AjaxResult.error("找不到指定的科室")
        } else {
            AjaxResult.success(department)
        }
    }

}