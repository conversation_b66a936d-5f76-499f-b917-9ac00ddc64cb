package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.core.page.TableDataInfo
import lombok.extern.slf4j.Slf4j
import org.apache.shiro.authz.annotation.RequiresPermissions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.domain.WxTransfer
import space.lzhq.ph.domain.WxTransferSearchForm
import space.lzhq.ph.service.IWxTransferService

@Controller
@RequestMapping("/ph/wx_transfer")
@Slf4j
class WxTransferAdminController : BaseController() {

    @Autowired
    private lateinit var service: IWxTransferService

    private val prefix = "ph/wx_transfer"

    @RequiresPermissions("ph:wx_transfer:view")
    @GetMapping
    fun index(): String {
        return "$prefix/index"
    }

    @RequiresPermissions("ph:wx_transfer:view")
    @PostMapping("/page")
    @ResponseBody
    fun page(searchForm: WxTransferSearchForm): TableDataInfo {
        startPage("create_time DESC")
        val list = service.listBySearchForm(searchForm)
        return getDataTable(list)
    }

    @RequiresPermissions("ph:wx_transfer:requestWxTransfer")
    @PostMapping("/requestWxTransfer/{id}")
    @ResponseBody
    fun requestWxTransfer(@PathVariable id: String): AjaxResult {
        val wxTransfer = service.getById(id)!!
        if (wxTransfer.transferState in WxTransfer.FINAL_STATES) {
            return AjaxResult.error("该笔订单不支持重新发起转账")
        }
        if ("SUCCESS".equals(wxTransfer.transferState)) {
            return AjaxResult.error("该笔订单已成功发起转账")
        }

        service.renewOrderNo(wxTransfer)
        service.requestTransfer(wxTransfer)
        return AjaxResult.success()
    }

    @RequiresPermissions("ph:wx_transfer:refreshWxTransfer")
    @PostMapping("/refreshWxTransfer/{id}")
    @ResponseBody
    fun refreshWxTransfer(@PathVariable id: String): AjaxResult {
        val wxTransfer = service.getById(id)!!
        service.updateTransferState(wxTransfer)
        return AjaxResult.success()
    }

}