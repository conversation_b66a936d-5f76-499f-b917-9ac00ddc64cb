package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.domain.DoctorCategory
import space.lzhq.ph.service.IDoctorCategoryService

@RestController
@RequestMapping("/open/doctorCategory")
class DoctorCategoryApiController : BaseController() {

    @Autowired
    private lateinit var doctorCategoryService: IDoctorCategoryService

    @GetMapping("/dept/paginate")
    @ResponseBody
    fun paginate(): AjaxResult {
        startPage()
        val example = DoctorCategory().apply {
            this.parentId = 1
        }
        val list = doctorCategoryService.selectDoctorCategoryList(example)
        return AjaxResult.success(getDataTable(list))
    }

}