package space.lzhq.ph.controller

import cn.binarywang.wx.miniapp.api.WxMaService
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult
import com.github.binarywang.wxpay.bean.notify.*
import com.github.binarywang.wxpay.bean.result.WxPayRefundQueryResult
import com.github.binarywang.wxpay.bean.transfer.TransferBillsNotifyResult
import com.ruoyi.common.annotation.ValidIdCard
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import com.ruoyi.common.exception.file.FileNameLengthLimitExceededException
import com.ruoyi.common.exception.file.FileSizeLimitExceededException
import com.ruoyi.system.service.ISysConfigService
import com.tencentcloudapi.ocr.v20181119.models.ResidenceBookletOCRResponse
import jakarta.validation.constraints.NotBlank
import me.chanjar.weixin.common.bean.ocr.WxOcrIdCardResult
import me.chanjar.weixin.common.error.WxErrorException
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import org.mospital.bsoft.BSoftService
import org.mospital.bsoft.PatientInfo
import org.mospital.common.StringKit
import org.mospital.common.WeixinFaceIdentification
import org.mospital.common.WeixinFaceIdentifyResult
import org.mospital.jackson.JacksonKit
import org.mospital.wecity.mip.WeixinMipSetting
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.common.ClientType
import space.lzhq.ph.common.ServiceType
import space.lzhq.ph.domain.MedicalRecordMailing
import space.lzhq.ph.domain.PatientInfoToken
import space.lzhq.ph.domain.Session
import space.lzhq.ph.ext.FileUploader
import space.lzhq.ph.ext.HisExt
import space.lzhq.ph.ext.TencentCloudApi
import space.lzhq.ph.ext.WeixinExt
import space.lzhq.ph.service.*
import java.io.File
import java.nio.file.Files
import java.util.*

@RestController
@Validated
class WxController : BaseController() {

    @Autowired
    private lateinit var wxMaService: WxMaService

    @Autowired
    private lateinit var sessionService: ISessionService

    @Autowired
    private lateinit var wxPaymentService: IWxPaymentService

    @Autowired
    private lateinit var wxRefundService: IWxRefundService

    @Autowired
    private lateinit var sysConfigService: ISysConfigService

    @Autowired
    private lateinit var mipWxOrderService: IMipWxOrderService

    @Autowired
    private lateinit var medicalRecordMailingService: IMedicalRecordMailingService

    @Autowired
    private lateinit var wxTransferService: IWxTransferService

    @PostMapping("/open/wx/login")
    fun login(@RequestParam code: String): AjaxResult {
        val code2sessionResult: WxMaJscode2SessionResult = try {
            wxMaService.userService.getSessionInfo(code)
        } catch (e: WxErrorException) {
            return AjaxResult.error(e.error.errorMsg)
        }

        val sid: String = UUID.randomUUID().toString()

        sessionService.deleteSessionByOpenId(code2sessionResult.openid)
        val newWxSession = Session().apply {
            this.id = sid
            this.clientType = ClientType.WEIXIN.code
            this.openId = code2sessionResult.openid
            this.unionId = code2sessionResult.unionid ?: ""
            this.sessionKey = code2sessionResult.sessionKey
        }
        sessionService.insertSession(newWxSession)

        response.setHeader(HttpHeaders.AUTHORIZATION, sid)
        return AjaxResult.success(mapOf(HttpHeaders.AUTHORIZATION to sid))
    }

    private fun ok(msg: String = "SUCCESS"): String = WxPayNotifyResponse.success(msg)
    private fun fail(msg: String = "FAIL"): String = WxPayNotifyResponse.fail(msg)

    @PostMapping("/open/wx/onWXPay")
    fun onWXPay(@RequestBody xmlData: String): String {
        try {
            var notifyResult: WxPayOrderNotifyResult? = null
            var notifyParseException: Exception? = null
            for (payService in WeixinExt.getWxPayServices()) {
                try {
                    notifyResult = payService.parseOrderNotifyResult(xmlData)
                    break
                } catch (e: Exception) {
                    notifyParseException = e
                    continue
                }
            }
            if (notifyResult == null) {
                return fail(if (notifyParseException != null) ExceptionUtils.getMessage(notifyParseException) else "解析通知失败")
            }

            val payment = wxPaymentService.selectWxPaymentByZyPayNo(notifyResult.outTradeNo)
                ?: return fail("本地无此订单: ${notifyResult.outTradeNo}")

            val wxPayService = WeixinExt.getWxPayServiceByMchId(notifyResult.mchId)
                ?: return fail("找不到对应的支付服务: ${notifyResult.mchId}")

            synchronized(payment.openid.intern()) {
                if (!payment.wxPayNo.isNullOrBlank()) {
                    // 本地订单状态已更新
                    return ok()
                }

                val wxPayOrderQueryResult = wxPayService.queryOrder(null, notifyResult.outTradeNo)
                val updateSuccess = wxPaymentService.updateOnPay(payment, wxPayOrderQueryResult)
                if (!updateSuccess) {
                    return fail("更新订单记录的微信交易状态失败")
                }

                HisExt.recharge(payment.id)
                return ok()
            }
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return fail(ExceptionUtils.getMessage(e))
        }
    }

    @PostMapping("/open/wx/onWXRefund")
    fun onWXRefund(@RequestBody xmlData: String): String {
        try {
            var notifyResult: WxPayRefundNotifyResult? = null
            var notifyParseException: Exception? = null
            for (payService in WeixinExt.getWxPayServices()) {
                try {
                    notifyResult = payService.parseRefundNotifyResult(xmlData)
                    break
                } catch (e: Exception) {
                    notifyParseException = e
                    continue
                }
            }
            if (notifyResult == null) {
                return fail(if (notifyParseException != null) ExceptionUtils.getMessage(notifyParseException) else "解析通知失败")
            }

            val zyRefundNo = notifyResult.reqInfo.outRefundNo
            val wxRefund = wxRefundService.selectWxRefundByZyRefundNo(zyRefundNo)
                ?: return fail("本地无此订单: 掌医退款单号=$zyRefundNo")
            if (!wxRefund.wxRefundNo.isNullOrBlank()) {
                // 本地订单状态已更新
                return ok()
            }

            val wxPayService = WeixinExt.getWxPayServiceByMchId(notifyResult.mchId)
                ?: return fail("找不到对应的支付服务: ${notifyResult.mchId}")
            val wxPayRefundQueryResult = wxPayService.refundQuery(null, null, zyRefundNo, null)
            val refundRecord: WxPayRefundQueryResult.RefundRecord = wxPayRefundQueryResult.refundRecords[0]
            val ok = wxRefundService.updateOnWXRefund(wxRefund, refundRecord)
            return if (ok) {
                ok()
            } else {
                fail("更新退款记录失败")
            }
        } catch (e: Exception) {
            logger.debug(e.message, e)
            return fail(ExceptionUtils.getMessage(e))
        }
    }

    @PostMapping(value = ["/open/wx/onWxInsurancePay"], produces = [MediaType.APPLICATION_XML_VALUE])
    fun onWxInsurancePay(@RequestBody xmlData: String): String {
        try {
            logger.debug("notify: onWxInsurancePay: $xmlData")
            val insuranceOrderNotifyResult = WxInsuranceOrderNotifyResult.fromXML(xmlData)
            mipWxOrderService.refreshOrderStatus(insuranceOrderNotifyResult.medTransId)
        } catch (e: Exception) {
            logger.debug(e.message, e)
        }

        val response = WxInsuranceOrderNotifyResponse.success(signKey = WeixinMipSetting.ma.mipKey)
        return response.toXML()

    }

    @PostMapping("/open/wx/idCardOcr")
    @RequireSession(ClientType.WEIXIN)
    fun idCardOcr(file: MultipartFile): AjaxResult {
        var tempFile: File? = null
        return try {
            val fileNameLength = file.originalFilename!!.length
            if (fileNameLength > FileUploader.DEFAULT_FILE_NAME_LENGTH) {
                throw FileNameLengthLimitExceededException(FileUploader.DEFAULT_FILE_NAME_LENGTH)
            }

            val size = file.size
            if (size > 2 * 1024 * 1024) {
                throw FileSizeLimitExceededException(2)
            }

            val extensionName = FileUploader.getExtension(file)
            tempFile = Files.createTempFile("ocr-", ".$extensionName").toFile()
            file.transferTo(tempFile)
            val result = WeixinExt.idCardOcr(tempFile)
            AjaxResult.success(result)
        } catch (e: Exception) {
            logger.error("idCardOcr failed", e)
            AjaxResult.error(e.message)
        } finally {
            FileUtils.deleteQuietly(tempFile)
        }
    }

    @GetMapping("/open/wx/phoneNumber")
    @RequireSession(ClientType.WEIXIN)
    fun phoneNumber(@RequestParam @NotBlank(message = "code不能为空") code: String): AjaxResult {
        val errorMessage = "获取手机号失败"
        return try {
            val phoneNoInfo = wxMaService.userService.getPhoneNumber(code)
            if (phoneNoInfo != null) {
                AjaxResult.success(phoneNoInfo)
            } else {
                AjaxResult.error(errorMessage)
            }
        } catch (e: WxErrorException) {
            logger.error(errorMessage, e)
            AjaxResult.error(errorMessage)
        }
    }

    @PostMapping("/open/wx/identifyFaceAndQueryPatients")
    @RequireSession(ClientType.WEIXIN)
    fun identifyFaceAndQueryPatients(
        verifyResult: String,
        idCardNo: String,
        name: String
    ): AjaxResult {
        try {
            val identificationResult: WeixinFaceIdentifyResult = WeixinFaceIdentification.identify(
                accessToken = wxMaService.accessToken,
                verifyResult = verifyResult
            )
            if (!identificationResult.isOk()) {
                return AjaxResult.error("人脸识别失败")
            }

            if (!identificationResult.matches(idCardNo, name)) {
                return AjaxResult.error("人脸不匹配")
            }

            val patientInfoResponse = BSoftService.getPatientsByIdCard(idCardNo)
            return if (patientInfoResponse.isOk()) {
                val patients: List<Map<String, Any>> =
                    patientInfoResponse.data?.filter { HisExt.matchesChinese(it.patientName, name) }?.map {
                        it.toMap()
                    } ?: emptyList()
                AjaxResult.success(patients)
            } else {
                AjaxResult.error(patientInfoResponse.message)
            }
        } catch (e: Exception) {
            logger.error(e.message, e)
            return AjaxResult.error(e.message ?: "发生未知错误")
        }
    }

    @GetMapping("/open/wx/patients")
    @RequireSession(ClientType.WEIXIN)
    fun queryPatients(
        @RequestParam @NotBlank(message = "身份证号不能为空") @ValidIdCard(message = "身份证号不正确") idCardNo: String,
        @RequestParam @NotBlank(message = "姓名不能为空") name: String
    ): AjaxResult {
        val patientInfoResponse = BSoftService.getPatientsByIdCard(idCardNo)
        return if (patientInfoResponse.isOk()) {
            val patients: List<Map<String, Any>> =
                patientInfoResponse.data?.filter { HisExt.matchesChinese(it.patientName, name) }?.map {
                    it.toMap()
                } ?: emptyList()
            AjaxResult.success(patients)
        } else {
            AjaxResult.error(patientInfoResponse.message)
        }
    }

    @PostMapping("/open/wx/idCardOcrAndQueryPatients")
    @RequireSession(ClientType.WEIXIN)
    fun idCardOcrAndQueryPatients(file: MultipartFile): AjaxResult {
        var tempFile: File? = null
        return try {
            val fileNameLength = file.originalFilename!!.length
            if (fileNameLength > FileUploader.DEFAULT_FILE_NAME_LENGTH) {
                throw FileNameLengthLimitExceededException(FileUploader.DEFAULT_FILE_NAME_LENGTH)
            }

            val size = file.size
            if (size > 2 * 1024 * 1024) {
                throw FileSizeLimitExceededException(2)
            }

            val extensionName = FileUploader.getExtension(file)
            tempFile = Files.createTempFile("ocr-", ".$extensionName").toFile()
            file.transferTo(tempFile)
            val ocrResult: WxOcrIdCardResult = WeixinExt.idCardOcr(tempFile)

            val patientInfoResponse = BSoftService.getPatientsByIdCard(ocrResult.id)
            return if (patientInfoResponse.isOk()) {
                val patients: List<Map<String, Any>> =
                    patientInfoResponse.data?.filter { HisExt.matchesChinese(it.patientName, ocrResult.name) }?.map {
                        it.toMap()
                    } ?: emptyList()
                AjaxResult.success(
                    mapOf<String, Any>(
                        "ocrResult" to ocrResult,
                        "patients" to patients
                    )
                )
            } else {
                AjaxResult.error(patientInfoResponse.message)
            }
        } catch (e: Exception) {
            logger.error("idCardOcr failed", e)
            AjaxResult.error(e.message)
        } finally {
            FileUtils.deleteQuietly(tempFile)
        }
    }

    @PostMapping("/open/wx/residenceBookletOcr")
    @RequireSession(ClientType.WEIXIN)
    fun residenceBookletOcr(file: MultipartFile): AjaxResult {
        var tempFile: File? = null
        return try {
            val fileNameLength = file.originalFilename!!.length
            if (fileNameLength > FileUploader.DEFAULT_FILE_NAME_LENGTH) {
                throw FileNameLengthLimitExceededException(FileUploader.DEFAULT_FILE_NAME_LENGTH)
            }

            val size = file.size
            if (size > 5 * 1024 * 1024) {
                throw FileSizeLimitExceededException(5)
            }

            val extensionName = FileUploader.getExtension(file)
            tempFile = Files.createTempFile("ocr-", ".$extensionName").toFile()
            file.transferTo(tempFile)
            val ocrResult: ResidenceBookletOCRResponse = TencentCloudApi.residenceBookletOCR(tempFile)
            return AjaxResult.success(ocrResult)
        } catch (e: Exception) {
            logger.error("idCardOcr failed", e)
            AjaxResult.error(e.message)
        } finally {
            FileUtils.deleteQuietly(tempFile)
        }
    }

    @PostMapping("/open/wx/residenceBookletOcrAndQueryPatients")
    @RequireSession(ClientType.WEIXIN)
    fun residenceBookletOcrAndQueryPatients(file: MultipartFile): AjaxResult {
        var tempFile: File? = null
        return try {
            val fileNameLength = file.originalFilename!!.length
            if (fileNameLength > FileUploader.DEFAULT_FILE_NAME_LENGTH) {
                throw FileNameLengthLimitExceededException(FileUploader.DEFAULT_FILE_NAME_LENGTH)
            }

            val size = file.size
            if (size > 5 * 1024 * 1024) {
                throw FileSizeLimitExceededException(5)
            }

            val extensionName = FileUploader.getExtension(file)
            tempFile = Files.createTempFile("ocr-", ".$extensionName").toFile()
            file.transferTo(tempFile)
            val ocrResult: ResidenceBookletOCRResponse = TencentCloudApi.residenceBookletOCR(tempFile)

            val patientInfoResponse = BSoftService.getPatientsByIdCard(ocrResult.idCardNumber)
            return if (patientInfoResponse.isOk()) {
                val patients: List<Map<String, Any>> =
                    patientInfoResponse.data?.filter { HisExt.matchesChinese(it.patientName, ocrResult.name) }?.map {
                        it.toMap()
                    } ?: emptyList()
                AjaxResult.success(
                    mapOf<String, Any>(
                        "ocrResult" to ocrResult,
                        "patients" to patients
                    )
                )
            } else {
                AjaxResult.error(patientInfoResponse.message)
            }
        } catch (e: Exception) {
            logger.error("idCardOcr failed", e)
            AjaxResult.error(e.message)
        } finally {
            FileUtils.deleteQuietly(tempFile)
        }
    }

    fun PatientInfo.toMap(): Map<String, Any> {
        val patientInfoToken = PatientInfoToken.builder()
            .patientIdCardNo(patientIdCard)
            .patientName(patientName)
            .patientId(patientId)
            .patientCardNo(cardNo)
            .patientMobile(patientPhone.orEmpty())
            .build()
        return mapOf<String, Any>(
            "patientInfoToken" to patientInfoToken.generateToken(),
            "patientIdCardNo" to StringKit.hideIdCardNo(patientInfoToken.patientIdCardNo),
            "patientName" to StringKit.hideName(patientInfoToken.patientName),
            "patientId" to patientInfoToken.patientId,
            "patientCardNo" to StringKit.hideIdCardNo(patientInfoToken.patientCardNo),
            "balance" to cardBalance
        )
    }

    /**
     * 微信转账通知处理
     *
     * 关于 ResponseEntity<String> 的处理:
     * 1. produces = [MediaType.APPLICATION_JSON_VALUE] 声明此接口产生 JSON 响应
     * 2. 响应头将被设置为 Content-Type: application/json
     * 3. 处理规则:
     *    - 如果返回的 String 已经是合法的 JSON 字符串，则直接返回
     *    - 如果是普通字符串，则会被包装成 JSON 字符串（如 "hello" 变成 "\"hello\""）
     *    - WxPayNotifyV3Response.success/fail 返回的已经是格式化好的 JSON 字符串
     */
    @PostMapping(value = ["/open/wx/onWxTransfer"], produces = [MediaType.APPLICATION_JSON_VALUE])
    fun onWxTransfer(@RequestBody @NotBlank(message = "请求数据不能为空") jsonString: String): ResponseEntity<String> {
        return try {
            logger.debug("notify: onWxTransfer: $jsonString")

            // 构建签名头信息，用于验证请求的合法性
            val signatureHeader = SignatureHeader.builder()
                .serial(request.getHeader("Wechatpay-Serial"))
                .signature(request.getHeader("Wechatpay-Signature"))
                .timeStamp(request.getHeader("Wechatpay-Timestamp"))
                .nonce(request.getHeader("Wechatpay-Nonce"))
                .build()
            // 解析通知结果，验证签名
            val notifyResult: TransferBillsNotifyResult =
                WeixinExt.getWxPayServiceForMenZhen().parseTransferBillsNotifyV3Result(jsonString, signatureHeader)
            logger.debug("notify: onWxTransfer: ${JacksonKit.writeValueAsString(notifyResult.result)}")

            // 处理业务逻辑
            val outBillNo = notifyResult.result.outBillNo
            val wxTransferOptional = wxTransferService.getOneByOutBillNo(outBillNo)
            if (wxTransferOptional.isPresent) {
                val wxTransfer = wxTransferOptional.get()
                wxTransferService.updateTransferState(wxTransfer)
            } else {
                logger.warn("notify: onWxTransfer: 本地无此订单: $outBillNo")
            }

            // 成功响应：
            // 1. 状态码 200
            // 2. WxPayNotifyV3Response.success 返回符合微信规范的 JSON 字符串
            // 3. SpringBoot 将直接返回这个 JSON 字符串，不会进行额外处理
            ResponseEntity.ok(WxPayNotifyV3Response.success("SUCCESS"))
        } catch (e: Exception) {
            logger.error(e.message, e)
            // 失败响应：
            // 1. 状态码 500
            // 2. WxPayNotifyV3Response.fail 返回包含错误信息的 JSON 字符串
            // 3. SpringBoot 将直接返回这个 JSON 字符串，不会进行额外处理
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(WxPayNotifyV3Response.fail(e.message))
        }
    }

    /**
     * 获取微信小程序的 accessToken
     */
    @GetMapping("/open/wx/accessToken")
    fun accessToken(@RequestParam authCode: String): AjaxResult {
        val expectedAuthCode = "qUKVf-KQpDhDwqnUnmYB3R*vsEtwQLhg"
        if (expectedAuthCode != authCode) {
            return AjaxResult.error("授权码不正确")
        }
        return try {
            AjaxResult.success("ok", wxMaService.accessToken)
        } catch (e: Exception) {
            AjaxResult.error(e.message)
        }
    }

}