package space.lzhq.ph.controller

import com.ruoyi.common.core.domain.AjaxResult
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import space.lzhq.ph.annotation.RequireSession
import space.lzhq.ph.domain.JubaoCase
import space.lzhq.ph.domain.Session
import space.lzhq.ph.ext.getClientSession
import space.lzhq.ph.service.IJubaoCaseService
import java.util.*
import jakarta.servlet.http.HttpServletRequest

@RestController
@RequestMapping("/api/jubao/case")
class JubaoCaseApiController {

    @Autowired
    private lateinit var jubaoCaseService: IJubaoCaseService

    @RequireSession
    @PostMapping
    fun create(@RequestBody jubao: JubaoCase, request: HttpServletRequest): AjaxResult {
        val session: Session = request.getClientSession()!!
        jubao.openId = session.openId
        jubao.clientType = session.clientType
        jubao.createTime = Date()
        jubao.status = JubaoCase.STATUS_INITED
        jubaoCaseService.insertJubaoCase(jubao)
        return AjaxResult.success(jubao)
    }

    @RequireSession
    @GetMapping()
    fun query(request: HttpServletRequest): AjaxResult {
        val session: Session = request.getClientSession()!!
        val list: List<JubaoCase> = jubaoCaseService.selectJubaoCaseList(JubaoCase().apply {
            openId = session.openId
        })
        return AjaxResult.success(list)
    }

}