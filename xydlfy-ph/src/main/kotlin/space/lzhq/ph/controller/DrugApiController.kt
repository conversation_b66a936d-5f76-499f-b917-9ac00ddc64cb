package space.lzhq.ph.controller

import com.github.pagehelper.PageHelper
import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.page.TableDataInfo
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.domain.Drug
import space.lzhq.ph.service.IDrugService

@RestController
@RequestMapping("/open/drug")
class DrugApiController : BaseController() {

    @Autowired
    private lateinit var drugService: IDrugService

    @GetMapping("/paginate")
    fun paginate(
        @RequestParam(defaultValue = "100") pageSize: Int,
        @RequestParam(defaultValue = "1") pageNumber: Int,
        @RequestParam(defaultValue = "") keyword: String,
    ): TableDataInfo {
        PageHelper.startPage<Drug>(pageNumber, pageSize, "no asc")
        val queryTemplate = Drug().apply {
            this.name = keyword
        }
        val list = drugService.selectDrugList(queryTemplate)
        return getDataTable(list)
    }

}