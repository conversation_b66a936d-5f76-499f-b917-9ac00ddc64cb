package space.lzhq.ph.controller

import com.ruoyi.common.core.controller.BaseController
import com.ruoyi.common.core.domain.AjaxResult
import kotlinx.coroutines.*
import org.mospital.bsoft.*
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import space.lzhq.ph.common.Values
import space.lzhq.ph.domain.Doctor
import space.lzhq.ph.domain.DoctorCategory
import space.lzhq.ph.dto.ReservationTicketDto
import space.lzhq.ph.ext.toAjaxResult
import space.lzhq.ph.model.DoctorWrapper
import space.lzhq.ph.service.DepartmentKit
import space.lzhq.ph.service.IDoctorCategoryService
import space.lzhq.ph.service.IDoctorService
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter

@RestController
@RequestMapping("/open/schedule")
class ScheduleApiController(
    private val doctorService: IDoctorService,
    private val doctorCategoryService: IDoctorCategoryService,
) : BaseController() {

    @GetMapping("indexedDepartments")
    fun indexedDepartments(
        @RequestParam(name = "reload", required = false, defaultValue = "false") reload: Boolean
    ): AjaxResult {
        if (Values.INDEXED_DEPARTMENTS.isEmpty() || reload) {
            DepartmentKit.sync()
        }
        return AjaxResult.success(Values.INDEXED_DEPARTMENTS)
    }

    /**
     * 掌医本地医生列表，可适用于按医生分类查询
     */
    @GetMapping("doctorList")
    fun doctorList(doctor: DoctorWrapper): AjaxResult {
        startPage("sort_no desc")
        val list: List<Doctor> = doctorService.selectDoctorList(doctor).onEach {
            it.department = null
        }
        return AjaxResult.success(getDataTable(list))
    }

    /**
     * 查询排班医生
     * @param departmentCode 科室代码
     * @param date 排班日期
     */
    @GetMapping("doctors")
    fun doctors(
        @RequestParam departmentCode: String,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate,
    ): AjaxResult {
        val doctorsResult: Result<List<ReservationDoctor>> = runBlocking {
            BSoftService.me.getReservationDoctorList(
                ReservationDoctorForm(
                    deptId = departmentCode,
                    reserveTime = date,
                    sourceFlag = 1
                )
            )
        }

        // 拼接科室信息
        val example = DoctorCategory().apply {
            this.parentId = 1
            this.categoryCode = departmentCode
        }
        val doctorCategory = doctorCategoryService.selectDoctorCategoryList(example).first()
        val result = mapOf(
            "department" to doctorCategory,
            "doctorList" to doctorsResult.data
        )
        return doctorsResult.toAjaxResult().data(result)
    }

    /**
     * 查询一段时间排班医生。用于前端展示一段时间科室是否存在可预约医生
     */
    @GetMapping("queryDoctorByDays")
    fun queryDoctorByDays(
        @RequestParam departmentCode: String,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate,
        @RequestParam(defaultValue = "1") days: Long
    ): AjaxResult {
        if (days > 10) return AjaxResult.error("最大查询天数为10天")

        val dateRange = (0 until days).map { date.plusDays(it) }
        val doctorsResult = runBlocking {
            dateRange
                .map { reserveTime ->
                    async {
                        val form = ReservationDoctorForm(deptId = departmentCode, reserveTime = reserveTime, sourceFlag = 1)
                        BSoftService.me.getReservationDoctorList(form)
                    }
                }.toList()
                .awaitAll()
                .mapIndexed { index, result ->
                    dateRange[index] to result.data.orEmpty()
                }
                .toMap()
        }

        // 添加科室、医生关联
        CoroutineScope(Dispatchers.IO).launch {
            // 整理医生数据，关联医生分类中的科室信息
            val doctorIdSet = doctorsResult.values.flatten().map { it.doctorId }.toSet()
            doctorIdSet.onEach {
                // 分别添加医生和科室的映射关系
                doctorCategoryService.addRelation(departmentCode, it)
            }
        }
        return AjaxResult.success(doctorsResult)
    }

    /**
     * 查询指定医生一段时间的排班情况
     * @param doctorCode 医生代码
     */
    @GetMapping("queryByDoctorCode")
    fun queryByDoctorCode(@RequestParam doctorCode: String): AjaxResult {
        val result = runBlocking {
            BSoftService.me.getReservationComingDoctor(ReservationComingDoctorForm(doctorCode))
        }
        return result.toAjaxResult()
    }

    /**
     * 查询排班号源
     * @param departmentCode 科室代码
     * @param doctorCode 医生代码
     * @param date 排班日期
     * @param ampm 上午下午，参见 period.code ：1=上午，2=下午，3=晚上
     */
    @GetMapping("tickets")
    fun tickets(
        @RequestParam departmentCode: String?,
        @RequestParam doctorCode: String,
        @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) date: LocalDate,
        @RequestParam ampm: Int,
    ): AjaxResult {
        val fixedAmpm = AMPM.fromCode(ampm)
            ?: return AjaxResult.error("参数错误[ampm=$ampm]")

        if (date < LocalDate.now()) {
            return AjaxResult.error("只能查询今天及以后的号源")
        }

        val ticketsResult: Result<List<ReservationTicket>> =
            BSoftService.getReservationTicketList(
                ReservationTicketForm(
                    // 注意这里将医生编码作为科室编码传入，并不是BUG
                    deptId = doctorCode,
                    doctorId = "",
                    registerDate = date,
                    ampm = fixedAmpm.code,
                    sourceFlag = 1
                )
            )
        return if (ticketsResult.isOk()) {
            val now = LocalDateTime.now()
            val tickets = (ticketsResult.data?:emptyList()).filter {
               try {
                   // Check if the period contains an expected format
                   if (!it.period.contains('-')) {
                       logger.error("Unexpected period format: ${it.period}")
                       false
                   } else {
                       val endTime = LocalTime.parse(
                           it.period.substringAfter('-').trim(),
                           DateTimeFormatter.ofPattern("HH:mm")
                       )
                       it.date.atTime(endTime) > now
                   }
               } catch (e: Exception) {
                   logger.error("Failed to parse time from period: ${it.period}", e)
                   false  // Filter out tickets with unparseable times
               }
            }.map { ReservationTicketDto(it) }
            AjaxResult.success(tickets)
        } else {
            AjaxResult.error(ticketsResult.message)
        }
    }

    /**
     * 全文搜索医生
     */
    @GetMapping("searchDoctor")
    fun searchDoctor(
        @RequestParam keywords: String,
        @RequestParam(defaultValue = "10") limit: Int
    ): AjaxResult {
        if (keywords.length < 2) return AjaxResult.error("关键词长度至少两个字")

        val doctorList = doctorService.fulltextSearch(keywords, limit)
        return AjaxResult.success(doctorList)
    }
}