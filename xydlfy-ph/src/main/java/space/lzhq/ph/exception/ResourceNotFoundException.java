package space.lzhq.ph.exception;

import lombok.Getter;

/**
 * 资源未找到异常
 * 当请求的资源不存在时抛出此异常
 */
@Getter
public class ResourceNotFoundException extends RuntimeException {

    /**
     * 获取资源类型
     */
    private final String resourceType;

    /**
     * 资源ID
     */
    private final String resourceId;

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public ResourceNotFoundException(String message) {
        super(message);
        this.resourceType = null;
        this.resourceId = null;
    }

    /**
     * 构造函数
     *
     * @param message      异常消息
     * @param resourceType 资源类型
     * @param resourceId   资源ID
     */
    public ResourceNotFoundException(String message, String resourceType, String resourceId) {
        super(message);
        this.resourceType = resourceType;
        this.resourceId = resourceId;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public ResourceNotFoundException(String message, Throwable cause) {
        super(message, cause);
        this.resourceType = null;
        this.resourceId = null;
    }

}