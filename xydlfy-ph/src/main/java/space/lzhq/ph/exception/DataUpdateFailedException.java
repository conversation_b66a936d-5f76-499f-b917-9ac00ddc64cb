package space.lzhq.ph.exception;

import lombok.Getter;

/**
 * 数据更新失败异常
 * 当数据库更新操作失败时抛出此异常
 */
@Getter
public class DataUpdateFailedException extends RuntimeException {

    /**
     * 操作类型
     */
    private final String operationType;

    /**
     * 资源ID
     */
    private final String resourceId;

    /**
     * 构造函数
     *
     * @param message 异常消息
     */
    public DataUpdateFailedException(String message) {
        super(message);
        this.operationType = null;
        this.resourceId = null;
    }

    /**
     * 构造函数
     *
     * @param message       异常消息
     * @param operationType 操作类型
     */
    public DataUpdateFailedException(String message, String operationType) {
        super(message);
        this.operationType = operationType;
        this.resourceId = null;
    }

    /**
     * 构造函数
     *
     * @param message       异常消息
     * @param operationType 操作类型
     * @param resourceId    资源ID
     */
    public DataUpdateFailedException(String message, String operationType, String resourceId) {
        super(message);
        this.operationType = operationType;
        this.resourceId = resourceId;
    }

    /**
     * 构造函数
     *
     * @param message 异常消息
     * @param cause   异常原因
     */
    public DataUpdateFailedException(String message, Throwable cause) {
        super(message, cause);
        this.operationType = null;
        this.resourceId = null;
    }

    /**
     * 构造函数
     *
     * @param message       异常消息
     * @param operationType 操作类型
     * @param cause         异常原因
     */
    public DataUpdateFailedException(String message, String operationType, Throwable cause) {
        super(message, cause);
        this.operationType = operationType;
        this.resourceId = null;
    }

    /**
     * 构造函数
     *
     * @param message       异常消息
     * @param operationType 操作类型
     * @param resourceId    资源ID
     * @param cause         异常原因
     */
    public DataUpdateFailedException(String message, String operationType, String resourceId, Throwable cause) {
        super(message, cause);
        this.operationType = operationType;
        this.resourceId = resourceId;
    }
} 