package space.lzhq.ph.exception;

/**
 * 附件验证异常
 * 当附件验证失败时抛出此异常
 */
public class AttachmentValidationException extends RuntimeException {

    private final String attachmentId;
    private final String attachmentType;

    public AttachmentValidationException(String message) {
        super(message);
        this.attachmentId = null;
        this.attachmentType = null;
    }

    public AttachmentValidationException(String message, String attachmentId, String attachmentType) {
        super(message);
        this.attachmentId = attachmentId;
        this.attachmentType = attachmentType;
    }

    public AttachmentValidationException(String message, Throwable cause) {
        super(message, cause);
        this.attachmentId = null;
        this.attachmentType = null;
    }

    public AttachmentValidationException(String message, String attachmentId, String attachmentType, Throwable cause) {
        super(message, cause);
        this.attachmentId = attachmentId;
        this.attachmentType = attachmentType;
    }

    public String getAttachmentId() {
        return attachmentId;
    }

    public String getAttachmentType() {
        return attachmentType;
    }
} 