package space.lzhq.ph.webservice;

import jakarta.jws.WebMethod;
import jakarta.jws.WebParam;
import jakarta.jws.WebService;

@WebService(targetNamespace = "http://ws.access.hai/")
public interface BSXmlWsEntryClass {
    
    @WebMethod
    String invoke(
        @WebParam(name = "service") String service,
        @WebParam(name = "urid") String urid,
        @WebParam(name = "pwd") String pwd,
        @WebParam(name = "parameter") String parameter
    );
} 