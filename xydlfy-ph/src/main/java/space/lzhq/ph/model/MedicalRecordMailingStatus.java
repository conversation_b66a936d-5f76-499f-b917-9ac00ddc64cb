package space.lzhq.ph.model;

import com.baomidou.mybatisplus.annotation.IEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * 病历邮寄状态枚举类
 */
public enum MedicalRecordMailingStatus {

    APPLIED(1, "已申请"),
    CONFIRMED(2, "已确认病历信息"),
    PAID(3, "已支付"),
    MAILED(4, "已邮寄"),
    CLOSED(9, "关闭订单");
    private final int value;
    private final String description;

    private static final Map<Integer, MedicalRecordMailingStatus> BY_VALUE = new HashMap<>();

    static {
        for (MedicalRecordMailingStatus e: values()) {
            BY_VALUE.put(e.value, e);
        }
    }

    public static MedicalRecordMailingStatus fromValue(Integer value) {
        return BY_VALUE.get(value);
    }

    MedicalRecordMailingStatus(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

}
