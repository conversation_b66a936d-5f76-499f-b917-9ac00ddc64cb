package space.lzhq.ph.service;

import org.apache.ibatis.annotations.Param;
import space.lzhq.ph.domain.PscCarer;
import space.lzhq.ph.domain.PscOrder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单Service接口
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
public interface IPscOrderService {
    /**
     * 查询订单
     *
     * @param id 订单ID
     * @return 订单
     */
    PscOrder selectPscOrderById(Long id);

    /**
     * 查询订单列表
     *
     * @param pscOrder 订单
     * @return 订单集合
     */
    List<PscOrder> selectPscOrderList(PscOrder pscOrder);

    /**
     * 查询未完成订单
     *
     * @param patientId 病人ID
     * @return 订单集合
     */
    List<PscOrder> selectUnfinishedOrderList(String patientId);

    /**
     * 查询订单列表
     *
     * @param carerEmployeeNo   陪检工号
     * @param patientDepartment 病人科室
     * @param examItemCode      检查项目编码
     * @param beginCreateTime   开始创建时间
     * @param endCreateTime     结束创建时间
     * @return 订单列表
     */
    List<PscOrder> selectPscOrderListByCarer(
            String carerEmployeeNo,
            String patientDepartment,
            String examItemCode,
            LocalDateTime beginCreateTime,
            LocalDateTime endCreateTime
    );

    List<String> selectDepartments();

    /**
     * 新增订单
     *
     * @param pscOrder 订单
     * @return 结果
     */
    int insertPscOrder(PscOrder pscOrder);

    /**
     * 修改订单
     *
     * @param pscOrder 订单
     * @return 结果
     */
    int updatePscOrder(PscOrder pscOrder);

    /**
     * 批量删除订单
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deletePscOrderByIds(String ids);

    /**
     * 删除订单信息
     *
     * @param id 订单ID
     * @return 结果
     */
    int deletePscOrderById(Long id);

    void assignCarer(String ids, PscCarer pscCarer);

    void takeBatch(@Param("ids") String[] ids, @Param("carer") PscCarer carer);

    void release(@Param("ids") String[] ids, @Param("carerEmployeeNo") String carerEmployeeNo);

}
