package space.lzhq.ph.service;

import space.lzhq.ph.domain.Session;

import java.util.List;

/**
 * 会话Service接口
 *
 * <AUTHOR>
 * @date 2020-05-24
 */
public interface ISessionService {
    /**
     * 查询会话
     *
     * @param id 会话ID
     * @return 会话
     */
    Session selectSessionById(String id);

    /**
     * 根据openId查询会话
     *
     * @param openId openId
     * @return
     */
    Session selectSessionByOpenId(String openId);

    /**
     * 查询会话列表
     *
     * @param session 会话
     * @return 会话集合
     */
    List<Session> selectSessionList(Session session);

    /**
     * 新增会话
     *
     * @param session 会话
     * @return 结果
     */
    int insertSession(Session session);

    /**
     * 修改会话
     *
     * @param session 会话
     * @return 结果
     */
    int updateSession(Session session);

    /**
     * 批量删除会话
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteSessionByIds(String ids);

    /**
     * 删除会话信息
     *
     * @param id 会话ID
     * @return 结果
     */
    int deleteSessionById(String id);

    /**
     * 根据openId删除会话
     *
     * @param openId openId
     * @return 结果
     */
    int deleteSessionByOpenId(String openId);
}