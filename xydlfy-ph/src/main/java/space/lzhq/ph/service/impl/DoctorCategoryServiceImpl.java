package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.domain.Ztree;
import com.ruoyi.common.core.text.Convert;
import org.dromara.hutool.core.cache.Cache;
import org.dromara.hutool.core.cache.CacheUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import space.lzhq.ph.domain.Doctor;
import space.lzhq.ph.domain.DoctorCategory;
import space.lzhq.ph.mapper.DoctorCategoryMapper;
import space.lzhq.ph.mapper.DoctorMapper;
import space.lzhq.ph.service.IDoctorCategoryService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 医生分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-15
 */
@Service
public class DoctorCategoryServiceImpl implements IDoctorCategoryService {
    @Autowired
    private DoctorCategoryMapper doctorCategoryMapper;

    @Autowired
    private DoctorMapper doctorMapper;

    /**
     * 添加医生、医生分类关联关系缓存
     */
    private final Cache<String, Boolean> addRelationCache = CacheUtil.newTimedCache(1000 * 60 * 60 * 24);

    /**
     * 查询医生分类
     *
     * @param id 医生分类主键
     * @return 医生分类
     */
    @Override
    public DoctorCategory selectDoctorCategoryById(Long id) {
        return doctorCategoryMapper.selectDoctorCategoryById(id);
    }

    /**
     * 查询医生分类列表
     *
     * @param doctorCategory 医生分类
     * @return 医生分类
     */
    @Override
    public List<DoctorCategory> selectDoctorCategoryList(DoctorCategory doctorCategory) {
        return doctorCategoryMapper.selectDoctorCategoryList(doctorCategory);
    }

    /**
     * 新增医生分类
     *
     * @param doctorCategory 医生分类
     * @return 结果
     */
    @Override
    public int insertDoctorCategory(DoctorCategory doctorCategory) {
        return doctorCategoryMapper.insertDoctorCategory(doctorCategory);
    }

    /**
     * 修改医生分类
     *
     * @param doctorCategory 医生分类
     * @return 结果
     */
    @Override
    public int updateDoctorCategory(DoctorCategory doctorCategory) {
        return doctorCategoryMapper.updateDoctorCategory(doctorCategory);
    }

    /**
     * 批量删除医生分类
     *
     * @param ids 需要删除的医生分类主键
     * @return 结果
     */
    @Override
    public int deleteDoctorCategoryByIds(String ids) {
        return doctorCategoryMapper.deleteDoctorCategoryByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除医生分类信息
     *
     * @param id 医生分类主键
     * @return 结果
     */
    @Override
    public int deleteDoctorCategoryById(Long id) {
        return doctorCategoryMapper.deleteDoctorCategoryById(id);
    }


    @Transactional
    @Override
    public int associateDoctor(Long doctorCategoryId, String doctorId) {
        // 关联医生和分类
        int associateDoctorResult = doctorCategoryMapper.associateDoctor(doctorCategoryId, doctorId);

        // 查询当前医生的关联分类，并 join 成 string
        String doctorCategoryString = doctorCategoryMapper.selectByDoctorId(doctorId).stream()
                .map(DoctorCategory::getName)
                .sorted()
                .collect(Collectors.joining());

        // 查询出医生，更新关联分类信息
        Doctor doctor = doctorMapper.selectDoctorById(doctorId);
        doctor.setRelateCategory(doctorCategoryString);
        doctorMapper.updateDoctor(doctor);
        return associateDoctorResult;
    }

    @Override
    public void addRelation(String departmentCode, String doctorId) {
        // 0. 判断该科室、医生关系是否有添加过，在缓存中过滤判断
        String cacheKey = String.join(":", Arrays.asList(departmentCode, doctorId));
        if (addRelationCache.containsKey(cacheKey)) return;
        addRelationCache.put(cacheKey, true);

        // 1. 通过科室编码查询科室信息
        DoctorCategory example = new DoctorCategory();
        example.setCategoryCode(departmentCode);
        List<DoctorCategory> categoryList = selectDoctorCategoryList(example);
        if (categoryList.isEmpty()) return;

        // 2. 如果查询到了则使用 associateDoctor 方法添加关联
        categoryList.forEach(item -> associateDoctor(item.getId(), doctorId));
    }

    /**
     * 查询医生分类树列表
     *
     * @return 所有医生分类信息
     */
    @Override
    public List<Ztree> selectDoctorCategoryTree() {
        List<DoctorCategory> doctorCategoryList = doctorCategoryMapper.selectDoctorCategoryList(new DoctorCategory());
        List<Ztree> ztrees = new ArrayList<Ztree>();
        for (DoctorCategory doctorCategory : doctorCategoryList) {
            Ztree ztree = new Ztree();
            ztree.setId(doctorCategory.getId());
            ztree.setpId(doctorCategory.getParentId());
            ztree.setName(doctorCategory.getName());
            ztree.setTitle(doctorCategory.getName());
            ztrees.add(ztree);
        }
        return ztrees;
    }
}
