package space.lzhq.ph.service;

import org.apache.ibatis.annotations.Param;
import space.lzhq.ph.domain.Doctor;
import space.lzhq.ph.model.DoctorWrapper;

import java.util.List;

/**
 * 医生Service接口
 *
 * @date 2020-05-27
 */
public interface IDoctorService {
    /**
     * 查询医生
     *
     * @param id 医生ID
     * @return 医生
     */
    Doctor selectDoctorById(String id);

    /**
     * 查询医生列表
     *
     * @param doctor 医生
     * @return 医生集合
     */
    List<Doctor> selectDoctorList(DoctorWrapper doctor);

    /**
     * 按照医生分类查询医生列表
     *
     * @param doctorCategoryId 医生分类ID
     * @return 医生集合
     */
    List<Doctor> selectByDoctorCategoryId(Long doctorCategoryId);

    /**
     * 医生全文搜索
     */
    List<Doctor> fulltextSearch(@Param("keywords") String keywords, @Param("limit") Integer limit);

    /**
     * 新增医生
     *
     * @param doctor 医生
     * @return 结果
     */
    int insertDoctor(Doctor doctor);

    /**
     * 修改医生
     *
     * @param doctor 医生
     * @return 结果
     */
    int updateDoctor(Doctor doctor);

    /**
     * 批量删除医生
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteDoctorByIds(String ids);

    /**
     * 删除医生信息
     *
     * @param id 医生ID
     * @return 结果
     */
    int deleteDoctorById(String id);

}