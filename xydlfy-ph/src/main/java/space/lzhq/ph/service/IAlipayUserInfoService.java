package space.lzhq.ph.service;

import space.lzhq.ph.domain.AlipayUserInfo;
import space.lzhq.ph.domain.Session;
import space.lzhq.ph.domain.UserInfo;

import java.util.List;

public interface IAlipayUserInfoService {
    /**
     * 查询支付宝用户信息
     *
     * @param id 支付宝用户信息ID
     * @return 支付宝用户信息
     */
    AlipayUserInfo selectAlipayUserInfoById(String id);

    /**
     * 查询支付宝用户信息列表
     *
     * @param alipayUserInfo 支付宝用户信息
     * @return 支付宝用户信息集合
     */
    List<AlipayUserInfo> selectAlipayUserInfoList(AlipayUserInfo alipayUserInfo);

    /**
     * 新增支付宝用户信息
     *
     * @param alipayUserInfo 支付宝用户信息
     * @return 结果
     */
    int insertAlipayUserInfo(AlipayUserInfo alipayUserInfo);

    /**
     * 修改支付宝用户信息
     *
     * @param alipayUserInfo 支付宝用户信息
     * @return 结果
     */
    int updateAlipayUserInfo(AlipayUserInfo alipayUserInfo);

    /**
     * 批量删除支付宝用户信息
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteAlipayUserInfoByIds(String ids);

    /**
     * 删除支付宝用户信息信息
     *
     * @param id 支付宝用户信息ID
     * @return 结果
     */
    int deleteAlipayUserInfoById(String id);

    UserInfo fillUserInfo(Session session, UserInfo userInfo);

}
