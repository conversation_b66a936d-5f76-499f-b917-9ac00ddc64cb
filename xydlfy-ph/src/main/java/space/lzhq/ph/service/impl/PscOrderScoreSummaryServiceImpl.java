package space.lzhq.ph.service.impl;

import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.PscOrderScoreSummary;
import space.lzhq.ph.mapper.PscOrderScoreSummaryMapper;
import space.lzhq.ph.service.IPscOrderScoreSummaryService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PscOrderScoreSummaryServiceImpl implements IPscOrderScoreSummaryService {

    private final PscOrderScoreSummaryMapper mapper;

    public PscOrderScoreSummaryServiceImpl(PscOrderScoreSummaryMapper mapper) {
        this.mapper = mapper;
    }

    @Override
    public List<PscOrderScoreSummary> summary(LocalDateTime minConfirmTime, LocalDateTime maxConfirmTime) {
        return mapper.summary(minConfirmTime, maxConfirmTime);
    }

}
