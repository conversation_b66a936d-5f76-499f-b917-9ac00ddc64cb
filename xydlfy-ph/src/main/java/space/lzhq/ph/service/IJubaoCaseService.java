package space.lzhq.ph.service;

import space.lzhq.ph.domain.JubaoCase;

import java.util.List;

/**
 * 举报Service接口
 */
public interface IJubaoCaseService {
    /**
     * 查询举报
     *
     * @param id 举报主键
     * @return 举报
     */
    JubaoCase selectJubaoCaseById(Long id);

    /**
     * 查询举报列表
     *
     * @param jubaoCase 举报
     * @return 举报集合
     */
    List<JubaoCase> selectJubaoCaseList(JubaoCase jubaoCase);

    List<JubaoCase> selectListByOpenId(String openId);

    /**
     * 新增举报
     *
     * @param jubaoCase 举报
     * @return 结果
     */
    int insertJubaoCase(JubaoCase jubaoCase);

    /**
     * 修改举报
     *
     * @param jubaoCase 举报
     * @return 结果
     */
    int updateJubaoCase(JubaoCase jubaoCase);

    /**
     * 批量删除举报
     *
     * @param ids 需要删除的举报主键集合
     * @return 结果
     */
    int deleteJubaoCaseByIds(String ids);

    /**
     * 删除举报信息
     *
     * @param id 举报主键
     * @return 结果
     */
    int deleteJubaoCaseById(Long id);

    int acceptJubaoCaseByIds(String ids);

    int finishJubaoCaseByIds(String ids);
}
