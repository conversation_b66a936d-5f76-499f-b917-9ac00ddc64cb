package space.lzhq.ph.service.impl;

import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryResult;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.map.Dict;
import org.dromara.hutool.core.text.StrUtil;
import org.mospital.bsoft.MenzhenSettlement;
import org.mospital.bsoft.Recharge;
import org.mospital.bsoft.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.common.ServiceType;
import space.lzhq.ph.domain.*;
import space.lzhq.ph.mapper.WxPaymentMapper;
import space.lzhq.ph.service.IWxPaymentService;
import space.lzhq.ph.service.IWxRefundService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 充值记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-06-06
 */
@Service
public class WxPaymentServiceImpl implements IWxPaymentService {
    @Autowired
    private WxPaymentMapper wxPaymentMapper;

    @Autowired
    private IWxRefundService wxRefundService;

    /**
     * 查询充值记录
     *
     * @param id 充值记录ID
     * @return 充值记录
     */
    @Override
    public WxPayment selectWxPaymentById(Long id) {
        return wxPaymentMapper.selectWxPaymentById(id);
    }


    /**
     * 查找充值记录
     *
     * @param zyPayNo 掌医订单号
     */
    @Override
    public WxPayment selectWxPaymentByZyPayNo(String zyPayNo) {
        return wxPaymentMapper.selectWxPaymentByZyPayNo(zyPayNo);
    }

    @Override
    public WxPayment selectOneByWxPayNo(String wxPayNo) {
        return wxPaymentMapper.selectOneByWxPayNo(wxPayNo);
    }

    /**
     * 查询充值记录列表
     *
     * @param wxPayment 充值记录
     * @return 充值记录
     */
    @Override
    public List<WxPayment> selectWxPaymentList(WxPayment wxPayment) {
        return wxPaymentMapper.selectWxPaymentList(wxPayment);
    }

    /**
     * 查询可退款的充值订单
     *
     * @param jzCardNo      就诊卡号
     * @param minCreateTime 最小下单时间
     * @param maxCreateTime 最大下单时间
     */
    @Override
    public List<WxPayment> selectListForRefund(String jzCardNo, Date minCreateTime, Date maxCreateTime) {
        WxPayment wxPayment = new WxPayment();
        wxPayment.setJzCardNo(jzCardNo);
        wxPayment.setWxTradeStatus(Constants.PAY_OK);
        wxPayment.setHisTradeStatus(Constants.RECHARGE_OK);
        wxPayment.setParams(
                Dict.of()
                        .set("beginCreateTime", DateUtil.formatDateTime(minCreateTime))
                        .set("endCreateTime", DateUtil.formatDateTime(maxCreateTime))
        );
        return wxPaymentMapper.selectWxPaymentList(wxPayment);
    }

    @Override
    public Long sumAmount(WxPayment wxPayment) {
        return wxPaymentMapper.sumAmount(wxPayment);
    }

    /**
     * 新增充值记录
     *
     * @param wxPayment 充值记录
     * @return 结果
     */
    @Override
    public int insertWxPayment(WxPayment wxPayment) {
        wxPayment.setCreateTime(DateUtils.getNowDate());
        return wxPaymentMapper.insertWxPayment(wxPayment);
    }

    /**
     * 修改充值记录
     *
     * @param wxPayment 充值记录
     * @return 结果
     */
    @Override
    public int updateWxPayment(WxPayment wxPayment) {
        return wxPaymentMapper.updateWxPayment(wxPayment);
    }

    /**
     * 删除充值记录对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteWxPaymentByIds(String ids) {
        return wxPaymentMapper.deleteWxPaymentByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除充值记录信息
     *
     * @param id 充值记录ID
     * @return 结果
     */
    @Override
    public int deleteWxPaymentById(Long id) {
        return wxPaymentMapper.deleteWxPaymentById(id);
    }

    /**
     * 创建并保存
     *
     * @param serviceType 服务类型
     * @param patient     门诊患者
     * @param request     微信统一下单
     */
    @Override
    public boolean createAndSave(ServiceType serviceType, Patient patient, WxPayUnifiedOrderRequest request) {
        return createAndSave(serviceType, patient, request, "");
    }

    @Override
    public boolean createAndSave(ServiceType serviceType, Patient patient, WxPayUnifiedOrderRequest request, String settlementIds) {
        WxPayment payment = new WxPayment();
        payment.setCreateTime(new Date());
        payment.setType(serviceType.name());
        payment.setMchId(request.getMchId());
        payment.setOpenid(patient.getOpenId());
        payment.setJzCardNo(patient.getJzCardNo());
        payment.setZhuyuanNo(patient.getZhuyuanNo());
        payment.setZyPayNo(request.getOutTradeNo());
        payment.setAmount(request.getTotalFee().longValue());
        payment.setPatientNo(patient.getPatientNo());
        payment.setSettlementIds(settlementIds);

        // 已退金额，初始为0
        payment.setExrefund(0L);
        // 未退（可退）金额，初始为null
        payment.setUnrefund(null);

        payment.setWxPayNo("");
        payment.setWxTradeStatus("");
        payment.setHisTradeStatus("");
        payment.setManualPayState(Constants.MANUAL_INIT);
        payment.setRemark("");
        return insertWxPayment(payment) == 1;
    }

    @Override
    public boolean createAndSave(MedicalRecordApplication application, WxPayUnifiedOrderRequest request) {
        WxPayment payment = new WxPayment();
        payment.setCreateTime(new Date());
        payment.setType(ServiceType.BL.name());
        payment.setMchId(request.getMchId());
        payment.setOpenid(application.getOpenId());
        payment.setJzCardNo("");
        payment.setZhuyuanNo(application.getAdmissionId());
        payment.setZyPayNo(request.getOutTradeNo());
        payment.setAmount(request.getTotalFee().longValue());
        payment.setPatientNo("");
        payment.setSettlementIds("");

        // 已退金额，初始为0
        payment.setExrefund(0L);
        // 未退（可退）金额，初始为null
        payment.setUnrefund(null);

        payment.setWxPayNo("");
        payment.setWxTradeStatus("");
        payment.setHisTradeStatus("");
        payment.setManualPayState(Constants.MANUAL_INIT);
        payment.setRemark("");
        return insertWxPayment(payment) == 1;
    }

    /**
     * 创建并保存
     *
     * @param serviceType 服务类型
     * @param patient     门诊患者
     * @param request     微信统一下单
     */
    @Override
    public boolean createAndSave(ServiceType serviceType, ZhuyuanPatient patient, WxPayUnifiedOrderRequest request) {
        WxPayment payment = new WxPayment();
        payment.setCreateTime(new Date());
        payment.setType(serviceType.name());
        payment.setMchId(request.getMchId());
        payment.setOpenid(patient.getOpenId());
        payment.setJzCardNo(patient.getAdmissionNumber());
        payment.setZhuyuanNo(patient.getZhuyuanNo());
        payment.setZyPayNo(request.getOutTradeNo());
        payment.setAmount(request.getTotalFee().longValue());
        payment.setPatientNo(patient.getPatientNo());

        // 已退金额，初始为0
        payment.setExrefund(0L);
        // 未退（可退）金额，初始为null
        payment.setUnrefund(null);

        payment.setWxPayNo("");
        payment.setWxTradeStatus("");
        payment.setHisTradeStatus("");
        payment.setManualPayState(Constants.MANUAL_INIT);
        payment.setRemark("");
        return insertWxPayment(payment) == 1;
    }

    /**
     * 接收到支付通知时，更新充值记录
     *
     * @param wxPayment             充值记录
     * @param wxPayOrderQueryResult 支付通知
     */
    @Override
    public boolean updateOnPay(WxPayment wxPayment, WxPayOrderQueryResult wxPayOrderQueryResult) {
        wxPayment.setWxPayNo(wxPayOrderQueryResult.getTransactionId());

        wxPayment.setWxTradeStatus(wxPayOrderQueryResult.getTradeStateDesc());

        // 可退金额为null，则更新为订单金额
        if (wxPayment.getUnrefund() == null && StrUtil.isNotBlank(wxPayOrderQueryResult.getTransactionId())) {
            wxPayment.setUnrefund(wxPayment.getAmount());
        }

        return updateWxPayment(wxPayment) == 1;
    }

    /**
     * HIS充值后，更新充值记录
     *
     * @param wxPayment        充值记录
     */
    @Override
    public boolean updateOnRecharge(WxPayment wxPayment, Result<Recharge> rechargeResult) {
        if (wxPayment.getManualPayState() == Constants.MANUAL_REQUESTED) {
            wxPayment.setManualPayState(Constants.MANUAL_APPROVED);
        }

        if (rechargeResult.isOk()) {
            wxPayment.setHisTradeStatus(Constants.RECHARGE_OK);
            Recharge recharge = rechargeResult.getData();
            wxPayment.setHisReceiptNo(recharge.getHisNo());
            wxPayment.setBalance(recharge.getCardBalance());
            wxPayment.setBusinessSerialNumber(recharge.getBusinessSerialNumber());
        } else {
            wxPayment.setHisTradeStatus(rechargeResult.getCode() + ":" + rechargeResult.getMessage());
        }

        return updateWxPayment(wxPayment) == 1;
    }

    @Override
    public boolean updateOnSettlement(WxPayment wxPayment, Result<MenzhenSettlement> settlementResult) {
        if (wxPayment.getManualPayState() == Constants.MANUAL_REQUESTED) {
            wxPayment.setManualPayState(Constants.MANUAL_APPROVED);
        }

        if (settlementResult.isOk()) {
            wxPayment.setHisTradeStatus(Constants.RECHARGE_OK);
            MenzhenSettlement settlement = settlementResult.getData();
            if (settlement != null) {
                wxPayment.setHisReceiptNo(settlement.getOrderNo());
                wxPayment.setBalance(new BigDecimal(settlement.getCardBalance()));
            } else {
                wxPayment.setHisReceiptNo("");
                wxPayment.setBalance(null);
            }
        } else {
            wxPayment.setHisTradeStatus(settlementResult.getCode() + ":" + settlementResult.getMessage());
        }

        return updateWxPayment(wxPayment) == 1;
    }

    /**
     * HIS退款后，更新充值记录
     *
     * @param wxPayment    充值记录
     * @param refundAmount 退款金额，以分为单位
     */
    @Override
    public boolean updateOnRefund(WxPayment wxPayment, Long refundAmount) {
        wxPayment.setExrefund(wxPayment.getExrefund() + refundAmount);
        wxPayment.setUnrefund(wxPayment.getAmount() - wxPayment.getExrefund());
        return updateWxPayment(wxPayment) == 1;
    }

    @Override
    public Long calculateNetInAmount(LocalDate date) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startTime = date.atStartOfDay().format(dateTimeFormatter);
        String endTime = date.atTime(LocalTime.MAX).format(dateTimeFormatter);

        Map<String, Object> params = new HashMap<>();
        params.put("beginCreateTime", startTime);
        params.put("endCreateTime", endTime);

        WxPayment wxPayment = new WxPayment();
        wxPayment.setParams(params);
        Long payAmount = sumAmount(wxPayment);
        payAmount = payAmount == null ? 0 : payAmount;

        WxRefund wxRefund = new WxRefund();
        wxRefund.setParams(params);
        Long refundAmount = wxRefundService.sumAmount(wxRefund);
        refundAmount = refundAmount == null ? 0 : refundAmount;

        return payAmount - refundAmount;
    }

}
