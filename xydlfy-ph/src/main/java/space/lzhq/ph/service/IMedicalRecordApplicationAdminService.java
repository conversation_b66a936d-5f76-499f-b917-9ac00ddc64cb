package space.lzhq.ph.service;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import space.lzhq.ph.domain.MedicalRecordApplication;
import space.lzhq.ph.dto.request.MedicalRecordApplicationApprovalDto;
import space.lzhq.ph.dto.request.MedicalRecordApplicationSearchForm;
import space.lzhq.ph.dto.request.MedicalRecordApplicationShippingDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationAdminListResponseDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationDetailResponseDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationStatisticsDto;

import java.util.List;

/**
 * 病历复印申请后台管理服务接口
 */
public interface IMedicalRecordApplicationAdminService {

    /**
     * 搜索病历复印申请记录（管理端）
     *
     * @param searchForm 搜索条件
     * @return 申请记录列表
     */
    List<MedicalRecordApplicationAdminListResponseDto> searchApplications(@NotNull MedicalRecordApplicationSearchForm searchForm);

    /**
     * 导出病历复印申请记录
     *
     * @param searchForm 搜索条件
     * @return 申请记录列表（用于导出）
     */
    List<MedicalRecordApplication> exportApplications(@NotNull MedicalRecordApplicationSearchForm searchForm);

    /**
     * 获取病历复印申请详情（管理端）
     *
     * @param id 申请记录ID
     * @return 申请记录详情
     * @throws space.lzhq.ph.exception.ResourceNotFoundException 当申请记录不存在时
     */
    MedicalRecordApplicationDetailResponseDto getAdminDetailById(@NotBlank @Size(min = 32, max = 32) String id);

    /**
     * 审批病历复印申请记录
     *
     * @param id           申请记录ID
     * @param approvalDto  审批信息（包含费用信息）
     * @param approverName 审批人姓名
     * @throws space.lzhq.ph.exception.ResourceNotFoundException 当申请记录不存在时
     * @throws space.lzhq.ph.exception.EditNotAllowedException   当申请状态不允许审批时
     * @throws space.lzhq.ph.exception.DataUpdateFailedException 当数据更新失败时
     */
    void approveApplication(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid MedicalRecordApplicationApprovalDto approvalDto,
            @NotBlank String approverName
    );

    /**
     * 邮寄病历复印申请记录
     *
     * @param id           申请记录ID
     * @param shippingDto  邮寄信息（包含快递单号）
     * @param operatorName 操作人姓名
     * @throws space.lzhq.ph.exception.ResourceNotFoundException 当申请记录不存在时
     * @throws space.lzhq.ph.exception.EditNotAllowedException   当申请状态不允许邮寄时
     * @throws space.lzhq.ph.exception.DataUpdateFailedException 当数据更新失败时
     */
    void shipApplication(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid MedicalRecordApplicationShippingDto shippingDto,
            @NotBlank String operatorName
    );

    /**
     * 删除病历复印申请记录
     *
     * @param id           申请记录ID
     * @param operatorName 操作人姓名
     * @return 是否删除成功
     * @throws space.lzhq.ph.exception.ResourceNotFoundException 当申请记录不存在时
     * @throws space.lzhq.ph.exception.EditNotAllowedException   当申请状态不允许删除时
     */
    boolean deleteApplication(@NotBlank @Size(min = 32, max = 32) String id, @NotBlank String operatorName);

    /**
     * 获取病历复印申请统计信息
     *
     * @return 统计信息
     */
    MedicalRecordApplicationStatisticsDto getStatistics();

} 