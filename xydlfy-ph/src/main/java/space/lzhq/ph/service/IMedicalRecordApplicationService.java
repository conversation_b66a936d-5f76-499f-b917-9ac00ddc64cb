package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import space.lzhq.ph.domain.MedicalRecordApplication;
import space.lzhq.ph.dto.request.MedicalRecordApplicationAgentUpdateDto;
import space.lzhq.ph.dto.request.MedicalRecordApplicationPatientFormDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationDetailResponseDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationListResponseDto;
import space.lzhq.ph.enums.MedicalRecordApplicationStatus;
import space.lzhq.ph.exception.AttachmentValidationException;
import space.lzhq.ph.exception.DataUpdateFailedException;

import java.util.List;
import java.util.Optional;

public interface IMedicalRecordApplicationService extends IService<MedicalRecordApplication> {

    /**
     * 根据ID获取病历复印申请详情
     * <p>
     * 此方法用于获取指定ID的病历复印申请的详细信息。执行以下操作：
     * <ul>
     *   <li>验证申报记录是否存在</li>
     *   <li>验证申报记录是否属于当前用户</li>
     *   <li>返回包含完整详情的响应DTO</li>
     * </ul>
     *
     * @param id     申报记录ID，不能为空
     * @param openId 用户OpenID，用于验证权限，不能为空
     * @return 申报记录详情；若记录不存在或无权限访问直接抛出相应异常
     */
    MedicalRecordApplicationDetailResponseDto getDetailById(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

    MedicalRecordApplication getByZyPayNo(
            @NotBlank @Size(min = 20, max = 20) String zyPayNo
    );

    boolean updatePaymentInfo(@NotBlank @Size(min = 32, max = 32) String id, @NotBlank @Size(min = 28, max = 28) String wxPayNo);

    /**
     * 保存病历复印申请记录
     * <p>
     * 此方法用于创建新的病历复印申请记录。执行以下操作：
     * <ul>
     *   <li>验证患者身份证附件的有效性</li>
     *   <li>将DTO映射为实体对象</li>
     *   <li>设置申报记录的基本信息（用户ID、状态、时间等）</li>
     *   <li>保存申报记录到数据库</li>
     *   <li>更新相关附件的申报记录关联</li>
     * </ul>
     *
     * @param dto    患者申报表单DTO，包含患者基本信息和附件ID，不能为空且必须通过验证
     * @param openId 用户OpenID，用于标识申报用户，长度必须在16-28个字符之间
     * @return 包含保存后申报记录的Optional对象，如果保存失败则抛出异常
     * @throws AttachmentValidationException 当附件验证失败时抛出异常
     * @throws DataUpdateFailedException     当数据库保存操作失败时抛出异常
     * @throws RuntimeException              当其他数据库操作失败时抛出异常
     */
    Optional<MedicalRecordApplication> save(
            @NotNull @Valid MedicalRecordApplicationPatientFormDto dto,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

    /**
     * 清除病历复印申请记录的代办标志
     *
     * @param id     申报记录ID
     * @param openId 用户OpenID
     * @return 更新后的申报记录
     * @throws space.lzhq.ph.exception.ResourceNotFoundException           当申报记录不存在时
     * @throws space.lzhq.ph.exception.UnauthorizedResourceAccessException 当申报记录不属于当前用户时
     * @throws space.lzhq.ph.exception.EditNotAllowedException             当申报记录状态不允许编辑时
     */
    MedicalRecordApplication clearAgentFlag(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

    /**
     * 更新病历复印申请记录的代办人信息
     * <p>
     * 此方法用于更新现有病历复印申请记录的代办人信息。执行以下操作：
     * <ul>
     *   <li>验证申报记录是否存在</li>
     *   <li>验证申报记录是否属于当前用户</li>
     *   <li>验证申报记录状态是否允许编辑</li>
     *   <li>验证代办人身份证附件的有效性</li>
     *   <li>更新代办人相关信息</li>
     *   <li>设置代办标志为1</li>
     * </ul>
     *
     * @param id       申报记录ID，不能为空
     * @param agentDto 代办人更新信息DTO，包含代办人的所有必要信息，不能为空且必须通过验证
     * @param openId   用户OpenID，用于验证权限，长度必须在16-28字符之间
     * @return 更新后的申报记录实体
     * @throws space.lzhq.ph.exception.ResourceNotFoundException           当申报记录不存在时
     * @throws space.lzhq.ph.exception.UnauthorizedResourceAccessException 当申报记录不属于当前用户时
     * @throws space.lzhq.ph.exception.EditNotAllowedException             当申报记录状态不允许编辑时
     * @throws space.lzhq.ph.exception.AttachmentValidationException       当代办人身份证附件验证失败时
     * @throws RuntimeException                                            当数据库操作失败时
     * @since 1.0.0
     */
    MedicalRecordApplication updateAgent(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid MedicalRecordApplicationAgentUpdateDto agentDto,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

    /**
     * 更新病历复印申请记录的邮寄信息
     * <p>
     * 此方法用于更新现有病历复印申请记录的邮寄信息。执行以下操作：
     * <ul>
     *   <li>验证申报记录是否存在</li>
     *   <li>验证申报记录是否属于当前用户</li>
     *   <li>验证申报记录状态是否允许编辑</li>
     *   <li>更新收件人相关信息</li>
     * </ul>
     *
     * @param id         申报记录ID，不能为空
     * @param mailingDto 邮寄信息更新DTO，包含收件人的所有必要信息，不能为空且必须通过验证
     * @param openId     用户OpenID，用于验证权限，长度必须在16-28字符之间
     * @return 更新后的申报记录实体
     * @throws space.lzhq.ph.exception.ResourceNotFoundException           当申报记录不存在时
     * @throws space.lzhq.ph.exception.UnauthorizedResourceAccessException 当申报记录不属于当前用户时
     * @throws space.lzhq.ph.exception.EditNotAllowedException             当申报记录状态不允许编辑时
     * @throws RuntimeException                                            当数据库操作失败时
     * @since 1.0.0
     */
    MedicalRecordApplication updateMailingInfo(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotNull @Valid space.lzhq.ph.dto.request.MedicalRecordApplicationMailingUpdateDto mailingDto,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

    /**
     * 提交病历复印申请记录
     * <p>
     * 此方法用于提交现有病历复印申请记录进行审核。执行以下操作：
     * <ul>
     *   <li>验证申报记录是否存在</li>
     *   <li>验证申报记录是否属于当前用户</li>
     *   <li>验证申报记录状态是否允许提交（必须是DRAFT状态）</li>
     *   <li>验证申请信息完整性（患者信息、邮寄信息等）</li>
     *   <li>更新申请状态为SUBMITTED</li>
     *   <li>设置提交时间</li>
     * </ul>
     *
     * @param id     申报记录ID，不能为空
     * @param openId 用户OpenID，用于验证权限，长度必须在16-28字符之间
     * @return 更新后的申报记录实体
     * @throws space.lzhq.ph.exception.ResourceNotFoundException           当申报记录不存在时
     * @throws space.lzhq.ph.exception.UnauthorizedResourceAccessException 当申报记录不属于当前用户时
     * @throws space.lzhq.ph.exception.EditNotAllowedException             当申报记录状态不允许提交时
     * @throws IllegalArgumentException                                    当申请信息不完整时
     * @throws RuntimeException                                            当数据库操作失败时
     * @since 1.0.0
     */
    MedicalRecordApplication submitApplication(
            @NotBlank @Size(min = 32, max = 32) String id,
            @NotBlank @Size(min = 16, max = 28) String openId
    );

    /**
     * 获取用户的病历复印申请列表
     * <p>
     * 此方法用于获取指定用户的病历复印申请列表。执行以下操作：
     * <ul>
     *   <li>根据用户OpenID查询申请记录</li>
     *   <li>可选择按状态过滤</li>
     *   <li>返回申请记录列表响应DTO</li>
     * </ul>
     *
     * @param openId 用户OpenID，用于查询用户的申请记录，不能为空
     * @param status 申请状态，可选的过滤条件，为null时返回所有状态的记录
     * @return 用户的申请记录列表
     * @since 1.0.0
     */
    List<MedicalRecordApplicationListResponseDto> getUserApplicationList(
            @NotBlank @Size(min = 16, max = 28) String openId,
            MedicalRecordApplicationStatus status
    );
}
