package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import space.lzhq.ph.domain.MedicalRecordApplicationTimeline;
import space.lzhq.ph.dto.response.MedicalRecordApplicationTimelineDto;
import space.lzhq.ph.enums.MedicalRecordApplicationStatus;
import space.lzhq.ph.enums.MedicalRecordApplicationEventType;
import space.lzhq.ph.enums.TimelineOperatorType;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 病历复印申请时间线服务接口
 *
 * <AUTHOR>
 */
public interface IMedicalRecordApplicationTimelineService extends IService<MedicalRecordApplicationTimeline> {

    /**
     * 获取申请记录的时间线
     *
     * @param applicationId  申请记录ID
     * @param includeDetails 是否包含详细操作，false时只返回重要事件
     * @return 时间线事件列表
     */
    List<MedicalRecordApplicationTimelineDto> getApplicationTimeline(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            Boolean includeDetails
    );

    /**
     * 记录时间线事件（基础版本）
     *
     * @param applicationId    申请记录ID
     * @param eventType        事件类型
     * @param eventDescription 事件描述
     * @param operatorType     操作者类型
     * @param operatorName     操作者姓名
     */
    void recordTimelineEvent(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotNull MedicalRecordApplicationEventType eventType,
            @NotBlank String eventDescription,
            @NotNull TimelineOperatorType operatorType,
            @NotBlank String operatorName
    );

    /**
     * 记录状态变更时间线事件
     *
     * @param applicationId    申请记录ID
     * @param eventType        事件类型
     * @param eventDescription 事件描述
     * @param operatorType     操作者类型
     * @param operatorName     操作者姓名
     * @param oldStatus        变更前状态
     * @param newStatus        变更后状态
     */
    void recordStatusChangeEvent(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotNull MedicalRecordApplicationEventType eventType,
            @NotBlank String eventDescription,
            @NotNull TimelineOperatorType operatorType,
            @NotBlank String operatorName,
            MedicalRecordApplicationStatus oldStatus,
            @NotNull MedicalRecordApplicationStatus newStatus
    );

    /**
     * 记录包含额外数据的时间线事件
     *
     * @param applicationId    申请记录ID
     * @param eventType        事件类型
     * @param eventDescription 事件描述
     * @param operatorType     操作者类型
     * @param operatorName     操作者姓名
     * @param additionalData   额外数据
     */
    void recordTimelineEventWithData(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotNull MedicalRecordApplicationEventType eventType,
            @NotBlank String eventDescription,
            @NotNull TimelineOperatorType operatorType,
            @NotBlank String operatorName,
            Map<String, Serializable> additionalData
    );

    /**
     * 记录申请记录创建事件
     *
     * @param applicationId 申请记录ID
     * @param operatorName  操作者姓名
     */
    void recordApplicationCreated(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName
    );

    /**
     * 记录申请记录提交事件
     *
     * @param applicationId 申请记录ID
     * @param operatorName  操作者姓名
     * @param oldStatus     变更前状态
     */
    void recordApplicationSubmitted(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName,
            MedicalRecordApplicationStatus oldStatus
    );

    /**
     * 记录申请记录审核通过事件
     *
     * @param applicationId 申请记录ID
     * @param operatorName  操作者姓名
     * @param oldStatus     变更前状态
     */
    void recordApplicationApproved(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName,
            MedicalRecordApplicationStatus oldStatus
    );

    /**
     * 记录申请记录驳回事件
     *
     * @param applicationId 申请记录ID
     * @param operatorName  操作者姓名
     * @param oldStatus     变更前状态
     * @param rejectReason  驳回原因
     */
    void recordApplicationRejected(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName,
            MedicalRecordApplicationStatus oldStatus,
            @NotBlank String rejectReason
    );

    /**
     * 记录申请完成事件
     *
     * @param applicationId 申请记录ID
     * @param operatorName  操作者姓名
     * @param oldStatus     变更前状态
     */
    void recordApplicationCompleted(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName,
            MedicalRecordApplicationStatus oldStatus
    );

    /**
     * 记录附件上传事件
     *
     * @param applicationId 申请记录ID
     * @param operatorName  操作者姓名
     * @param attachmentType 附件类型
     */
    void recordAttachmentUploaded(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName,
            @NotBlank String attachmentType
    );

    /**
     * 记录邮寄信息设置事件
     *
     * @param applicationId  申请记录ID
     * @param operatorName   操作者姓名
     * @param mailingAddress 邮寄地址
     */
    void recordMailingInfoSet(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName,
            @NotBlank String mailingAddress
    );

    /**
     * 记录代办人信息清除事件
     *
     * @param applicationId  申请记录ID
     * @param operatorName   操作者姓名
     */
    void recordAgentInfoCleared(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName
    );

    /**
     * 记录代办人信息设置事件
     *
     * @param applicationId 申请记录ID
     * @param operatorName   操作者姓名
     * @param agentName      代办人姓名
     */
    void recordAgentInfoSet(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName,
            @NotBlank String agentName
    );

    /**
     * 记录申请记录已支付事件
     *
     * @param applicationId 申请记录ID
     * @param operatorName  操作者姓名
     * @param oldStatus     变更前状态
     */
    void recordApplicationPaid(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName,
            MedicalRecordApplicationStatus oldStatus
    );

    /**
     * 记录申请记录已邮寄事件
     *
     * @param applicationId  申请记录ID
     * @param operatorName   操作者姓名
     * @param oldStatus      变更前状态
     * @param trackingNumber 快递单号
     */
    void recordApplicationDelivered(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName,
            MedicalRecordApplicationStatus oldStatus,
            @NotBlank String trackingNumber
    );

    /**
     * 记录申请记录删除事件
     *
     * @param applicationId 申请记录ID
     * @param operatorName  操作者姓名
     * @param oldStatus     删除前状态
     */
    void recordApplicationDeleted(
            @NotBlank @Size(min = 32, max = 32) String applicationId,
            @NotBlank String operatorName,
            MedicalRecordApplicationStatus oldStatus
    );

} 