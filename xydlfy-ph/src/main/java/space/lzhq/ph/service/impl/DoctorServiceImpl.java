package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.PinyinUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Doctor;
import space.lzhq.ph.mapper.DoctorMapper;
import space.lzhq.ph.model.DoctorWrapper;
import space.lzhq.ph.service.IDoctorService;

import java.util.List;

/**
 * 医生Service业务层处理
 *
 * @date 2020-05-27
 */
@Service
public class DoctorServiceImpl implements IDoctorService {

    private static final Logger log = LoggerFactory.getLogger(DoctorServiceImpl.class);


    @Autowired
    private DoctorMapper doctorMapper;

    /**
     * 查询医生
     *
     * @param id 医生ID
     * @return 医生
     */
    @Override
    public Doctor selectDoctorById(String id) {
        return doctorMapper.selectDoctorById(id);
    }

    /**
     * 查询医生列表
     *
     * @param doctor 医生
     * @return 医生
     */
    @Override
    public List<Doctor> selectDoctorList(Doctor<PERSON>rap<PERSON> doctor) {
        return doctorMapper.selectDoctorList(doctor);
    }

    @Override
    public List<Doctor> selectByDoctorCategoryId(Long doctorCategoryId) {
        return doctorMapper.selectByDoctorCategoryId(doctorCategoryId);
    }

    @Override
    public List<Doctor> fulltextSearch(String keywords, Integer limit) {
        return doctorMapper.fulltextSearch(keywords, limit);
    }

    /**
     * 新增医生
     *
     * @param doctor 医生
     * @return 结果
     */
    @Override
    public int insertDoctor(Doctor doctor) {
        doctor.setSimplePinyin(PinyinUtils.getSimplePinyin(doctor.getName(), "", true));
        doctor.setFullPinyin(PinyinUtils.getFullPinyin(doctor.getName(), "", true));
        return doctorMapper.insertDoctor(doctor);
    }

    /**
     * 修改医生
     *
     * @param doctor 医生
     * @return 结果
     */
    @Override
    public int updateDoctor(Doctor doctor) {
        doctor.setSimplePinyin(PinyinUtils.getSimplePinyin(doctor.getName(), "", true));
        doctor.setFullPinyin(PinyinUtils.getFullPinyin(doctor.getName(), "", true));
        return doctorMapper.updateDoctor(doctor);
    }

    /**
     * 删除医生对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteDoctorByIds(String ids) {
        return doctorMapper.deleteDoctorByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除医生信息
     *
     * @param id 医生ID
     * @return 结果
     */
    @Override
    public int deleteDoctorById(String id) {
        return doctorMapper.deleteDoctorById(id);
    }

}