package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.ZhuyuanPatient;
import space.lzhq.ph.mapper.ZhuyuanPatientMapper;
import space.lzhq.ph.service.IZhuyuanPatientService;

import java.util.List;

/**
 * 住院患者Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
@Service
public class ZhuyuanPatientServiceImpl implements IZhuyuanPatientService {
    @Autowired
    private ZhuyuanPatientMapper zhuyuanPatientMapper;

    /**
     * 查询住院患者
     *
     * @param id 住院患者主键
     * @return 住院患者
     */
    @Override
    public ZhuyuanPatient selectZhuyuanPatientById(Long id) {
        return zhuyuanPatientMapper.selectZhuyuanPatientById(id);
    }

    /**
     * 查询住院患者列表
     *
     * @param zhuyuanPatient 住院患者
     * @return 住院患者
     */
    @Override
    public List<ZhuyuanPatient> selectZhuyuanPatientList(ZhuyuanPatient zhuyuanPatient) {
        return zhuyuanPatientMapper.selectZhuyuanPatientList(zhuyuanPatient);
    }

    /**
     * 新增住院患者
     *
     * @param zhuyuanPatient 住院患者
     * @return 结果
     */
    @Override
    public int insertZhuyuanPatient(ZhuyuanPatient zhuyuanPatient) {
        return zhuyuanPatientMapper.insertZhuyuanPatient(zhuyuanPatient);
    }

    /**
     * 修改住院患者
     *
     * @param zhuyuanPatient 住院患者
     * @return 结果
     */
    @Override
    public int updateZhuyuanPatient(ZhuyuanPatient zhuyuanPatient) {
        return zhuyuanPatientMapper.updateZhuyuanPatient(zhuyuanPatient);
    }

    /**
     * 批量删除住院患者
     *
     * @param ids 需要删除的住院患者主键
     * @return 结果
     */
    @Override
    public int deleteZhuyuanPatientByIds(String ids) {
        return zhuyuanPatientMapper.deleteZhuyuanPatientByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除住院患者信息
     *
     * @param id 住院患者主键
     * @return 结果
     */
    @Override
    public int deleteZhuyuanPatientById(Long id) {
        return zhuyuanPatientMapper.deleteZhuyuanPatientById(id);
    }

    @Override
    public ZhuyuanPatient selectActivePatientByOpenId(String openId) {
        return zhuyuanPatientMapper.selectZhuyuanPatientByOpenId(openId);
    }
}
