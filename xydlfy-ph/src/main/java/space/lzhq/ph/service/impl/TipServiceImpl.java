package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Tip;
import space.lzhq.ph.mapper.TipMapper;
import space.lzhq.ph.service.ITipService;

import java.util.List;

/**
 * 提示信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
@Service
public class TipServiceImpl implements ITipService {
    @Autowired
    private TipMapper tipMapper;

    /**
     * 查询提示信息
     *
     * @param id 提示信息主键
     * @return 提示信息
     */
    @Override
    public Tip selectTipById(Long id) {
        return tipMapper.selectTipById(id);
    }

    /**
     * 查询提示信息列表
     *
     * @param tip 提示信息
     * @return 提示信息
     */
    @Override
    public List<Tip> selectTipList(Tip tip) {
        return tipMapper.selectTipList(tip);
    }

    public List<Tip> selectByCodeList(List<String> codeList) {
        return tipMapper.selectByCodeList(codeList);
    }

    /**
     * 新增提示信息
     *
     * @param tip 提示信息
     * @return 结果
     */
    @Override
    public int insertTip(Tip tip) {
        return tipMapper.insertTip(tip);
    }

    /**
     * 修改提示信息
     *
     * @param tip 提示信息
     * @return 结果
     */
    @Override
    public int updateTip(Tip tip) {
        return tipMapper.updateTip(tip);
    }

    /**
     * 批量删除提示信息
     *
     * @param ids 需要删除的提示信息主键
     * @return 结果
     */
    @Override
    public int deleteTipByIds(String ids) {
        return tipMapper.deleteTipByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除提示信息信息
     *
     * @param id 提示信息主键
     * @return 结果
     */
    @Override
    public int deleteTipById(Long id) {
        return tipMapper.deleteTipById(id);
    }
}
