package space.lzhq.ph.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import space.lzhq.ph.domain.OutpatientStopClinic;

import java.util.List;

/**
 * 门诊停诊消息服务
 */
public interface OutpatientStopClinicService extends IService<OutpatientStopClinic> {

    /**
     * 分页获取患者的停诊消息
     *
     * @param patientId 患者ID
     * @param page      页码
     * @param size      每页大小
     * @return 分页数据
     */
    IPage<OutpatientStopClinic> getPageByPatientId(String patientId, long page, long size);

    List<OutpatientStopClinic> getListByPatientId(String patientId);

    OutpatientStopClinic getOneByAppointmentId(String appointmentId);

    boolean existsByAppointmentId(String appointmentId);

    /**
     * 标记消息为已读
     *
     * @param id 消息ID
     * @return 是否成功
     */
    boolean markAsRead(Long id);

    void sendSubscribeMessage(OutpatientStopClinic outpatientStopClinic);

} 