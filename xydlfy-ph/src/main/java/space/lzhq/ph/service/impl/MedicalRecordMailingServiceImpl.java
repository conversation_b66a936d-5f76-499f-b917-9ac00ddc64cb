package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.MedicalRecordMailing;
import space.lzhq.ph.mapper.MedicalRecordMailingMapper;
import space.lzhq.ph.service.IMedicalRecordMailingService;

import java.util.List;

/**
 * 病历邮寄Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-11
 */
@Service
public class MedicalRecordMailingServiceImpl extends ServiceImpl<MedicalRecordMailingMapper, MedicalRecordMailing> implements IMedicalRecordMailingService {
    @Autowired
    private MedicalRecordMailingMapper medicalRecordMailingMapper;

    /**
     * 查询病历邮寄
     *
     * @param id 病历邮寄主键
     * @return 病历邮寄
     */
    @Override
    public MedicalRecordMailing selectMedicalRecordMailingById(Long id) {
        return medicalRecordMailingMapper.selectMedicalRecordMailingById(id);
    }

    /**
     * 查询病历邮寄列表
     *
     * @param medicalRecordMailing 病历邮寄
     * @return 病历邮寄
     */
    @Override
    public List<MedicalRecordMailing> selectMedicalRecordMailingList(MedicalRecordMailing medicalRecordMailing) {
        return medicalRecordMailingMapper.selectMedicalRecordMailingList(medicalRecordMailing);
    }

    /**
     * 新增病历邮寄
     *
     * @param medicalRecordMailing 病历邮寄
     * @return 结果
     */
    @Override
    public int insertMedicalRecordMailing(MedicalRecordMailing medicalRecordMailing) {
        medicalRecordMailing.setCreateTime(DateUtils.getNowDate());
        return medicalRecordMailingMapper.insertMedicalRecordMailing(medicalRecordMailing);
    }

    /**
     * 修改病历邮寄
     *
     * @param medicalRecordMailing 病历邮寄
     * @return 结果
     */
    @Override
    public int updateMedicalRecordMailing(MedicalRecordMailing medicalRecordMailing) {
        medicalRecordMailing.setUpdateTime(DateUtils.getNowDate());
        return medicalRecordMailingMapper.updateMedicalRecordMailing(medicalRecordMailing);
    }

    /**
     * 批量删除病历邮寄
     *
     * @param ids 需要删除的病历邮寄主键
     * @return 结果
     */
    @Override
    public int deleteMedicalRecordMailingByIds(String ids) {
        return medicalRecordMailingMapper.deleteMedicalRecordMailingByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除病历邮寄信息
     *
     * @param id 病历邮寄主键
     * @return 结果
     */
    @Override
    public int deleteMedicalRecordMailingById(Long id) {
        return medicalRecordMailingMapper.deleteMedicalRecordMailingById(id);
    }
}
