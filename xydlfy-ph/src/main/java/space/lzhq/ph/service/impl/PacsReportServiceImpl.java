package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.mospital.bsoft.ListCheckReport;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.PacsReport;
import space.lzhq.ph.domain.PacsReportLink;
import space.lzhq.ph.dto.PacsReportDto;
import space.lzhq.ph.dto.ReportLink;
import space.lzhq.ph.mapper.PacsReportMapper;
import space.lzhq.ph.service.IPacsReportService;

import java.util.*;
import java.util.stream.Collectors;

@DataSource(DataSourceType.PACS)
@Service
public class PacsReportServiceImpl extends ServiceImpl<PacsReportMapper, PacsReport> implements IPacsReportService {

    private static final String INTERNAL_REPORT_URL = "http://**************:8081";
    private static final String EXTERNAL_REPORT_URL = "https://xjetyy-pacs-pdf.xjyqtl.cn";

    @Override
    public List<String> selectReportLinksByApplicationNo(
            @NotBlank(message = "申请单号不能为空")
            @Pattern(regexp = "^\\d{4,10}$", message = "申请单号必须是4-10位数字")
            String applicationNo
    ) {
        return baseMapper.selectReportLinksByApplicationNo(applicationNo).stream()
                .filter(StringUtils::isNotBlank)
                .map(link -> link.replace(INTERNAL_REPORT_URL, EXTERNAL_REPORT_URL))
                .filter(link -> link.startsWith(EXTERNAL_REPORT_URL))
                .sorted()
                .toList();
    }

    @Override
    public Map<String, List<String>> selectReportsByApplicationNos(
            @NotEmpty(message = "申请单号列表不能为空")
            List<@NotBlank(message = "申请单号不能为空")
            @Pattern(regexp = "^\\d{4,10}$", message = "申请单号必须是4-10位数字")
                    String> applicationNos
    ) {
        return baseMapper.selectReportsByApplicationNos(applicationNos).stream()
                .filter(link -> StringUtils.isNotBlank(link.getReportLink()))
                .map(link -> new PacsReportLink(link.getApplicationNo(),
                        link.getReportLink().replace(INTERNAL_REPORT_URL, EXTERNAL_REPORT_URL)))
                .filter(link -> link.getReportLink().startsWith(EXTERNAL_REPORT_URL))
                .collect(Collectors.groupingBy(PacsReportLink::getApplicationNo,
                        Collectors.collectingAndThen(
                                Collectors.mapping(PacsReportLink::getReportLink, Collectors.toList()),
                                list -> list.stream().sorted().toList()
                        )));
    }

    @Override
    public List<PacsReportDto> fillReportLinks(List<ListCheckReport> pacsReports) {
        Set<String> reportTypes = Set.of("DR", "CT", "MRI");
        List<String> applicationNos = pacsReports.stream()
                .filter(report -> StringUtils.isNotBlank(report.getCheckId()) && !reportTypes.contains(report.getReportTypeName()))
                .map(ListCheckReport::getCheckId)
                .distinct()
                .toList();
        Map<String, List<String>> reportUrls = applicationNos.isEmpty() ? Collections.emptyMap() : selectReportsByApplicationNos(applicationNos);
        return pacsReports.stream()
                .sorted(Comparator.comparing(ListCheckReport::getCheckId, Comparator.reverseOrder()))
                .map(report -> {
                    List<ReportLink> reportLinks;
                    if (reportTypes.contains(report.getReportTypeName())) {
                        if (StringUtils.isBlank(report.getCheckId())) {
                            reportLinks = Collections.emptyList();
                        } else {
                            reportLinks = List.of(
                                    new ReportLink("图文报告", "https://xjetyy-pacs.xjyqtl.cn/pages_nm/view_dist/index.html#/order?studyclass0=RIS&accessionnumber=" + report.getCheckId())
                            );
                        }
                    } else {
                        reportLinks = new ArrayList<>();
                        int i = 1;
                        for (String url : reportUrls.getOrDefault(report.getCheckId(), Collections.emptyList())) {
                            reportLinks.add(new ReportLink("图文报告" + i++, url));
                        }
                    }
                    return new PacsReportDto(report, reportLinks);
                })
                .toList();
    }

}