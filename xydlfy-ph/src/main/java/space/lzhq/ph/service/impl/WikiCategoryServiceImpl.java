package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.WikiCategory;
import space.lzhq.ph.mapper.WikiArticleMapper;
import space.lzhq.ph.mapper.WikiCategoryMapper;
import space.lzhq.ph.service.IWikiCategoryService;

import java.util.List;

/**
 * 知识分类Service业务层处理
 */
@Service
public class WikiCategoryServiceImpl implements IWikiCategoryService {

    private final WikiCategoryMapper wikiCategoryMapper;
    private final WikiArticleMapper wikiArticleMapper;

    public WikiCategoryServiceImpl(WikiCategoryMapper wikiCategoryMapper, WikiArticleMapper wikiArticleMapper) {
        this.wikiCategoryMapper = wikiCategoryMapper;
        this.wikiArticleMapper = wikiArticleMapper;
    }

    /**
     * 查询知识分类
     *
     * @param id 知识分类主键
     * @return 知识分类
     */
    @Override
    public WikiCategory selectWikiCategoryById(Long id) {
        return wikiCategoryMapper.selectWikiCategoryById(id);
    }

    /**
     * 查询知识分类列表
     *
     * @param wikiCategory 知识分类
     * @return 知识分类
     */
    @Override
    public List<WikiCategory> selectWikiCategoryList(WikiCategory wikiCategory) {
        return wikiCategoryMapper.selectWikiCategoryList(wikiCategory);
    }

    @Override
    public List<WikiCategory> selectAll() {
        return wikiCategoryMapper.selectAll();
    }

    /**
     * 新增知识分类
     *
     * @param wikiCategory 知识分类
     * @return 结果
     */
    @Override
    public int insertWikiCategory(WikiCategory wikiCategory) {
        return wikiCategoryMapper.insertWikiCategory(wikiCategory);
    }

    /**
     * 修改知识分类
     *
     * @param wikiCategory 知识分类
     * @return 结果
     */
    @Override
    public int updateWikiCategory(WikiCategory wikiCategory) {
        int result = wikiCategoryMapper.updateWikiCategory(wikiCategory);
        wikiArticleMapper.updateCategoryName(wikiCategory.getId(), wikiCategory.getName());
        return result;
    }

    /**
     * 批量删除知识分类
     *
     * @param ids 需要删除的知识分类主键
     * @return 结果
     */
    @Override
    public int deleteWikiCategoryByIds(String ids) {
        return wikiCategoryMapper.deleteWikiCategoryByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除知识分类信息
     *
     * @param id 知识分类主键
     * @return 结果
     */
    @Override
    public int deleteWikiCategoryById(Long id) {
        if (wikiArticleMapper.countByCategoryId(id) > 0) {
            throw new IllegalStateException("该分类下还有文章，不能删除");
        }
        return wikiCategoryMapper.deleteWikiCategoryById(id);
    }

}
