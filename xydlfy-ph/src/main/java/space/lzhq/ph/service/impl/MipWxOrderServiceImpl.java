package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayOrderQueryResult;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayRefundResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.text.StrValidator;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.MipWxOrder;
import space.lzhq.ph.ext.WeixinExt;
import space.lzhq.ph.mapper.MipWxOrderMapper;
import space.lzhq.ph.service.IMipWxOrderService;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class MipWxOrderServiceImpl extends ServiceImpl<MipWxOrderMapper, MipWxOrder> implements IMipWxOrderService {

    @Override
    public MipWxOrder getOneByPayOrderId(String payOrderId) {
        return lambdaQuery().eq(MipWxOrder::getPayOrderId, payOrderId).one();
    }

    @Override
    public MipWxOrder getOneByMedTransactionId(String medTransactionId) {
        return lambdaQuery().eq(MipWxOrder::getMedTransactionId, medTransactionId).one();
    }

    @Override
    public WxInsurancePayOrderQueryResult refreshOrderStatus(String medTransactionId) throws WxPayException {
        if (StrValidator.isBlank(medTransactionId)) {
            return null;
        }
        WxInsurancePayOrderQueryResult wxInsurancePayOrderQueryResult = WeixinExt.INSTANCE.getWxInsurancePayService().queryOrder(medTransactionId, "");
        MipWxOrder mipWxOrder = getOneByMedTransactionId(medTransactionId);
        if (mipWxOrder != null) {
            mipWxOrder.setInsuranceOrderId(wxInsurancePayOrderQueryResult.getInsuranceOrderId());
            mipWxOrder.setInsuranceTradeStatus(wxInsurancePayOrderQueryResult.getInsuranceTradeStatus());
            mipWxOrder.setMedTradeState(wxInsurancePayOrderQueryResult.getMedTradeState());
            mipWxOrder.setCashOrderId(wxInsurancePayOrderQueryResult.getCashOrderId());
            mipWxOrder.setCashTradeStatus(wxInsurancePayOrderQueryResult.getCashTradeStatus());
            mipWxOrder.setTradeStatusDesc(wxInsurancePayOrderQueryResult.getTradeStatusDesc());
            updateById(mipWxOrder);
        }
        return wxInsurancePayOrderQueryResult;
    }

    @Override
    public List<MipWxOrder> queryUnlockableOrders(String patientIdCardNo) {
        return lambdaQuery().eq(MipWxOrder::getPatientIdCardNo, patientIdCardNo)
                .eq(MipWxOrder::getHospOutRefundNo, "")
                .isNull(MipWxOrder::getUnlockTime)
                .notIn(MipWxOrder::getInsuranceTradeStatus, "PAYED", "REFUND")
                .ne(MipWxOrder::getSettlementIds, "")
                .orderByDesc(MipWxOrder::getId)
                .list();
    }

    public boolean canUnlock(MipWxOrder mipWxOrder) {
        if (mipWxOrder == null) {
            return false;
        }

        String insuranceTradeStatus = mipWxOrder.getInsuranceTradeStatus();
        return StrValidator.isBlank(mipWxOrder.getHospOutRefundNo())
                && mipWxOrder.getUnlockTime() == null
                && !("PAYED".equals(insuranceTradeStatus) || "REFUND".equals(insuranceTradeStatus));
    }

    @Override
    public void updateUnlockTime(Long id, LocalDateTime unlockTime) {
        lambdaUpdate()
                .set(MipWxOrder::getUnlockTime, unlockTime)
                .eq(MipWxOrder::getId, id)
                .update();
    }

    @Override
    public boolean updateOnRefund(MipWxOrder mipWxOrder, WxInsurancePayRefundResult refundResult) {
        mipWxOrder.setMedRefundId(refundResult.getMedRefundId());
        mipWxOrder.setCashRefundId(refundResult.getCashRefundId());
        mipWxOrder.setInsuranceRefundId(refundResult.getInsuranceRefundId());
        return updateById(mipWxOrder);
    }

}
