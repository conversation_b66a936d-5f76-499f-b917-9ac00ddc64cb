package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.PscCarer;
import space.lzhq.ph.domain.PscOrder;
import space.lzhq.ph.mapper.PscOrderMapper;
import space.lzhq.ph.service.IPscOrderService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Service
public class PscOrderServiceImpl implements IPscOrderService {
    @Autowired
    private PscOrderMapper pscOrderMapper;

    /**
     * 查询订单
     *
     * @param id 订单ID
     * @return 订单
     */
    @Override
    public PscOrder selectPscOrderById(Long id) {
        return pscOrderMapper.selectPscOrderById(id);
    }

    /**
     * 查询订单列表
     *
     * @param pscOrder 订单
     * @return 订单
     */
    @Override
    public List<PscOrder> selectPscOrderList(PscOrder pscOrder) {
        return pscOrderMapper.selectPscOrderList(pscOrder);
    }

    @Override
    public List<PscOrder> selectUnfinishedOrderList(String patientId) {
        return pscOrderMapper.selectUnfinishedOrderList(patientId);
    }

    @Override
    public List<PscOrder> selectPscOrderListByCarer(
            String carerEmployeeNo,
            String patientDepartment,
            String examItemCode,
            LocalDateTime beginCreateTime,
            LocalDateTime endCreateTime
    ) {
        return pscOrderMapper.selectPscOrderListByCarer(
                carerEmployeeNo,
                patientDepartment,
                examItemCode,
                beginCreateTime,
                endCreateTime
        );
    }

    @Override
    public List<String> selectDepartments() {
        return pscOrderMapper.selectDepartments();
    }

    /**
     * 新增订单
     *
     * @param pscOrder 订单
     * @return 结果
     */
    @Override
    public int insertPscOrder(PscOrder pscOrder) {
        pscOrder.setCreateTime(DateUtils.getNowDate());
        return pscOrderMapper.insertPscOrder(pscOrder);
    }

    /**
     * 修改订单
     *
     * @param pscOrder 订单
     * @return 结果
     */
    @Override
    public int updatePscOrder(PscOrder pscOrder) {
        return pscOrderMapper.updatePscOrder(pscOrder);
    }

    /**
     * 删除订单对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deletePscOrderByIds(String ids) {
        return pscOrderMapper.deletePscOrderByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除订单信息
     *
     * @param id 订单ID
     * @return 结果
     */
    @Override
    public int deletePscOrderById(Long id) {
        return pscOrderMapper.deletePscOrderById(id);
    }

    @Override
    public void assignCarer(String ids, PscCarer pscCarer) {
        pscOrderMapper.assignCarer(ids.split(","), pscCarer);
    }

    @Override
    public void takeBatch(String[] ids, PscCarer carer) {
        pscOrderMapper.takeBatch(ids, carer);
    }

    @Override
    public void release(String[] ids, String carerEmployeeNo) {
        pscOrderMapper.release(ids, carerEmployeeNo);
    }
}
