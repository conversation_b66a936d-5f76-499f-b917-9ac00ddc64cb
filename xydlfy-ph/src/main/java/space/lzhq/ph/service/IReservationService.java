package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotNull;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Reservation;

import java.time.LocalDate;
import java.util.List;

@Service
public interface IReservationService extends IService<Reservation> {

    Reservation getOneByReservationNumber(String reservationNumber);

    List<Reservation> getValidReservationsByDate(@NotNull LocalDate date);

}
