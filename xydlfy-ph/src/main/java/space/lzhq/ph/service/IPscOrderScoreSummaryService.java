package space.lzhq.ph.service;

import org.apache.ibatis.annotations.Param;
import space.lzhq.ph.domain.PscOrderScoreSummary;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IPscOrderScoreSummaryService {

    /**
     * 汇总
     *
     * @param minConfirmTime 最小验收时间
     * @param maxConfirmTime 最大验收时间
     * @return 汇总结果
     */
    List<PscOrderScoreSummary> summary(
            @Param("minConfirmTime") LocalDateTime minConfirmTime,
            @Param("maxConfirmTime") LocalDateTime maxConfirmTime
    );

}
