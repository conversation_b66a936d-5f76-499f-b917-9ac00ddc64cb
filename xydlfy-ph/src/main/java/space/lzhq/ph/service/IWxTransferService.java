package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import space.lzhq.ph.domain.WxTransfer;
import space.lzhq.ph.domain.WxTransferSearchForm;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface IWxTransferService extends IService<WxTransfer> {

    List<WxTransfer> listBySearchForm(WxTransferSearchForm searchForm);

    List<WxTransfer> listByOpenId(@NotBlank(message = "openId不能为空") String openId);

    List<WxTransfer> listByMinCreateTimeAndTransferState(
            @NotNull(message = "最小下单时间不能为空") LocalDateTime minCreateTime,
            @NotBlank(message = "转账状态不能为空") String transferState
    );

    Optional<WxTransfer> getOneByOutBillNo(@NotBlank(message = "outBillNo不能为空") String outBillNo);

    BigDecimal sumAmountByOpenIdAndDate(
            @NotBlank(message = "openId不能为空") String openId,
            @NotNull(message = "日期不能为空") LocalDate date
    );

    BigDecimal sumAmountByDate(
            @NotNull(message = "日期不能为空") LocalDate date
    );

    /**
     * 检查用户是否有未成功完成的转账记录
     *
     * @param openId 用户openId
     * @return 如果有未成功完成的转账记录返回true，否则返回false
     */
    boolean hasUnsuccessfulTransfers(@NotBlank(message = "openId不能为空") String openId);

    /**
     * 检查用户是否有进行中的转账记录(非最终状态)
     *
     * @param openId 用户openId
     * @return 如果有进行中的转账记录返回true，否则返回false
     */
    boolean hasPendingTransfers(@NotBlank(message = "openId不能为空") String openId);

    void menzhenRefund(WxTransfer wxTransfer);

    void renewOrderNo(WxTransfer wxTransfer);

    void requestTransfer(WxTransfer wxTransfer);

    void updateTransferState(WxTransfer wxTransfer);

    void notifyReceiveTransfer(@NotNull WxTransfer wxTransfer);
    
}
