package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.Reservation;
import space.lzhq.ph.mapper.ReservationMapper;
import space.lzhq.ph.service.IReservationService;

import java.time.LocalDate;
import java.util.List;

@Service
@Slf4j
@Validated
public class ReservationServiceImpl extends ServiceImpl<ReservationMapper, Reservation> implements IReservationService {


    @Override
    public Reservation getOneByReservationNumber(String reservationNumber) {
        return lambdaQuery()
                .eq(Reservation::getReservationNumber, reservationNumber)
                .eq(Reservation::getOperationType, Reservation.OPERATION_TYPE_RESERVE)
                .one();
    }

    @Override
    public List<Reservation> getValidReservationsByDate(LocalDate date) {
        return lambdaQuery()
                .eq(Reservation::getJzDate, date)
                .eq(Reservation::getOperationType, Reservation.OPERATION_TYPE_RESERVE)
                .eq(Reservation::getOperationResult, Reservation.OPERATION_RESULT_SUCCESS)
                .eq(Reservation::getStatus, Reservation.RESERVATION_STATUS_INIT)
                .list();
    }
}
