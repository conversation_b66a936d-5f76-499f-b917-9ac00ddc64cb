package space.lzhq.ph.service.impl;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import space.lzhq.ph.domain.Department;
import space.lzhq.ph.mapper.DepartmentMapper;
import space.lzhq.ph.service.IDepartmentService;

import java.util.List;

/**
 * 科室Service业务层处理
 *
 * @date 2020-05-24
 */
@Service
public class DepartmentServiceImpl implements IDepartmentService {

    private static final Logger log = LoggerFactory.getLogger(DoctorServiceImpl.class);

    @Autowired
    private DepartmentMapper departmentMapper;

    /**
     * 查询科室
     *
     * @param id 科室ID
     * @return 科室
     */
    @Override
    public Department selectDepartmentById(String id) {
        return departmentMapper.selectDepartmentById(id);
    }

    @Override
    public List<Department> selectDepartmentAll() {
        return departmentMapper.selectDepartmentAll();
    }

    @Override
    public List<Department> selectDepartmentListByKeyword(String keyword) {
        if (StringUtils.isNotBlank(keyword)) {
            keyword = "%" + keyword.trim().toLowerCase() + "%";
        }
        return departmentMapper.selectDepartmentListByKeyword(keyword.trim());
    }

    /**
     * 查询科室列表
     *
     * @param department 科室
     * @return 科室
     */
    @Override
    public List<Department> selectDepartmentList(Department department) {
        return departmentMapper.selectDepartmentList(department);
    }

    /**
     * 新增科室
     *
     * @param department 科室
     * @return 结果
     */
    @Override
    public int insertDepartment(Department department) {
        return departmentMapper.insertDepartment(department);
    }

    /**
     * 修改科室
     *
     * @param department 科室
     * @return 结果
     */
    @Override
    public int updateDepartment(Department department) {
        return departmentMapper.updateDepartment(department);
    }

    /**
     * 删除科室对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteDepartmentByIds(String ids) {
        return departmentMapper.deleteDepartmentByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除科室信息
     *
     * @param id 科室ID
     * @return 结果
     */
    @Override
    public int deleteDepartmentById(String id) {
        return departmentMapper.deleteDepartmentById(id);
    }

}
