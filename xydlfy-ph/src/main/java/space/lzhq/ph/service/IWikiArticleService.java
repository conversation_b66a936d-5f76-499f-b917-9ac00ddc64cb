package space.lzhq.ph.service;

import space.lzhq.ph.domain.WikiArticle;

import java.util.List;

/**
 * 文章Service接口
 */
public interface IWikiArticleService {
    /**
     * 查询文章
     *
     * @param id 文章ID
     * @return 文章
     */
    WikiArticle selectWikiArticleById(Long id);

    /**
     * 查询文章列表
     *
     * @param article 文章
     * @return 文章集合
     */
    List<WikiArticle> selectWikiArticleList(WikiArticle article);

    List<WikiArticle> selectListByCategoryId(Long categoryId);

    /**
     * 新增文章
     *
     * @param article 文章
     * @return 结果
     */
    int insertWikiArticle(WikiArticle article);

    /**
     * 修改文章
     *
     * @param article 文章
     * @return 结果
     */
    int updateWikiArticle(WikiArticle article);

    /**
     * 批量删除文章
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteWikiArticleByIds(String ids);

    /**
     * 删除文章信息
     *
     * @param id 文章ID
     * @return 结果
     */
    int deleteWikiArticleById(Long id);
}
