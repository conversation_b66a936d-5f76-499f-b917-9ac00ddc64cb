package space.lzhq.ph.service;

import org.mospital.alipay.AlipayBillItem;
import org.mospital.bsoft.BillDetail;
import org.mospital.bsoft.Result;
import space.lzhq.ph.domain.AlipayCheck;

import java.util.List;

/**
 * 支付宝对账Service接口
 *
 * <AUTHOR>
 * @date 2023-02-04
 */
public interface IAlipayCheckService {
    /**
     * 查询支付宝对账
     *
     * @param id 支付宝对账主键
     * @return 支付宝对账
     */
    AlipayCheck selectAlipayCheckById(Long id);

    /**
     * 查询支付宝对账列表
     *
     * @param alipayCheck 支付宝对账
     * @return 支付宝对账集合
     */
    List<AlipayCheck> selectAlipayCheckList(AlipayCheck alipayCheck);

    /**
     * 新增支付宝对账
     *
     * @param alipayCheck 支付宝对账
     * @return 结果
     */
    int insertAlipayCheck(AlipayCheck alipayCheck);

    /**
     * 修改支付宝对账
     *
     * @param alipayCheck 支付宝对账
     * @return 结果
     */
    int updateAlipayCheck(AlipayCheck alipayCheck);

    /**
     * 批量删除支付宝对账
     *
     * @param ids 需要删除的支付宝对账主键集合
     * @return 结果
     */
    int deleteAlipayCheckByIds(String ids);

    /**
     * 删除支付宝对账信息
     *
     * @param id 支付宝对账主键
     * @return 结果
     */
    int deleteAlipayCheckById(Long id);

    AlipayCheck getOrCreate(Long id);

    boolean syncAlipayBill(Long id, List<AlipayBillItem> alipayBillItems);

    boolean syncHisBill(Long id, Result<List<BillDetail>> billResponse);

}
