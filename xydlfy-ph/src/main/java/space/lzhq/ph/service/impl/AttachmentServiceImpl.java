package space.lzhq.ph.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.domain.Attachment;
import space.lzhq.ph.enums.AttachmentMasterType;
import space.lzhq.ph.enums.AttachmentType;
import space.lzhq.ph.mapper.AttachmentMapper;
import space.lzhq.ph.service.AttachmentFileDeleter;
import space.lzhq.ph.service.IAttachmentService;

import java.time.LocalDateTime;
import java.util.Optional;

@Slf4j
@Service
@Validated
public class AttachmentServiceImpl
        extends ServiceImpl<AttachmentMapper, Attachment>
        implements IAttachmentService {

    private AttachmentFileDeleter attachmentFileDeleter;

    @Autowired
    public AttachmentServiceImpl(AttachmentFileDeleter attachmentFileDeleter) {
        this.attachmentFileDeleter = attachmentFileDeleter;
    }

    @Override
    public Optional<Attachment> save(String openId, AttachmentType type, String fileName, String fileUrl) {
        Attachment attachment = Attachment.builder()
                .openId(openId)
                .type(type)
                .fileName(fileName)
                .fileUrl(fileUrl)
                .createTime(LocalDateTime.now())
                .build();

        if (this.save(attachment)) {
            return Optional.of(attachment);
        } else {
            log.error("Failed to save CardRefundAttachment for openId: {}", openId);
            return Optional.empty();
        }
    }

    @Override
    public boolean updateMasterInfo(@NotBlank String attachmentId, @NotBlank String masterId, @NotNull AttachmentMasterType masterType) {
        if (StringUtils.isBlank(attachmentId) || StringUtils.isBlank(masterId)) {
            log.warn("updateRegistrationId skipped – blank args attachmentId={}, registrationId={}",
                    attachmentId, masterId);
            return false;
        }
        return this.lambdaUpdate()
                .set(Attachment::getMasterId, masterId)
                .set(Attachment::getMasterType, masterType)
                .eq(Attachment::getId, attachmentId)
                .isNull(Attachment::getMasterId)
                .update();
    }

    @Override
    public boolean deleteAttachment(String id) {
        try {
            // 先查询附件记录
            Attachment attachment = this.getById(id);
            if (attachment == null) {
                log.warn("附件记录不存在，ID: {}", id);
                return false;
            }

            // 删除数据库记录
            boolean dbDeleteSuccess = this.removeById(id);
            if (!dbDeleteSuccess) {
                log.error("删除附件数据库记录失败，ID: {}", id);
                return false;
            }

            log.info("成功删除附件数据库记录，ID: {}", id);

            // 删除关联的文件
            deleteAssociatedFile(id, attachment);

            return true;

        } catch (Exception e) {
            log.error("删除附件时发生异常，ID: {}", id, e);
            return false;
        }
    }

    private void deleteAssociatedFile(String id, Attachment attachment) {
        try {
            String fileUrl = attachment.getFileUrl();
            if (fileUrl != null && !fileUrl.trim().isEmpty()) {
                boolean fileDeleteSuccess = attachmentFileDeleter.deleteFile(fileUrl);
                if (fileDeleteSuccess) {
                    log.info("成功删除附件文件，URL: {}", fileUrl);
                } else {
                    log.warn("删除附件文件失败，但数据库记录已删除，URL: {}", fileUrl);
                }
            } else {
                log.warn("附件记录中文件URL为空，跳过文件删除，ID: {}", id);
            }
        } catch (Exception e) {
            log.error("删除附件文件时发生异常，但数据库记录已删除，ID: {}, URL: {}",
                    id, attachment.getFileUrl(), e);
        }
    }

}
