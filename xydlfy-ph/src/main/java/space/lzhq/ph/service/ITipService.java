package space.lzhq.ph.service;

import org.apache.ibatis.annotations.Param;
import space.lzhq.ph.domain.Tip;

import java.util.List;

/**
 * 提示信息Service接口
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
public interface ITipService {
    /**
     * 查询提示信息
     *
     * @param id 提示信息主键
     * @return 提示信息
     */
    public Tip selectTipById(Long id);

    /**
     * 查询提示信息列表
     *
     * @param tip 提示信息
     * @return 提示信息集合
     */
    public List<Tip> selectTipList(Tip tip);

    /**
     * 按照 codeList 查询
     */
    public List<Tip> selectByCodeList(@Param("codeList") List<String> codeList);

    /**
     * 新增提示信息
     *
     * @param tip 提示信息
     * @return 结果
     */
    public int insertTip(Tip tip);

    /**
     * 修改提示信息
     *
     * @param tip 提示信息
     * @return 结果
     */
    public int updateTip(Tip tip);

    /**
     * 批量删除提示信息
     *
     * @param ids 需要删除的提示信息主键集合
     * @return 结果
     */
    public int deleteTipByIds(String ids);

    /**
     * 删除提示信息信息
     *
     * @param id 提示信息主键
     * @return 结果
     */
    public int deleteTipById(Long id);
}
