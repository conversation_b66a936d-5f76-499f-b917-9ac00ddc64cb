package space.lzhq.ph.service.impl;

import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.binarywang.wxpay.bean.transfer.TransferBillsGetResult;
import com.github.binarywang.wxpay.bean.transfer.TransferBillsRequest;
import com.github.binarywang.wxpay.bean.transfer.TransferBillsResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.ext.LambdaQueryChainWrapperExt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.mospital.bsoft.BSoftService;
import org.mospital.bsoft.MenzhenRefund;
import org.mospital.bsoft.MenzhenRefundForm;
import org.mospital.bsoft.Result;
import org.mospital.common.StringKit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import space.lzhq.ph.common.PaymentKit;
import space.lzhq.ph.common.ServiceType;
import space.lzhq.ph.configuration.WxSubscribeMessageConfiguration;
import space.lzhq.ph.domain.WxTransfer;
import space.lzhq.ph.domain.WxTransferSearchForm;
import space.lzhq.ph.ext.WeixinExt;
import space.lzhq.ph.mapper.WxTransferMapper;
import space.lzhq.ph.service.IWxTransferService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static space.lzhq.ph.domain.WxTransfer.FINAL_STATES;

@Slf4j
@Service
@Validated
public class WxTransferServiceImpl extends ServiceImpl<WxTransferMapper, WxTransfer> implements IWxTransferService {

    private final WxPayService wxPayService;
    private final WxSubscribeMessageConfiguration wxSubscribeMessageConfiguration;


    private static final String SUCCESS = "SUCCESS";
    private static final String FAILED = "FAILED";

    @Autowired
    public WxTransferServiceImpl(WxPayService wxPayService, WxSubscribeMessageConfiguration wxSubscribeMessageConfiguration) {
        this.wxPayService = wxPayService;
        this.wxSubscribeMessageConfiguration = wxSubscribeMessageConfiguration;
    }

    public List<WxTransfer> listBySearchForm(WxTransferSearchForm searchForm) {
        LambdaQueryChainWrapperExt<WxTransfer> queryChainWrapperExt = new LambdaQueryChainWrapperExt<>(lambdaQuery())
                .eqIfNotEmpty(WxTransfer::getOutBillNo, searchForm.getOutBillNo())
                .eqIfNotEmpty(WxTransfer::getTransferBillNo, searchForm.getTransferBillNo())
                .eqIfNotEmpty(WxTransfer::getPatientIdCardNo, searchForm.getPatientIdCardNo())
                .eqIfNotEmpty(WxTransfer::getPatientCardNo, searchForm.getPatientCardNo());
        if (SUCCESS.equalsIgnoreCase(searchForm.getTransferState())) {
            queryChainWrapperExt.eqIfNotEmpty(WxTransfer::getTransferState, SUCCESS);
        }
        if (FAILED.equalsIgnoreCase(searchForm.getTransferState())) {
            queryChainWrapperExt.neIfPresent(WxTransfer::getTransferState, SUCCESS);
        }
        return queryChainWrapperExt.wrapper().list();
    }

    public List<WxTransfer> listByOpenId(String openId) {
        if (StringUtils.isBlank(openId)) {
            return Collections.emptyList();
        }
        return lambdaQuery().eq(WxTransfer::getOpenId, openId).list();
    }

    @Override
    public List<WxTransfer> listByMinCreateTimeAndTransferState(LocalDateTime minCreateTime, String transferState) {
        return lambdaQuery()
                .ge(WxTransfer::getTransferCreateTime, minCreateTime)
                .eq(WxTransfer::getTransferState, transferState)
                .list();
    }

    public Optional<WxTransfer> getOneByOutBillNo(String outBillNo) {
        return lambdaQuery().eq(WxTransfer::getOutBillNo, outBillNo).oneOpt();
    }

    @Override
    public BigDecimal sumAmountByOpenIdAndDate(String openId, LocalDate date) {
        QueryWrapper<WxTransfer> queryWrapper = new QueryWrapper<WxTransfer>()
                .select("IFNULL(SUM(amount), 0) as total")
                .eq("open_id", openId)
                .between("transfer_create_time", date.atStartOfDay(), date.atTime(LocalTime.MAX));
        Map<String, Object> result = getMap(queryWrapper);
        return result != null ? new BigDecimal(result.get("total").toString()) : BigDecimal.ZERO;
    }

    @Override
    public BigDecimal sumAmountByDate(LocalDate date) {
        QueryWrapper<WxTransfer> queryWrapper = new QueryWrapper<WxTransfer>()
                .select("IFNULL(SUM(amount), 0) as total")
                .between("transfer_create_time", date.atStartOfDay(), date.atTime(LocalTime.MAX));
        Map<String, Object> result = getMap(queryWrapper);
        return result != null ? new BigDecimal(result.get("total").toString()) : BigDecimal.ZERO;
    }

    @Override
    public boolean hasUnsuccessfulTransfers(String openId) {
        return lambdaQuery()
                .eq(WxTransfer::getOpenId, openId)
                .isNotNull(WxTransfer::getTransferState)
                .ne(WxTransfer::getTransferState, SUCCESS)
                .exists();
    }

    @Override
    public boolean hasPendingTransfers(String openId) {
        return lambdaQuery()
                .eq(WxTransfer::getOpenId, openId)
                .and(i -> i.isNull(WxTransfer::getTransferState)
                        .or().notIn(WxTransfer::getTransferState, FINAL_STATES))
                .exists();
    }

    public void menzhenRefund(WxTransfer wxTransfer) {
        MenzhenRefundForm menzhenRefundForm = new MenzhenRefundForm(
                "",
                wxTransfer.getAmount(),
                "",
                "20",
                wxTransfer.getOutBillNo(),
                wxTransfer.getPatientCardNo(),
                wxTransfer.getPatientId(),
                "36",
                "",
                ""
        );
        Result<MenzhenRefund> menzhenRefundResult = BSoftService.Companion.menzhenRefund(menzhenRefundForm);

        if (menzhenRefundResult.isOk()) {
            MenzhenRefund menzhenRefund = menzhenRefundResult.getData();
            wxTransfer.setHisOrderNo(menzhenRefund.getOrderNo());
            wxTransfer.setHisReceiptNo(menzhenRefund.getReceiptNo());
            wxTransfer.setHisRefundSuccess(true);
        } else {
            wxTransfer.setHisRefundSuccess(false);
            wxTransfer.setHisRefundFailReason(menzhenRefundResult.getMessage());
        }

        LocalDateTime now = LocalDateTime.now();
        wxTransfer.setHisRefundTime(now);

        updateById(wxTransfer);
    }

    public void renewOrderNo(WxTransfer wxTransfer) {
        wxTransfer.setOutBillNo(PaymentKit.INSTANCE.newOrderId(ServiceType.TF));
        wxTransfer.setTransferBillNo("");
        wxTransfer.setTransferState("");
        wxTransfer.setTransferPackageInfo("");
        wxTransfer.setTransferFailReason("");
        wxTransfer.setTransferCreateTime(null);
        wxTransfer.setTransferUpdateTime(null);
        updateById(wxTransfer);
    }

    public void requestTransfer(WxTransfer wxTransfer) {
        String appId = wxPayService.getConfig().getAppId();
        Integer amountCents = Math.toIntExact(PaymentKit.INSTANCE.yuanToFen(wxTransfer.getAmount()));
        TransferBillsRequest request = TransferBillsRequest.newBuilder()
                .appid(appId)
                .outBillNo(wxTransfer.getOutBillNo())
                .transferSceneId("1000")
                .openid(wxTransfer.getOpenId())
                .notifyUrl(Constants.ON_WX_TRANSFER)
                .userName("")
                .transferAmount(amountCents)
                .transferRemark("门诊预交金返还")
                .userRecvPerception("现金奖励")
                .transferSceneReportInfos(List.of(
                        TransferBillsRequest.TransferSceneReportInfo.newBuilder()
                                .infoType("活动名称")
                                .infoContent("门诊预交金余额清退")
                                .build(),
                        TransferBillsRequest.TransferSceneReportInfo.newBuilder()
                                .infoType("奖励说明")
                                .infoContent("门诊预交金返还")
                                .build()
                ))
                .build();
        try {
            TransferBillsResult transferResult = wxPayService.getTransferService().transferBills(request);
            updateTransferInfo(wxTransfer, transferResult);
        } catch (WxPayException e) {
            log.error(e.getErrCodeDes(), e);
            wxTransfer.setTransferCreateTime(LocalDateTime.now());
            wxTransfer.setTransferFailReason(e.getMessage());
        }

        updateById(wxTransfer);
    }

    private void updateTransferInfo(WxTransfer wxTransfer, TransferBillsResult transferResult) {
        wxTransfer.setTransferBillNo(transferResult.getTransferBillNo());
        wxTransfer.setTransferState(transferResult.getState());
        wxTransfer.setTransferFailReason(CharSequenceUtil.emptyIfNull(transferResult.getFailReason()).toString());
        wxTransfer.setTransferPackageInfo(transferResult.getPackageInfo());
        wxTransfer.setTransferCreateTime(parseDateTime(transferResult.getCreateTime()));
    }

    public void updateTransferState(WxTransfer wxTransfer) {
        if (wxTransfer == null || StringUtils.isBlank(wxTransfer.getOutBillNo())) {
            log.warn("更新转账状态失败：商户订单号为空");
            throw new IllegalArgumentException("商户订单号不能为空");
        }
        if (wxTransfer.getTransferCreateTime().plusDays(30).isBefore(LocalDateTime.now())) {
            log.warn("单号：{}，错误：单据超过30天无法通过API查询", wxTransfer.getOutBillNo());
            return;
        }

        try {
            TransferBillsGetResult result = wxPayService.getTransferService()
                    .getBillsByOutBillNo(wxTransfer.getOutBillNo());

            updateTransferStateInfo(wxTransfer, result);
            updateById(wxTransfer);

            log.info("转账状态更新成功，单号：{}，状态：{}",
                    wxTransfer.getOutBillNo(), result.getState());
        } catch (WxPayException e) {
            log.error("查询转账状态失败，单号：{}，错误：{}",
                    wxTransfer.getOutBillNo(), e.getMessage(), e);
            throw new RuntimeException("查询转账状态失败", e);
        }
    }

    @Override
    public void notifyReceiveTransfer(WxTransfer wxTransfer) {
        try {
            WeixinExt.INSTANCE.sendSubscribeMessage(
                    wxSubscribeMessageConfiguration.receiveTransferId,
                    wxSubscribeMessageConfiguration.receiveTransferPage,
                    buildMessageData(wxTransfer),
                    wxTransfer.getOpenId(),
                    wxTransfer.getOutBillNo()
            );
        } catch (Exception e) {
            log.error("发送订阅消息失败: ", e);
        }
    }

    private void updateTransferStateInfo(WxTransfer wxTransfer, TransferBillsGetResult result) {
        wxTransfer.setTransferState(result.getState());
        wxTransfer.setTransferFailReason(result.getFailReason());

        if (StringUtils.isBlank(wxTransfer.getTransferBillNo())) {
            wxTransfer.setTransferBillNo(result.getTransferBillNo());
        }

        updateTransferTimes(wxTransfer, result);
    }

    private void updateTransferTimes(WxTransfer wxTransfer, TransferBillsGetResult result) {
        if (StringUtils.isNotBlank(result.getCreateTime())) {
            LocalDateTime parsedCreateTime = parseDateTime(result.getCreateTime());
            if (parsedCreateTime != null) {
                wxTransfer.setTransferCreateTime(parsedCreateTime);
            }
        }

        if (StringUtils.isNotBlank(result.getUpdateTime())) {
            LocalDateTime parsedUpdateTime = parseDateTime(result.getUpdateTime());
            if (parsedUpdateTime != null) {
                wxTransfer.setTransferUpdateTime(parsedUpdateTime);
            }
        }
    }

    private LocalDateTime parseDateTime(String dateTimeStr) {
        if (StringUtils.isBlank(dateTimeStr)) {
            log.warn("日期时间字符串为空");
            return null;
        }

        try {
            return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        } catch (Exception e) {
            log.error("解析时间失败: {}, 错误: {}", dateTimeStr, e.getMessage());
            return null;
        }
    }

    private List<WxMaSubscribeMessage.MsgData> buildMessageData(WxTransfer wxTransfer) {
        List<WxMaSubscribeMessage.MsgData> messageDataList = new ArrayList<>();
        // 姓名
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "thing1",
                StringKit.INSTANCE.sanitizeThing(wxTransfer.getPatientName())
        ));
        // 就诊卡
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "character_string2",
                StringKit.INSTANCE.sanitizeCharacterString(wxTransfer.getPatientCardNo())
        ));
        // 金额
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "amount3",
                wxTransfer.getAmount().toString()
        ));
        // 退款进度
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "thing4",
                StringKit.INSTANCE.sanitizeThing("待确认收款")
        ));
        // 温馨提醒
        messageDataList.add(WeixinExt.INSTANCE.newWxMaSubscribeMessageThingData(
                "thing5",
                StringKit.INSTANCE.sanitizeThing("请尽快确认，逾期资金将被退回")
        ));

        return messageDataList;
    }

}
