package space.lzhq.ph.service;

import space.lzhq.ph.domain.ZhuyuanPatient;

import java.util.List;

/**
 * 住院患者Service接口
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
public interface IZhuyuanPatientService {
    /**
     * 查询住院患者
     *
     * @param id 住院患者主键
     * @return 住院患者
     */
    public ZhuyuanPatient selectZhuyuanPatientById(Long id);

    /**
     * 查询住院患者列表
     *
     * @param zhuyuanPatient 住院患者
     * @return 住院患者集合
     */
    public List<ZhuyuanPatient> selectZhuyuanPatientList(ZhuyuanPatient zhuyuanPatient);

    /**
     * 新增住院患者
     *
     * @param zhuyuanPatient 住院患者
     * @return 结果
     */
    public int insertZhuyuanPatient(ZhuyuanPatient zhuyuanPatient);

    /**
     * 修改住院患者
     *
     * @param zhuyuanPatient 住院患者
     * @return 结果
     */
    public int updateZhuyuanPatient(ZhuyuanPatient zhuyuanPatient);

    /**
     * 批量删除住院患者
     *
     * @param ids 需要删除的住院患者主键集合
     * @return 结果
     */
    public int deleteZhuyuanPatientByIds(String ids);

    /**
     * 删除住院患者信息
     *
     * @param id 住院患者主键
     * @return 结果
     */
    public int deleteZhuyuanPatientById(Long id);

    ZhuyuanPatient selectActivePatientByOpenId(String openId);
}
