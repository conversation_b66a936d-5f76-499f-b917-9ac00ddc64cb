package space.lzhq.ph.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import org.mospital.bsoft.ListCheckReport;
import space.lzhq.ph.domain.PacsReport;
import space.lzhq.ph.dto.PacsReportDto;

import java.util.List;
import java.util.Map;

public interface IPacsReportService extends IService<PacsReport> {

    /**
     * 根据申请单号查询报告链接
     *
     * @param applicationNo 申请单号
     * @return 报告链接列表
     */
    List<String> selectReportLinksByApplicationNo(
            @NotBlank(message = "申请单号不能为空")
            @Pattern(regexp = "^\\d{4,10}$", message = "申请单号必须是4-10位数字")
            String applicationNo
    );

    /**
     * 根据多个申请单号查询报告链接
     *
     * @param applicationNos 申请单号列表
     * @return 申请单号和报告链接结果列表
     */
    Map<String, List<String>> selectReportsByApplicationNos(
            @NotEmpty(message = "申请单号列表不能为空")
            List<@NotBlank(message = "申请单号不能为空")
            @Pattern(regexp = "^\\d{4,10}$", message = "申请单号必须是4-10位数字")
                    String> applicationNos
    );

    List<PacsReportDto> fillReportLinks(List<ListCheckReport> pacsReports);

}