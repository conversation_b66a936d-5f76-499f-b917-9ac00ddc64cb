package space.lzhq.ph.job;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import space.lzhq.ph.domain.WxTransfer;
import space.lzhq.ph.service.IWxTransferService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 通知用户确认收款
 * 表达式：0 0 * * * ?
 */
@Slf4j
@Component("notifyReceiveTransferJob")
public class NotifyReceiveTransferJob {

    private final IWxTransferService wxTransferService;

    @Autowired
    public NotifyReceiveTransferJob(IWxTransferService wxTransferService) {
        this.wxTransferService = wxTransferService;
    }

    public void execute() {
        try {
            LocalDateTime minCreateTime = LocalDateTime.now().minusHours(3);
            List<WxTransfer> transfers = wxTransferService.listByMinCreateTimeAndTransferState(minCreateTime, WxTransfer.WAIT_USER_CONFIRM);
            for (WxTransfer transfer : transfers) {
                wxTransferService.notifyReceiveTransfer(transfer);
            }
        } catch (Exception e) {
            log.error("[通知用户确认收款]任务执行失败", e);

        }
    }

}
