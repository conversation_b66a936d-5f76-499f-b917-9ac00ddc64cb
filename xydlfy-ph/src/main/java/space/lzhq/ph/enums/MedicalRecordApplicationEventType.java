package space.lzhq.ph.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

/**
 * 病历复印申请时间线事件类型枚举
 *
 * <AUTHOR>
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum MedicalRecordApplicationEventType {

    // 状态变更事件
    APPLICATION_CREATED("创建申请"),
    APPLICATION_SUBMITTED("提交申请"),
    APPLICATION_APPROVED("审核通过"),
    APPLICATION_REJECTED("审核驳回"),
    APPLICATION_RESUBMITTED("重新提交"),
    APPLICATION_COMPLETED("完成申请"),
    APPLICATION_DELETED("删除申请"),
    APPLICATION_PAID("支付完成"),
    APPLICATION_DELIVERED("已寄出"),

    // 信息变更事件
    PATIENT_INFO_UPDATED("更新患者信息"),
    AGENT_INFO_SET("设置代办人信息"),
    AGENT_INFO_CLEARED("清除代办人信息"),
    AGENT_INFO_UPDATED("更新代办人信息"),
    MEDICAL_INFO_UPDATED("更新病历信息"),
    MAILING_INFO_SET("设置邮寄信息"),
    ADDRESS_INFO_UPDATED("更新地址信息"),
    
    // 附件相关事件
    ATTACHMENT_UPLOADED("上传附件"),
    ATTACHMENT_DELETED("删除附件");

    @Getter
    private final String description;

    MedicalRecordApplicationEventType(String description) {
        this.description = description;
    }

    public String getCode() {
        return name();
    }

    /**
     * 判断是否为状态变更事件
     */
    public boolean isStatusChangeEvent() {
        return this == APPLICATION_CREATED ||
                this == APPLICATION_SUBMITTED ||
                this == APPLICATION_APPROVED ||
                this == APPLICATION_REJECTED ||
                this == APPLICATION_RESUBMITTED ||
                this == APPLICATION_COMPLETED ||
                this == APPLICATION_DELETED ||
                this == APPLICATION_PAID ||
                this == APPLICATION_DELIVERED;
    }

    /**
     * 判断是否为重要事件（需要突出显示）
     */
    public boolean isImportantEvent() {
        return isStatusChangeEvent();
    }
}