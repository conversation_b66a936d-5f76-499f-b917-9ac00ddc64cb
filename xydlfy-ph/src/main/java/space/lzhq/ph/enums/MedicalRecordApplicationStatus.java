package space.lzhq.ph.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum MedicalRecordApplicationStatus {
    DRAFT("草稿（待提交）"),
    SUBMITTED("已提交（待审核）"),
    APPROVED("审核通过"),
    REJECTED("审核驳回（待整改）"),
    PAID("支付成功"),
    COMPLETED("已完成"),
    DELIVERED("邮寄完成");

    @Getter
    private final String description;

    MedicalRecordApplicationStatus(String description) {
        this.description = description;
    }

    public String getCode() {
        return name();
    }

    /**
     * 判断当前状态是否允许申请人或代理人编辑记录
     *
     * @return 如果状态为DRAFT（草稿）或REJECTED（审核驳回）时返回true，其他状态返回false
     */
    public boolean isEditableByApplicantOrAgent() {
        return this == DRAFT || this == REJECTED;
    }

    /**
     * 判断当前状态是否允许审批
     *
     * @return 如果状态为SUBMITTED（已提交）时返回true，其他状态返回false
     */
    public boolean canApprove() {
        return this == SUBMITTED;
    }

    /**
     * 判断当前状态是否允许删除
     *
     * @return 如果状态为DRAFT（草稿）时返回true，其他状态返回false
     */
    public boolean canDelete() {
        return this == DRAFT;
    }

    /**
     * 判断当前状态是否为待审批状态
     *
     * @return 如果状态为SUBMITTED（已提交）时返回true，其他状态返回false
     */
    public boolean isPendingApproval() {
        return this == SUBMITTED;
    }

    /**
     * 判断当前状态是否为待支付状态
     *
     * @return 如果状态为APPROVED（审核通过）时返回true，其他状态返回false
     */
    public boolean isPendingPayment() {
        return this == APPROVED;
    }

    /**
     * 判断当前状态是否为待邮寄状态
     *
     * @return 如果状态为PAID（支付成功）时返回true，其他状态返回false
     */
    public boolean isPendingShipping() {
        return this == PAID;
    }

}