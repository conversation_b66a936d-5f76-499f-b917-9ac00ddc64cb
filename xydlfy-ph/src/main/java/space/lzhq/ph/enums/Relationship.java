package space.lzhq.ph.enums;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

/**
 * 人员关系
 *
 * <AUTHOR>
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum Relationship {
    /**
     * 父亲
     */
    FATHER("父亲"),
    /**
     * 母亲
     */
    MOTHER("母亲"),

    /**
     * 丈夫
     */
    HUSBAND("丈夫"),

    /**
     * 妻子
     */
    WIFE("妻子"),

    /**
     * 儿子
     */
    SON("儿子"),

    /**
     * 女儿
     */
    DAUGHTER("女儿"),

    /**
     * 兄弟
     */
    BROTHER("兄弟"),

    /**
     * 姐妹
     */
    SISTER("姐妹"),

    /**
     * 朋友
     */
    FRIEND("朋友"),

    /**
     * 同事
     */
    COLLEAGUE("同事"),

    /**
     * 同学
     */
    CLASSMATE("同学"),

    /**
     * 其他
     */
    OTHER("其他");

    @Getter
    private final String description;

    Relationship(String description) {
        this.description = description;
    }

    public String getCode() {
        return name();
    }

}
