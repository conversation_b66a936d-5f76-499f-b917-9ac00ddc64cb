package space.lzhq.ph.mapper;

import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.Article;

import java.util.List;

/**
 * 文章Mapper接口
 *
 * <AUTHOR>
 */
@Repository
public interface ArticleMapper {
    /**
     * 查询文章
     *
     * @param id 文章ID
     * @return 文章
     */
    Article selectArticleById(Long id);

    /**
     * 查询文章列表
     *
     * @param article 文章
     * @return 文章集合
     */
    List<Article> selectArticleList(Article article);

    /**
     * 新增文章
     *
     * @param article 文章
     * @return 结果
     */
    int insertArticle(Article article);

    /**
     * 修改文章
     *
     * @param article 文章
     * @return 结果
     */
    int updateArticle(Article article);

    /**
     * 删除文章
     *
     * @param id 文章ID
     * @return 结果
     */
    int deleteArticleById(Long id);

    /**
     * 批量删除文章
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteArticleByIds(String[] ids);
}
