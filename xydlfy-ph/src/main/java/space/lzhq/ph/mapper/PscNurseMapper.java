package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.PscNurse;

import java.util.List;

/**
 * 护士Mapper接口
 *
 * <AUTHOR>
 * @date 2022-06-11
 */
@Repository
public interface PscNurseMapper {
    /**
     * 查询护士
     *
     * @param id 护士ID
     * @return 护士
     */
    PscNurse selectPscNurseById(Long id);

    /**
     * 查询护士
     *
     * @param employeeNo 工号
     * @return 护士
     */
    PscNurse selectPscNurseByEmployeeNo(@Param("employeeNo") String employeeNo);

    /**
     * 查询护士列表
     *
     * @param pscNurse 护士
     * @return 护士集合
     */
    List<PscNurse> selectPscNurseList(PscNurse pscNurse);

    /**
     * 检查工号是否唯一
     *
     * @param pscNurse 护士
     * @return 工号是否唯一
     */
    boolean isEmployeeNoUnique(PscNurse pscNurse);

    /**
     * 检查手机号是否唯一
     *
     * @param pscNurse 护士
     * @return 手机号是否唯一
     */
    boolean isMobileNoUnique(PscNurse pscNurse);

    /**
     * 新增护士
     *
     * @param pscNurse 护士
     * @return 结果
     */
    int insertPscNurse(PscNurse pscNurse);

    /**
     * 修改护士
     *
     * @param pscNurse 护士
     * @return 结果
     */
    int updatePscNurse(PscNurse pscNurse);

    /**
     * 删除护士
     *
     * @param id 护士ID
     * @return 结果
     */
    int deletePscNurseById(Long id);

    /**
     * 批量删除护士
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deletePscNurseByIds(String[] ids);
}
