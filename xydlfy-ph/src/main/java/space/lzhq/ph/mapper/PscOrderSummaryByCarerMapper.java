package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.PscOrderSummaryByCarer;
import space.lzhq.ph.domain.PscOrderSummaryByCarerAndDepartment;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface PscOrderSummaryByCarerMapper {

    /**
     * 汇总
     *
     * @param minConfirmTime 最小创建时间
     * @param maxConfirmTime 最大创建时间
     * @return 汇总结果
     */
    List<PscOrderSummaryByCarer> summary(
            @Param("minConfirmTime") LocalDateTime minConfirmTime,
            @Param("maxConfirmTime") LocalDateTime maxConfirmTime
    );

    List<PscOrderSummaryByCarerAndDepartment> summaryByCarerAndDepartment(
            @Param("minConfirmTime") LocalDateTime minConfirmTime,
            @Param("maxConfirmTime") LocalDateTime maxConfirmTime,
            @Param("carerEmployeeNo") String carerEmployeeNo
    );

}
