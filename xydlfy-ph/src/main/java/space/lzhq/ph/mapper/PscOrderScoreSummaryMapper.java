package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.PscOrderScoreSummary;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface PscOrderScoreSummaryMapper {

    /**
     * 汇总
     *
     * @param minConfirmTime 最小验收时间
     * @param maxConfirmTime 最大验收时间
     * @return 汇总结果
     */
    List<PscOrderScoreSummary> summary(
            @Param("minConfirmTime") LocalDateTime minConfirmTime,
            @Param("maxConfirmTime") LocalDateTime maxConfirmTime
    );

}
