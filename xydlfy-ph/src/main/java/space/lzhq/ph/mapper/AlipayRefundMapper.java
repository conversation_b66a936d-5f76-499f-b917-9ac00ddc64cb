package space.lzhq.ph.mapper;

import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.AlipayRefund;

import java.util.List;

/**
 * 支付宝退款记录Mapper接口
 *
 * <AUTHOR>
 * @date 2022-09-10
 */
@Repository
public interface AlipayRefundMapper {
    /**
     * 查询支付宝退款记录
     *
     * @param id 支付宝退款记录ID
     * @return 支付宝退款记录
     */
    AlipayRefund selectAlipayRefundById(Long id);

    /**
     * 查询支付宝退款记录列表
     *
     * @param alipayRefund 支付宝退款记录
     * @return 支付宝退款记录集合
     */
    List<AlipayRefund> selectAlipayRefundList(AlipayRefund alipayRefund);

    /**
     * 新增支付宝退款记录
     *
     * @param alipayRefund 支付宝退款记录
     * @return 结果
     */
    int insertAlipayRefund(AlipayRefund alipayRefund);

    /**
     * 修改支付宝退款记录
     *
     * @param alipayRefund 支付宝退款记录
     * @return 结果
     */
    int updateAlipayRefund(AlipayRefund alipayRefund);

    /**
     * 删除支付宝退款记录
     *
     * @param id 支付宝退款记录ID
     * @return 结果
     */
    int deleteAlipayRefundById(Long id);

    /**
     * 批量删除支付宝退款记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteAlipayRefundByIds(String[] ids);
}
