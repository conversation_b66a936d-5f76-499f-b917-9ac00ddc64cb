package space.lzhq.ph.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import space.lzhq.ph.domain.PacsReportLink;
import space.lzhq.ph.domain.PacsReport;

import java.util.List;

@Mapper
public interface PacsReportMapper extends BaseMapper<PacsReport> {

    /**
     * 根据申请单号查询报告链接
     *
     * @param applicationNo 申请单号
     * @return 报告链接列表
     */
    @Select("SELECT DISTINCT 报告链接 FROM medexmemrstemp.dbo.Report_ZY WHERE 申请单号 = #{applicationNo}")
    List<String> selectReportLinksByApplicationNo(@Param("applicationNo") String applicationNo);

    /**
     * 根据多个申请单号查询申请单号和报告链接
     *
     * @param applicationNos 申请单号列表
     * @return 申请单号和报告链接结果列表
     */
    @Select("<script>" +
            "SELECT DISTINCT 申请单号 as applicationNo, 报告链接 as reportLink " +
            "FROM medexmemrstemp.dbo.Report_ZY " +
            "WHERE 申请单号 IN " +
            "<foreach collection='applicationNos' item='applicationNo' open='(' separator=',' close=')'>" +
            "#{applicationNo}" +
            "</foreach>" +
            "</script>")
    List<PacsReportLink> selectReportsByApplicationNos(@Param("applicationNos") List<String> applicationNos);

}
