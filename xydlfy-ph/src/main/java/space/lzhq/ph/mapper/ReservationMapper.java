package space.lzhq.ph.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import space.lzhq.ph.domain.Reservation;

@Mapper
public interface ReservationMapper extends BaseMapper<Reservation> {

    @Select( "SELECT * FROM ph_reservation WHERE reservation_number = #{reservationNumber} AND operation_type = 0 LIMIT 1")
    Reservation selectOneByReservationNumber(@Param("reservationNumber") String reservationNumber);

}
