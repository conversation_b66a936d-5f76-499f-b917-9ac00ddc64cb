package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import space.lzhq.ph.domain.WikiArticle;

import java.util.List;

/**
 * 文章Mapper接口
 *
 * <AUTHOR>
 */
@Repository
public interface WikiArticleMapper {
    /**
     * 查询文章
     *
     * @param id 文章ID
     * @return 文章
     */
    WikiArticle selectWikiArticleById(Long id);

    /**
     * 查询文章列表
     *
     * @param article 文章
     * @return 文章集合
     */
    List<WikiArticle> selectWikiArticleList(WikiArticle article);

    List<WikiArticle> selectListByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 新增文章
     *
     * @param article 文章
     * @return 结果
     */
    int insertWikiArticle(WikiArticle article);

    /**
     * 修改文章
     *
     * @param article 文章
     * @return 结果
     */
    int updateWikiArticle(WikiArticle article);

    int updateCategoryName(@Param("categoryId") Long categoryId, @Param("categoryName") String categoryName);

    /**
     * 删除文章
     *
     * @param id 文章ID
     * @return 结果
     */
    int deleteWikiArticleById(Long id);

    /**
     * 批量删除文章
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteWikiArticleByIds(String[] ids);

    Long countByCategoryId(Long categoryId);

}
