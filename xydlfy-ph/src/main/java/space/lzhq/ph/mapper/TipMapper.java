package space.lzhq.ph.mapper;

import org.apache.ibatis.annotations.Param;
import space.lzhq.ph.domain.Tip;

import java.util.List;

/**
 * 提示信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
public interface TipMapper {
    /**
     * 查询提示信息
     *
     * @param id 提示信息主键
     * @return 提示信息
     */
    public Tip selectTipById(Long id);

    /**
     * 查询提示信息列表
     *
     * @param tip 提示信息
     * @return 提示信息集合
     */
    public List<Tip> selectTipList(Tip tip);

    public List<Tip> selectByCodeList(@Param("codeList") List<String> codeList);

    /**
     * 新增提示信息
     *
     * @param tip 提示信息
     * @return 结果
     */
    public int insertTip(Tip tip);

    /**
     * 修改提示信息
     *
     * @param tip 提示信息
     * @return 结果
     */
    public int updateTip(Tip tip);

    /**
     * 删除提示信息
     *
     * @param id 提示信息主键
     * @return 结果
     */
    public int deleteTipById(Long id);

    /**
     * 批量删除提示信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTipByIds(String[] ids);
}
