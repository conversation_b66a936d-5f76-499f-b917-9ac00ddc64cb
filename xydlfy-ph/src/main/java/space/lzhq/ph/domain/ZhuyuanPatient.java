package space.lzhq.ph.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.util.Date;

/**
 * 住院患者对象 ph_zhuyuan_patient
 *
 * <AUTHOR>
 * @date 2023-07-31
 */
public class ZhuyuanPatient extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long id;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCardNo;

    /**
     * 患者编号
     */
    @Excel(name = "患者编号")
    private String patientNo;

    /**
     * 住院号
     * HIS字段为：admissionNo
     */
    @Excel(name = "住院号")
    private String zhuyuanNo;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 性别
     */
    @Excel(name = "性别")
    private Integer gender;

    /**
     * 科室编码
     */
    @Excel(name = "科室编码")
    private String departmentCode;

    /**
     * 科室名称
     */
    @Excel(name = "科室名称")
    private String departmentName;

    /**
     * 床号
     */
    @Excel(name = "床号")
    private String bedNo;

    /**
     * 住院时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "住院时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date comeTime;

    /**
     * 离院时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "离院时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date leaveTime;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String state;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String openId;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String unionId;


    /**
     * 住院号码
     */
    @Excel(name = "住院号码")
    private String admissionNumber;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setPatientNo(String patientNo) {
        this.patientNo = patientNo;
    }

    public String getPatientNo() {
        return patientNo;
    }

    public void setZhuyuanNo(String zhuyuanNo) {
        this.zhuyuanNo = zhuyuanNo;
    }

    public String getZhuyuanNo() {
        return zhuyuanNo;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public Integer getGender() {
        return gender;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setBedNo(String bedNo) {
        this.bedNo = bedNo;
    }

    public String getBedNo() {
        return bedNo;
    }

    public void setComeTime(Date comeTime) {
        this.comeTime = comeTime;
    }

    public Date getComeTime() {
        return comeTime;
    }

    public void setLeaveTime(Date leaveTime) {
        this.leaveTime = leaveTime;
    }

    public Date getLeaveTime() {
        return leaveTime;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getState() {
        return state;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getUnionId() {
        return unionId;
    }

    public String getAdmissionNumber() {
        return admissionNumber;
    }

    public void setAdmissionNumber(String admissionNumber) {
        this.admissionNumber = admissionNumber;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("idCardNo", getIdCardNo())
                .append("patientNo", getPatientNo())
                .append("zhuyuanNo", getZhuyuanNo())
                .append("name", getName())
                .append("gender", getGender())
                .append("departmentCode", getDepartmentCode())
                .append("departmentName", getDepartmentName())
                .append("bedNo", getBedNo())
                .append("comeTime", getComeTime())
                .append("leaveTime", getLeaveTime())
                .append("state", getState())
                .append("openId", getOpenId())
                .append("unionId", getUnionId())
                .append("admissionNumber", getAdmissionNumber())
                .toString();
    }
}
