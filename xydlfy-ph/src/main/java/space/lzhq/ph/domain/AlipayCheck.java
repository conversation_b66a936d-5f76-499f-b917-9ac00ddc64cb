package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 支付宝对账对象 alipay_check
 *
 * <AUTHOR>
 * @date 2023-02-04
 */
public class AlipayCheck extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日期
     */
    private Long id;

    /**
     * 支付宝支付总金额
     */
    @Excel(name = "支付宝支付总金额")
    private BigDecimal alipayPayAmount;

    /**
     * HIS充值总金额
     */
    @Excel(name = "HIS充值总金额")
    private BigDecimal hisPayAmount;

    /**
     * 支付差额
     */
    @Excel(name = "支付差额")
    private BigDecimal diffPayAmount;

    /**
     * 支付平账？
     */
    @Excel(name = "支付平账？")
    private Integer payBalanced;

    /**
     * 支付宝退款总金额
     */
    @Excel(name = "支付宝退款总金额")
    private BigDecimal alipayRefundAmount;

    /**
     * HIS退款总金额
     */
    @Excel(name = "HIS退款总金额")
    private BigDecimal hisRefundAmount;

    /**
     * 退款差额
     */
    @Excel(name = "退款差额")
    private BigDecimal diffRefundAmount;

    /**
     * 退款平账？
     */
    @Excel(name = "退款平账？")
    private Integer refundBalanced;

    /**
     * 支付宝净流入
     */
    @Excel(name = "支付宝净流入")
    private BigDecimal alipayNetIn;

    /**
     * HIS净流入
     */
    @Excel(name = "HIS净流入")
    private BigDecimal hisNetIn;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setAlipayPayAmount(BigDecimal alipayPayAmount) {
        this.alipayPayAmount = alipayPayAmount;
    }

    public BigDecimal getAlipayPayAmount() {
        return alipayPayAmount;
    }

    public void setHisPayAmount(BigDecimal hisPayAmount) {
        this.hisPayAmount = hisPayAmount;
    }

    public BigDecimal getHisPayAmount() {
        return hisPayAmount;
    }

    public void setDiffPayAmount(BigDecimal diffPayAmount) {
        this.diffPayAmount = diffPayAmount;
    }

    public BigDecimal getDiffPayAmount() {
        return diffPayAmount;
    }

    public void setPayBalanced(Integer payBalanced) {
        this.payBalanced = payBalanced;
    }

    public Integer getPayBalanced() {
        return payBalanced;
    }

    public void setAlipayRefundAmount(BigDecimal alipayRefundAmount) {
        this.alipayRefundAmount = alipayRefundAmount;
    }

    public BigDecimal getAlipayRefundAmount() {
        return alipayRefundAmount;
    }

    public void setHisRefundAmount(BigDecimal hisRefundAmount) {
        this.hisRefundAmount = hisRefundAmount;
    }

    public BigDecimal getHisRefundAmount() {
        return hisRefundAmount;
    }

    public void setDiffRefundAmount(BigDecimal diffRefundAmount) {
        this.diffRefundAmount = diffRefundAmount;
    }

    public BigDecimal getDiffRefundAmount() {
        return diffRefundAmount;
    }

    public void setRefundBalanced(Integer refundBalanced) {
        this.refundBalanced = refundBalanced;
    }

    public Integer getRefundBalanced() {
        return refundBalanced;
    }

    public void setAlipayNetIn(BigDecimal alipayNetIn) {
        this.alipayNetIn = alipayNetIn;
    }

    public BigDecimal getAlipayNetIn() {
        return alipayNetIn;
    }

    public void setHisNetIn(BigDecimal hisNetIn) {
        this.hisNetIn = hisNetIn;
    }

    public BigDecimal getHisNetIn() {
        return hisNetIn;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("alipayPayAmount", getAlipayPayAmount())
                .append("hisPayAmount", getHisPayAmount())
                .append("diffPayAmount", getDiffPayAmount())
                .append("payBalanced", getPayBalanced())
                .append("alipayRefundAmount", getAlipayRefundAmount())
                .append("hisRefundAmount", getHisRefundAmount())
                .append("diffRefundAmount", getDiffRefundAmount())
                .append("refundBalanced", getRefundBalanced())
                .append("alipayNetIn", getAlipayNetIn())
                .append("hisNetIn", getHisNetIn())
                .append("remark", getRemark())
                .toString();
    }
}
