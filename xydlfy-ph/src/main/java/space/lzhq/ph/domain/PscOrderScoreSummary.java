package space.lzhq.ph.domain;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.write.style.ColumnWidth;

import java.io.Serial;
import java.io.Serializable;
import java.util.StringJoiner;

public class PscOrderScoreSummary implements Serializable {
    @Serial
    private static final long serialVersionUID = -8091171274047649385L;

    @ExcelProperty(value = "工号")
    @ColumnWidth(15)
    private String carerEmployeeNo;

    @ExcelProperty(value = "姓名")
    @ColumnWidth(15)
    private String carerName;

    @ExcelProperty(value = "手机号")
    @ColumnWidth(15)
    private String carerMobile;

    @ExcelProperty(value = {"评价人次", "非常不满意"})
    private Integer score1;

    @ExcelProperty(value = {"评价人次", "不满意"})
    private Integer score2;

    @ExcelProperty(value = {"评价人次", "一般"})
    private Integer score3;

    @ExcelProperty(value = {"评价人次", "满意"})
    private Integer score4;

    @ExcelProperty(value = {"评价人次", "非常满意"})
    private Integer score5;

    @ExcelProperty(value = "合计")
    private Integer total;

    public String getCarerEmployeeNo() {
        return carerEmployeeNo;
    }

    public void setCarerEmployeeNo(String carerEmployeeNo) {
        this.carerEmployeeNo = carerEmployeeNo;
    }

    public String getCarerName() {
        return carerName;
    }

    public void setCarerName(String carerName) {
        this.carerName = carerName;
    }

    public String getCarerMobile() {
        return carerMobile;
    }

    public void setCarerMobile(String carerMobile) {
        this.carerMobile = carerMobile;
    }

    public Integer getScore1() {
        return score1;
    }

    public void setScore1(Integer score1) {
        this.score1 = score1;
    }

    public Integer getScore2() {
        return score2;
    }

    public void setScore2(Integer score2) {
        this.score2 = score2;
    }

    public Integer getScore3() {
        return score3;
    }

    public void setScore3(Integer score3) {
        this.score3 = score3;
    }

    public Integer getScore4() {
        return score4;
    }

    public void setScore4(Integer score4) {
        this.score4 = score4;
    }

    public Integer getScore5() {
        return score5;
    }

    public void setScore5(Integer score5) {
        this.score5 = score5;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", PscOrderScoreSummary.class.getSimpleName() + "[", "]")
                .add("carerEmployeeNo='" + carerEmployeeNo + "'")
                .add("carerName='" + carerName + "'")
                .add("carerMobile='" + carerMobile + "'")
                .add("score1=" + score1)
                .add("score2=" + score2)
                .add("score3=" + score3)
                .add("score4=" + score4)
                .add("score5=" + score5)
                .add("total=" + total)
                .toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PscOrderScoreSummary)) return false;

        PscOrderScoreSummary that = (PscOrderScoreSummary) o;

        if (!getCarerEmployeeNo().equals(that.getCarerEmployeeNo())) return false;
        if (!getCarerName().equals(that.getCarerName())) return false;
        if (!getCarerMobile().equals(that.getCarerMobile())) return false;
        if (!getScore1().equals(that.getScore1())) return false;
        if (!getScore2().equals(that.getScore2())) return false;
        if (!getScore3().equals(that.getScore3())) return false;
        if (!getScore4().equals(that.getScore4())) return false;
        if (!getScore5().equals(that.getScore5())) return false;
        if (!getTotal().equals(that.getTotal())) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = getCarerEmployeeNo().hashCode();
        result = 31 * result + getCarerName().hashCode();
        result = 31 * result + getCarerMobile().hashCode();
        result = 31 * result + getScore1().hashCode();
        result = 31 * result + getScore2().hashCode();
        result = 31 * result + getScore3().hashCode();
        result = 31 * result + getScore4().hashCode();
        result = 31 * result + getScore5().hashCode();
        result = 31 * result + getTotal().hashCode();
        return result;
    }
}
