package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import space.lzhq.ph.model.MedicalRecordMailingStatus;

import java.io.Serial;
import java.util.Date;

/**
 * 病历邮寄对象 ph_medical_record_mailing
 *
 * <AUTHOR>
 * @date 2023-11-11
 */
@TableName(value = "ph_medical_record_mailing", excludeProperty = {"searchValue", "createBy", "updateBy", "remark", "params"})
public class MedicalRecordMailing extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 已申请
     */
    public static final int STATUS_APPLIED = 1;

    /**
     * 已确认病历信息
     */
    public static final int STATUS_CONFIRMED = 2;

    /**
     * 已支付
     */
    public static final int STATUS_PAID = 3;

    /**
     * 已邮寄
     */
    public static final int STATUS_MAILED = 4;

    /**
     * 关闭订单
     */
    public static final int STATUS_CLOSED = 9;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 患者号
     */
    @Excel(name = "患者号")
    private String patientNo;

    /**
     * 就诊卡号
     */
    @Excel(name = "就诊卡号")
    private String jzCardNo;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    private String zhuyuanNo;

    /**
     * 患者姓名
     */
    @Excel(name = "患者姓名")
    private String name;

    /**
     * 患者手机号
     */
    @Excel(name = "患者手机号")
    private String mobile;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCardNo;

    /**
     * 患者openid
     */
    @Excel(name = "患者openid")
    private String openid;

    /**
     * 收件地址
     */
    @Excel(name = "收件地址")
    private String address;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private Integer status;

    /**
     * 病历页数
     */
    @Excel(name = "病历页数")
    private Long medicalRecordPages;

    /**
     * 复印费用
     */
    @Excel(name = "复印费用")
    private Long copyingFee;

    /**
     * 快递费用
     */
    @Excel(name = "快递费用")
    private Long expressFee;

    /**
     * 掌医支付单号
     */
    @Excel(name = "掌医支付单号")
    private String zyPayNo;

    /**
     * 微信支付单号
     */
    @Excel(name = "微信支付单号")
    private String wxPayNo;

    /**
     * 快递单号
     */
    @Excel(name = "快递单号")
    private String courierNumber;

    /**
     * 订单关闭原因
     */
    @Excel(name = "订单关闭原因")
    private String shutdownReason;

    /**
     * 操作员ID
     */
    private Long operatorId;

    /**
     * 操作员名称
     */
    @Excel(name = "操作员名称")
    private String operatorName;

    /**
     * 确认病历时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confirmedTime;

    /**
     * 邮寄病历时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date mailedTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setPatientNo(String patientNo) {
        this.patientNo = patientNo;
    }

    public String getPatientNo() {
        return patientNo;
    }

    public void setJzCardNo(String jzCardNo) {
        this.jzCardNo = jzCardNo;
    }

    public String getJzCardNo() {
        return jzCardNo;
    }

    public void setZhuyuanNo(String zhuyuanNo) {
        this.zhuyuanNo = zhuyuanNo;
    }

    public String getZhuyuanNo() {
        return zhuyuanNo;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getOpenid() {
        return openid;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setMedicalRecordPages(Long medicalRecordPages) {
        this.medicalRecordPages = medicalRecordPages;
    }

    public Long getMedicalRecordPages() {
        return medicalRecordPages;
    }

    public void setCopyingFee(Long copyingFee) {
        this.copyingFee = copyingFee;
    }

    public Long getCopyingFee() {
        return copyingFee;
    }

    public void setExpressFee(Long expressFee) {
        this.expressFee = expressFee;
    }

    public Long getExpressFee() {
        return expressFee;
    }

    public void setZyPayNo(String zyPayNo) {
        this.zyPayNo = zyPayNo;
    }

    public String getZyPayNo() {
        return zyPayNo;
    }

    public void setWxPayNo(String wxPayNo) {
        this.wxPayNo = wxPayNo;
    }

    public String getWxPayNo() {
        return wxPayNo;
    }

    public void setCourierNumber(String courierNumber) {
        this.courierNumber = courierNumber;
    }

    public String getCourierNumber() {
        return courierNumber;
    }

    public void setShutdownReason(String shutdownReason) {
        this.shutdownReason = shutdownReason;
    }

    public String getShutdownReason() {
        return shutdownReason;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public Date getConfirmedTime() {
        return confirmedTime;
    }

    public void setConfirmedTime(Date confirmedTime) {
        this.confirmedTime = confirmedTime;
    }

    public Date getMailedTime() {
        return mailedTime;
    }

    public void setMailedTime(Date mailedTime) {
        this.mailedTime = mailedTime;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Long getTotalFee() {
        if (copyingFee == null || expressFee == null) {
            return null;
        } else {
            return copyingFee + expressFee;
        }
    }

    public String getStatusDesc() {
        if (status == null) return null;
        return MedicalRecordMailingStatus.fromValue(status).getDescription();
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("patientNo", getPatientNo())
                .append("jzCardNo", getJzCardNo())
                .append("zhuyuanNo", getZhuyuanNo())
                .append("name", getName())
                .append("mobile", getMobile())
                .append("idCardNo", getIdCardNo())
                .append("openid", getOpenid())
                .append("status", getStatus())
                .append("medicalRecordPages", getMedicalRecordPages())
                .append("copyingFee", getCopyingFee())
                .append("expressFee", getExpressFee())
                .append("zyPayNo", getZyPayNo())
                .append("wxPayNo", getWxPayNo())
                .append("courierNumber", getCourierNumber())
                .append("shutdownReason", getShutdownReason())
                .append("operatorId", getOperatorId())
                .append("operatorName", getOperatorName())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
