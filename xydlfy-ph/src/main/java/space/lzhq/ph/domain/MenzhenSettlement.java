package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.springframework.format.annotation.DateTimeFormat;

import lombok.Builder;
import lombok.Data;

/**
 * 门诊结算
 */
@Data
@Builder
@TableName("ph_menzhen_settlement")
public class MenzhenSettlement implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 患者ID
     */
    private String patientId;

    /**
     * 就诊卡号
     */
    private String cardNo;

    /**
     * 结算单号
     */
    private String settlementNos;

    /**
     * 结算金额
     */
    private BigDecimal amount;

    /**
     * 掌医订单号
     */
    private String zyOrderNo;

    /**
     * HIS订单号
     */
    private String hisOrderNo;

    /**
     * 结算是否成功
     */
    private Boolean success;

    /**
     * 结算状态消息
     */
    private String statusMessage;

}
