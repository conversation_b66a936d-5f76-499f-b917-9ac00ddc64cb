package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.mospital.bsoft.RegisterType;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "ph_reservation")
public class Reservation implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 预约
     */
    @TableField(exist = false)
    public static final int OPERATION_TYPE_RESERVE = 0;

    /**
     * 取消预约
     */
    @TableField(exist = false)
    public static final int OPERATION_TYPE_CANCEL = 1;

    /**
     * 操作成功
     */
    @TableField(exist = false)
    public static final int OPERATION_RESULT_SUCCESS = 0;

    /**
     * 操作失败
     */
    @TableField(exist = false)
    public static final int OPERATION_RESULT_FAIL = 1;


    /**
     * 预约成功
     */
    @TableField(exist = false)
    public static final Integer RESERVATION_STATUS_INIT = 0;

    /**
     * 取消预约
     */
    @TableField(exist = false)
    public static final Integer RESERVATION_STATUS_CANCELED = 9;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 操作类型
     */
    @Excel(name = "操作类型", dictType = "reservation_operation_type")
    private Integer operationType;

    /**
     * 操作时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operationTime;

    private String openid;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCardNo;

    /**
     * 病人ID
     */
    @Excel(name = "病人ID")
    private String patientId;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String mobile;

    /**
     * 就诊日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate jzDate;

    /**
     * 就诊时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime jzTime;

    /**
     * 挂号类型
     */
    @Excel(name = "挂号类型", dictType = "reservation_type")
    private Integer reservationType;

    /**
     * 科室编码
     */
    @Excel(name = "科室编码")
    private String departmentCode;

    /**
     * 科室名称
     */
    @Excel(name = "科室名称")
    private String departmentName;

    /**
     * 科室位置
     */
    @Excel(name = "科室位置")
    private String departmentLocation;

    /**
     * 医生编码
     */
    @Excel(name = "医生编码")
    private String doctorCode;

    /**
     * 医生姓名
     */
    @Excel(name = "医生姓名")
    private String doctorName;

    /**
     * 顺序号
     */
    @Excel(name = "顺序号")
    private String visitNumber;

    /**
     * 预约号
     */
    @Excel(name = "预约号")
    private String reservationNumber;

    /**
     * 操作结果
     */
    @Excel(name = "操作结果")
    private Integer operationResult;

    /**
     * 操作描述
     */
    @Excel(name = "操作描述")
    private String operationDesc;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private Integer status;

    /**
     * 支付宝医疗订单ID
     */
    private String alipayHospitalOrderId;

    public Reservation(Patient patient) {
        this.operationType = OPERATION_TYPE_RESERVE;
        this.operationTime = LocalDateTime.now().withNano(0);
        this.openid = patient.getOpenId();
        this.idCardNo = patient.getIdCardNo();
        this.patientId = patient.getPatientNo();
        this.name = patient.getName();
        this.mobile = patient.getMobile();
    }

    @JsonGetter("reservationTypeDescription")
    public String getReservationTypeDescription() {
        RegisterType registerType = RegisterType.fromCode(this.reservationType);
        return registerType == null ? "" : registerType.getDesc();
    }

    public Reservation toCancel() {
        Reservation cancel = new Reservation();
        cancel.setOperationType(OPERATION_TYPE_CANCEL);
        cancel.setOperationTime(LocalDateTime.now().withNano(0));
        cancel.setOpenid(this.openid);
        cancel.setIdCardNo(this.idCardNo);
        cancel.setPatientId(this.patientId);
        cancel.setName(this.name);
        cancel.setMobile(this.mobile);
        cancel.setJzDate(this.jzDate);
        cancel.setJzTime(this.jzTime);
        cancel.setReservationType(this.reservationType);
        cancel.setDepartmentCode(this.departmentCode);
        cancel.setDepartmentName(this.departmentName);
        cancel.setDoctorCode(this.doctorCode);
        cancel.setDoctorName(this.doctorName);
        cancel.setDepartmentLocation(this.departmentLocation);
        cancel.setVisitNumber(this.visitNumber);
        cancel.setReservationNumber(this.reservationNumber);
        cancel.setStatus(RESERVATION_STATUS_CANCELED);
        return cancel;
    }

}
