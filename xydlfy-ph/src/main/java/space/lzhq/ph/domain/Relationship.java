package space.lzhq.ph.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.enums.BaseEnum;

/**
 * 人员关系
 *
 * <AUTHOR>
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum Relationship implements BaseEnum {
    /**
     * 父亲
     */
    FATHER(1, "父亲"),
    /**
     * 母亲
     */
    MOTHER(2, "母亲"),

    /**
     * 丈夫
     */
    HUSBAND(3, "丈夫"),

    /**
     * 妻子
     */
    WIFE(4, "妻子"),

    /**
     * 儿子
     */
    SON(5, "儿子"),

    /**
     * 女儿
     */
    DAUGHTER(6, "女儿"),

    /**
     * 兄弟
     */
    BROTHER(7, "兄弟"),

    /**
     * 姐妹
     */
    SISTER(8, "姐妹"),

    /**
     * 朋友
     */
    FRIEND(9, "朋友"),

    /**
     * 同事
     */
    COLLEAGUE(10, "同事"),

    /**
     * 同学
     */
    CLASSMATE(11, "同学"),

    /**
     * 其他
     */
    OTHER(99, "其他");

    private Integer code;
    private String description;

    Relationship(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
