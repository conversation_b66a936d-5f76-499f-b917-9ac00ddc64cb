package space.lzhq.ph.domain;

import lombok.*;
import org.mospital.common.PatientDataToken;
import org.mospital.jackson.JacksonKit;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatientInfoToken {

    @NonNull
    private String patientId;

    @NonNull
    private String patientIdCardNo;

    @NonNull
    private String patientCardNo;

    @NonNull
    private String patientName;

    @NonNull
    private String patientMobile;

    private String getDataId() {
        return JacksonKit.INSTANCE.writeValueAsString(this);
    }

    public String generateToken() {
        PatientDataToken token = new PatientDataToken(getDataId(), patientId);
        return token.generateToken();
    }

    public static PatientInfoToken parseToken(String tokenString) {
        PatientDataToken token = PatientDataToken.Companion.parse(tokenString);
        if (token == null) {
            return null;
        }

        return JacksonKit.INSTANCE.readValue(token.getDataId(), PatientInfoToken.class);
    }

}
