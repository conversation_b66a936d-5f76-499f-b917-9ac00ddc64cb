package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import space.lzhq.ph.enums.AttachmentMasterType;
import space.lzhq.ph.enums.AttachmentType;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName(value = "attachment")
public class Attachment implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;


    /**
     * 附件ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 关联的主表ID
     */
    private String masterId;

    /**
     * 关联的主表类型
     */
    private AttachmentMasterType masterType;

    /**
     * 用户OpenID
     */
    private String openId;

    /**
     * 附件类型
     */
    private AttachmentType type;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件存储路径
     */
    private String fileUrl;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
