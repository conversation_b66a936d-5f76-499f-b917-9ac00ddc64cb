package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import space.lzhq.ph.common.ServiceType;

import java.io.Serial;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class WxRefund extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String type;

    /**
     * openid
     */
    @Excel(name = "openid")
    private String openid;

    /**
     * 商户号
     */
    private String mchId;

    @Excel(name = "患者标识")
    private String patientNo;

    /**
     * 就诊卡号
     */
    @Excel(name = "就诊卡号")
    private String jzCardNo;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    private String zhuyuanNo;

    /**
     * 充值订单金额
     */
    @Excel(name = "充值订单金额")
    private Long totalAmount;

    /**
     * 订单金额
     */
    @Excel(name = "订单金额")
    private Long amount;

    /**
     * 掌医充值订单号
     */
    @Excel(name = "掌医充值订单号")
    private String zyPayNo;

    /**
     * 微信充值订单号
     */
    @Excel(name = "微信充值订单号")
    private String wxPayNo;

    /**
     * 掌医退款订单号
     */
    @Excel(name = "掌医退款订单号")
    private String zyRefundNo;

    /**
     * 微信退款订单号
     */
    @Excel(name = "微信退款订单号")
    private String wxRefundNo;

    /**
     * HIS交易状态
     */
    @Excel(name = "HIS交易状态")
    private String hisTradeStatus;

    /**
     * 微信交易状态
     */
    @Excel(name = "微信交易状态")
    private String wxTradeStatus;

    /**
     * 人工退款状态
     */
    @Excel(name = "人工退款状态")
    private Integer manualRefundState;

    public boolean isMenzhen() {
        return ServiceType.MZ.name().equals(this.type);
    }

    public boolean isZhuyuan() {
        return ServiceType.ZY.name().equals(this.type);
    }

    public boolean isZhenjian() {
        return ServiceType.ZJ.name().equals(this.type);
    }
}
