package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 材料对象 ph_material
 *
 * <AUTHOR>
 * @date 2022-05-05
 */
public class Material extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 编码
     */
    @Excel(name = "编码")
    private String no;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 拼音码
     */
    @Excel(name = "拼音码")
    private String pinyinCode;

    /**
     * 类型编码
     */
    private String typeCode;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String typeName;

    /**
     * 单位
     */
    @Excel(name = "单位")
    private String unit;

    /**
     * 价格
     */
    @Excel(name = "价格")
    private BigDecimal price;

    public Material() {
        super();
    }

    public Material(org.mospital.bsoft.Material that) {
        this.no = that.getId();
        this.name = that.getName();
        this.pinyinCode = that.getPinyinCode();
        this.typeCode = that.getTypeCode();
        this.typeName = that.getTypeName();
        this.unit = that.getUnit();
        this.price = that.getPrice();
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setPinyinCode(String pinyinCode) {
        this.pinyinCode = pinyinCode;
    }

    public String getPinyinCode() {
        return pinyinCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnit() {
        return unit;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPrice() {
        return price;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).append("id", getId()).append("no", getNo()).append("name",
                getName()).append("pinyinCode", getPinyinCode()).append("typeCode", getTypeCode()).append("typeName",
                getTypeName()).append("unit", getUnit()).append("price", getPrice()).toString();
    }
}
