package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.TreeEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;

/**
 * 医生分类对象 doctor_category
 *
 * <AUTHOR>
 * @date 2023-07-15
 */
public class DoctorCategory extends TreeEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 排序号
     */
    @Excel(name = "排序号")
    private Long sortNo;

    /**
     * 分类代码
     */
    @Excel(name = "分类代码")
    private String categoryCode;

    /**
     * 名称全拼
     */
    private String namePinyin;

    /**
     * 名称简拼
     */
    private String namePinyinFirst;

    /**
     * 是否亚专业
     */
    @Excel(name = "是否亚专业")
    private Integer isSubspecialty;

    /**
     * 位置
     */
    private String address;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setSortNo(Long sortNo) {
        this.sortNo = sortNo;
    }

    public Long getSortNo() {
        return sortNo;
    }

    public void setCategoryCode(String categoryCode) {
        this.categoryCode = categoryCode;
    }

    public String getCategoryCode() {
        return categoryCode;
    }

    public void setNamePinyin(String namePinyin) {
        this.namePinyin = namePinyin;
    }

    public String getNamePinyin() {
        return namePinyin;
    }

    public void setNamePinyinFirst(String namePinyinFirst) {
        this.namePinyinFirst = namePinyinFirst;
    }

    public String getNamePinyinFirst() {
        return namePinyinFirst;
    }

    public void setIsSubspecialty(Integer isSubspecialty) {
        this.isSubspecialty = isSubspecialty;
    }

    public Integer getIsSubspecialty() {
        return isSubspecialty;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("sortNo", getSortNo())
                .append("parentId", getParentId())
                .append("categoryCode", getCategoryCode())
                .append("namePinyin", getNamePinyin())
                .append("namePinyinFirst", getNamePinyinFirst())
                .append("remark", getRemark())
                .append("isSubspecialty", getIsSubspecialty())
                .toString();
    }
}
