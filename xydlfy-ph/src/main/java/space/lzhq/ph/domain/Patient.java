package space.lzhq.ph.domain;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.*;
import org.dromara.hutool.core.data.IdcardUtil;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.dromara.hutool.core.text.StrValidator;
import org.mospital.bsoft.PatientInfo;
import org.mospital.common.StringKit;
import space.lzhq.ph.common.ClientType;
import space.lzhq.ph.ext.HisExt;

import java.io.Serial;
import java.util.Date;

/**
 * 就诊人对象 ph_patient
 *
 * @date 2020-05-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class Patient extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 客户端
     */
    private Integer clientType;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String idCardNo;

    /**
     * 患者索引号
     */
    @Excel(name = "患者索引号")
    private String patientNo;

    /**
     * 就诊卡号
     */
    @Excel(name = "就诊卡号")
    private String jzCardNo;

    /**
     * 门诊号
     */
    @Excel(name = "门诊号")
    private String menzhenNo;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    private String zhuyuanNo;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 性别：0=男，1=女
     */
    @Excel(name = "性别", readConverterExp = "0=男,1=女,2=未知")
    private Integer gender;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String mobile;

    /**
     * $column.columnComment
     */
    @Excel(name = "openid")
    private String openId;

    /**
     * $column.columnComment
     */
    @Excel(name = "unionid")
    private String unionId;

    /**
     * 是否默认卡
     */
    @Excel(name = "是否默认卡", readConverterExp = "1=是,0=否")
    private Integer active;

    /**
     * 电子健康卡主索引号
     */
    @Excel(name = "电子健康卡主索引号")
    private String empi;

    /**
     * 电子健康卡号
     */
    @Excel(name = "电子健康卡号")
    private String erhcCardNo;

    /**
     * 入院时间
     */
    @Excel(name = "入院时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date ruyuanTime;

    /**
     * 出院时间
     */
    @Excel(name = "出院时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date chuyuanTime;

    /**
     * 监护人身份证号
     */
    @Excel(name = "监护人身份证号")
    private String guarderIDCard;


    /**
     * 监护人手机号
     */
    @Excel(name = "监护人手机号")
    private String guarderPhoneNumber;

    /**
     * 监护人姓名
     */
    @Excel(name = "监护人姓名")
    private String guarderName;


    /**
     * 门诊余额
     */
    private Double menzhenBalance;

    /**
     * 住院余额
     */
    private Double zhuyuanBalance;

    public Patient(Session session, PatientInfo menzhenPatient, String mobile) {
        this.clientType = session.getClientType();
        this.idCardNo = menzhenPatient.getPatientIdCard();
        this.patientNo = menzhenPatient.getPatientId();
        this.jzCardNo = menzhenPatient.getCardNo();
        this.menzhenNo = menzhenPatient.getOutpatientNumber();
        this.zhuyuanNo = "";
        this.name = menzhenPatient.getPatientName().trim();
        this.gender = "1".equals(menzhenPatient.getPatientSex()) ? 0 : 1;
        this.mobile = CharSequenceUtil.trim(CharSequenceUtil.defaultIfNull(menzhenPatient.getPatientPhone(), mobile));
        this.openId = session.getOpenId();
        this.unionId = session.getUnionId();
        this.active = 0;
        this.empi = "";
        this.erhcCardNo = "";
        this.menzhenBalance = null;
        this.zhuyuanBalance = null;
    }

    public Patient(Session session, PatientInfo menzhenPatient, String mobile, String guarderPhoneNumber, String guarderName, String guarderIDCard) {
        this.clientType = session.getClientType();
        this.idCardNo = menzhenPatient.getPatientIdCard();
        this.patientNo = menzhenPatient.getPatientId();
        this.jzCardNo = menzhenPatient.getCardNo();
        this.menzhenNo = menzhenPatient.getOutpatientNumber();
        this.zhuyuanNo = "";
        this.name = menzhenPatient.getPatientName().trim();
        this.gender = "1".equals(menzhenPatient.getPatientSex()) ? 0 : 1;
        this.mobile = CharSequenceUtil.trim(CharSequenceUtil.defaultIfNull(menzhenPatient.getPatientPhone(), mobile));
        this.openId = session.getOpenId();
        this.unionId = session.getUnionId();
        this.active = 0;
        this.empi = "";
        this.erhcCardNo = "";
        this.menzhenBalance = null;
        this.zhuyuanBalance = null;
        this.guarderPhoneNumber = guarderPhoneNumber;
        this.guarderName = guarderName;
        this.guarderIDCard = guarderIDCard;
    }

    public ClientType getClientTypeEnum() {
        if (getClientType() == null) {
            return ClientType.WEIXIN;
        } else {
            return ClientType.Companion.fromCode(getClientType());
        }
    }

    /**
     * 原始身份证号（用于验证），不会被序列化
     */
    @JsonIgnore
    private String originalIdCardNo;

    public Boolean isMale() {
        String idCardToCheck = originalIdCardNo != null ? originalIdCardNo : idCardNo;
        return !StrValidator.isBlank(idCardToCheck) && IdcardUtil.getGender(idCardToCheck) == 1;
    }

    public Boolean isFemale() {
        String idCardToCheck = originalIdCardNo != null ? originalIdCardNo : idCardNo;
        return !StrValidator.isBlank(idCardToCheck) && IdcardUtil.getGender(idCardToCheck) == 0;
    }

    public Integer getAge() {
        String idCardToCheck = originalIdCardNo != null ? originalIdCardNo : idCardNo;
        return !StrValidator.isBlank(idCardToCheck) ? IdcardUtil.getAge(idCardToCheck) : null;
    }

    public boolean hasErhcCard() {
        return !erhcCardNo.isEmpty();
    }

    public Patient maskSensitive() {
        if (this.originalIdCardNo == null) {
            this.originalIdCardNo = this.idCardNo;
        }
        this.idCardNo = StringKit.INSTANCE.hideIdCardNo(this.idCardNo);
        this.mobile = StringKit.INSTANCE.hideMobile(this.mobile);
        return this;
    }

    /**
     * 获取二维码加密字符串，仅在JSON序列化时使用
     */
    @JsonGetter("qrcode")
    public String getQrcode() {
        return HisExt.INSTANCE.encryptForQrCode(this.getJzCardNo());
    }
}