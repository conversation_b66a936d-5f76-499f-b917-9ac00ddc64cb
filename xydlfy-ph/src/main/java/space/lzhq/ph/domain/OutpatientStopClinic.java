package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonGetter;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 门诊停诊消息
 */
@Data
@TableName("ph_outpatient_stop_clinic")
public class OutpatientStopClinic implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 排班ID
     */
    private String scheduleMark;

    /**
     * 预约ID，具有唯一性
     *
     * @see Reservation#getReservationNumber()
     */
    private String appointmentId;

    /**
     * 病人ID
     */
    private String sourcePatientId;

    /**
     * 挂号时间
     */
    private LocalDateTime registeredDateTime;

    /**
     * 预约时间（就诊时间）
     */
    private LocalDateTime appointmentDateTime;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 患者姓名
     */
    private String name;

    /**
     * 挂号科室ID
     */
    private String registeredDept;

    /**
     * 挂号科室名称
     */
    private String registeredDeptName;

    /**
     * 挂号医生ID
     */
    private String registeredDoctor;

    /**
     * 挂号医生姓名
     */
    private String registeredDoctorName;

    /**
     * 操作人姓名
     */
    private String operatingName;

    /**
     * 停诊操作时间
     */
    private LocalDateTime operatingDateTime;

    /**
     * 消息内容
     */
    private String messageContent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 是否已读
     */
    private Boolean isRead;

    /**
     * 微信用户ID
     */
    private String openId;

    /**
     * 订阅消息发送时间
     */
    private LocalDateTime subscribeMessageSendTime;

    /**
     * 订阅消息发送状态 0-成功 1-失败
     */
    private Boolean subscribeMessageSendStatus;

    /**
     * 订阅消息发送失败原因
     */
    private String subscribeMessageError;

    @JsonGetter("stopType")
    public String getStopType() {
        if (this.getMessageContent().contains("替诊")) {
            return "替诊";
        }

        return "停诊";
    }
}
