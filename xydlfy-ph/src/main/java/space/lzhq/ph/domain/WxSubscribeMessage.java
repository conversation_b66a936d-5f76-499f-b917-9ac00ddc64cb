package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "ph_wx_subscribe_message")
public class WxSubscribeMessage implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 模板ID
     */
    @Excel(name = "模板ID")
    private String templateId;

    /**
     * openid
     */
    @Excel(name = "openid")
    private String openid;

    /**
     * 消息内容
     */
    @Excel(name = "消息内容")
    private String data;

    /**
     * 消息类型
     */
    @Excel(name = "消息类型")
    private String type;

    /**
     * 关联ID
     */
    @Excel(name = "关联ID")
    private String companionId;

    /**
     * 发送时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime sendTime;

    /**
     * 发送状态
     */
    @Excel(name = "发送状态")
    private boolean sendStatus;

    /**
     * 错误信息
     */
    @Excel(name = "错误信息")
    private String error;

}
