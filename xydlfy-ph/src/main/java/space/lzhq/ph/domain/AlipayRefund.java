package space.lzhq.ph.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.mospital.bsoft.MenzhenRefund;
import org.mospital.bsoft.Result;

import java.io.Serial;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@NoArgsConstructor
public class AlipayRefund extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 支付宝用户ID
     */
    private String openid;

    /**
     * 患者索引号
     */
    @Excel(name = "患者索引号")
    private String patientId;

    /**
     * 就诊卡号
     */
    @Excel(name = "就诊卡号", width = 30)
    private String jzCardNo;

    /**
     * 住院号
     */
    private String zhuyuanNo;

    /**
     * 总金额
     */
    @Excel(name = "总金额/分")
    private Long totalAmount;

    /**
     * 订单金额
     */
    @Excel(name = "订单金额/分")
    private Long amount;

    /**
     * 掌医充值订单号
     */
    @Excel(name = "掌医充值订单号", width = 30)
    private String outTradeNo;

    /**
     * 支付宝充值订单号
     */
    @Excel(name = "支付宝充值订单号", width = 30)
    private String tradeNo;

    /**
     * 退款订单号
     */
    @Excel(name = "退款订单号", width = 30)
    private String outRefundNo;

    /**
     * HIS交易状态
     */
    @Excel(name = "HIS交易状态")
    private String hisTradeStatus;

    /**
     * HIS交易单号
     */
    @Excel(name = "HIS交易单号")
    private String hisTradeNo;

    /**
     * 支付宝交易状态
     */
    @Excel(name = "支付宝交易状态")
    private String alipayTradeStatus;

    /**
     * 支付宝交易时间
     */
    @Excel(name = "支付宝交易时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date alipayTradeTime;

    /**
     * 人工退款状态:0=未发起,1=待放行,2=已放行
     */
    @Excel(name = "人工退款状态", dictType = "manual_state")
    private Integer manualRefundState;

    public AlipayRefund(AlipayPayment payment, Long refundAmount, Result<MenzhenRefund> hisRefundResult) {
        this.setCreateTime(new Date());
        this.type = payment.getType();
        this.openid = payment.getOpenid();
        this.patientId = payment.getPatientId();
        this.jzCardNo = payment.getJzCardNo();
        this.zhuyuanNo = payment.getZhuyuanNo();
        this.totalAmount = payment.getTotalAmount();
        this.amount = refundAmount;
        this.outTradeNo = payment.getOutTradeNo();
        this.tradeNo = payment.getTradeNo();
        this.outRefundNo = "";
        if (hisRefundResult == null) {
            this.hisTradeStatus = Constants.REFUND_OK;
            this.hisTradeNo = "";
        } else if (hisRefundResult.isOk()) {
            MenzhenRefund menzhenRefund = hisRefundResult.getData();
            this.hisTradeStatus = Constants.REFUND_OK;
            this.hisTradeNo = menzhenRefund == null ? "" : menzhenRefund.getReceiptNo();
        } else {
            this.hisTradeStatus = hisRefundResult.getMessage();
            this.hisTradeNo = "";
        }
        this.alipayTradeStatus = "";
        this.alipayTradeTime = null;
        this.manualRefundState = Constants.MANUAL_INIT;
    }

}
