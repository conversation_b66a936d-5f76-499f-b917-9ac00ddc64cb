package space.lzhq.ph.domain;

import org.dromara.hutool.core.text.StrUtil;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class WikiArticle extends BaseEntity {

    /**
     * ID
     */
    private Long id;

    /**
     * 栏目
     */
    private Long categoryId;

    /**
     * 栏目
     */
    @Excel(name = "栏目")
    private String categoryName;

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 摘要
     */
    @Excel(name = "摘要")
    private String summary;

    /**
     * 封面图
     */
    private String coverImage;

    /**
     * 内容
     */
    @Excel(name = "内容")
    private String content;

    /**
     * 视频链接
     */
    private String videoUrl;

    private Integer sortNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getCoverImage() {
        return coverImage;
    }

    public void setCoverImage(String coverImage) {
        this.coverImage = coverImage;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getVideoId() {
        if (StrUtil.isBlank(getVideoUrl())) {
            return "";
        }

        String[] items = getVideoUrl().split("/");
        return StrUtil.removeSuffix(items[items.length - 1], ".html");
    }

    public Integer getSortNo() {
        return sortNo;
    }

    public void setSortNo(Integer sortNo) {
        this.sortNo = sortNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", id)
                .append("categoryId", categoryId)
                .append("categoryName", categoryName)
                .append("title", title)
                .append("summary", summary)
                .append("coverImage", coverImage)
                .append("content", content)
                .append("videoId", videoUrl)
                .append("sortNo", sortNo)
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
