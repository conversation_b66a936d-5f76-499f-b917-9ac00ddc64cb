package space.lzhq.ph.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * PACS报告对象 medexmemrstemp.dbo.Report_ZY
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("medexmemrstemp.dbo.Report_ZY")
public class PacsReport implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 病历号
     */
    @TableField("病历号")
    private String medicalRecordNo;

    /**
     * 姓名
     */
    @TableField("姓名")
    private String patientName;

    /**
     * 身份证号
     */
    @TableField("身份证号")
    private String idCardNo;

    /**
     * 申请单号
     */
    @TableField("申请单号")
    private String applicationNo;

    /**
     * 检查号
     */
    @TableField("检查号")
    private String examNo;

    /**
     * 来源
     */
    @TableField("来源")
    private String source;

    /**
     * 门诊号
     */
    @TableField("门诊号")
    private String outpatientNo;

    /**
     * 住院号
     */
    @TableField("住院号")
    private String inpatientNo;

    /**
     * 报告链接
     */
    @TableField("报告链接")
    private String reportLink;
}