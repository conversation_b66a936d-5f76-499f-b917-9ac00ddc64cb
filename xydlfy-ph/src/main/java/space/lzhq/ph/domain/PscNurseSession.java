package space.lzhq.ph.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.densensitize.MobileDesensitize;
import com.ruoyi.common.densensitize.NameDesensitize;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.mospital.bsoft.Nurse;

import java.io.Serial;

/**
 * 护士会话对象 psc_nurse_session
 *
 * <AUTHOR>
 * @date 2022-06-12
 */
public class PscNurseSession extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    @NameDesensitize
    private String name;

    /**
     * 工号
     */
    @Excel(name = "工号")
    private String employeeNo;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    @MobileDesensitize
    private String mobile;

    /**
     * 客户端类型:0=微信,1=支付宝
     */
    @Excel(name = "客户端类型:0=微信,1=支付宝")
    private Integer clientType;

    /**
     *
     */
    @Excel(name = "")
    private String openId;

    /**
     *
     */
    @Excel(name = "")
    private String unionId;

    public PscNurseSession() {
    }

    public PscNurseSession(
            String name,
            String employeeNo,
            String mobile,
            Integer clientType,
            String openId,
            String unionId
    ) {
        this.name = name;
        this.employeeNo = employeeNo;
        this.mobile = mobile;
        this.clientType = clientType;
        this.openId = openId;
        this.unionId = unionId;
    }

    public PscNurseSession(PscNurse nurse, Session clientSession) {
        this.name = nurse.getName();
        this.employeeNo = nurse.getEmployeeNo();
        this.mobile = nurse.getMobile();
        this.clientType = clientSession.getClientType();
        this.openId = clientSession.getOpenId();
        this.unionId = clientSession.getUnionId();
    }

    public PscNurseSession(Nurse nurse, Session clientSession) {
        this.name = nurse.getUserName();
        this.employeeNo = nurse.getUserId();
        this.mobile = "";
        this.clientType = clientSession.getClientType();
        this.openId = clientSession.getOpenId();
        this.unionId = clientSession.getUnionId();
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setEmployeeNo(String employeeNo) {
        this.employeeNo = employeeNo;
    }

    public String getEmployeeNo() {
        return employeeNo;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getUnionId() {
        return unionId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("employeeNo", getEmployeeNo())
                .append("mobile", getMobile())
                .append("clientType", getClientType())
                .append("openId", getOpenId())
                .append("unionId", getUnionId())
                .toString();
    }
}
