package space.lzhq.ph.domain;

import org.dromara.hutool.core.text.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.DictUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.dromara.hutool.core.text.split.SplitUtil;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单对象 psc_order
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
public class PscOrder extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 待分配陪检
     */
    public static int STATUS_INIT = 0;

    /**
     * 待陪检接单
     */
    public static int STATUS_TAKING = 1;

    /**
     * 服务已就绪
     */
    public static int STATUS_READY = 2;

    /**
     * 正在服务
     */
    public static int STATUS_SERVING = 3;

    /**
     * 待护士验收
     */
    public static int STATUS_CONFIRMING = 4;

    /**
     * 服务完成
     */
    public static int STATUS_COMPLETE = 99;

    /**
     * ID
     */
    private Long id;

    /**
     * 住院号
     */
    @Excel(name = "住院号")
    private String admissionNumber;

    /**
     * 患者ID
     */
    @Excel(name = "患者ID")
    private String patientId;

    /**
     * 患者身份证号
     */
    @Excel(name = "患者身份证号")
    private String patientIdCardNo;

    /**
     * 患者姓名
     */
    @Excel(name = "患者姓名")
    private String patientName;

    /**
     * 患者性别
     */
    @Excel(name = "患者性别", dictType = "sys_user_sex")
    private String patientSex;

    /**
     * 患者民族
     */
    @Excel(name = "患者民族", dictType = "minzu")
    private String patientNation;

    /**
     * 患者出生日期
     */
    @Excel(name = "患者出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate patientBirthday;

    /**
     * 患者手机号
     */
    @Excel(name = "患者手机号")
    private String patientMobile;

    /**
     * 患者科室
     */
    @Excel(name = "患者科室")
    private String patientDepartment;

    /**
     * 患者床号
     */
    @Excel(name = "患者床号")
    private String patientBedNumber;

    /**
     * 服务类型
     */
    @Excel(name = "服务类型", dictType = "psc_service_type")
    private String serviceType;

    /**
     * 检查项目
     */
    @Excel(name = "检查项目", dictType = "psc_exam_item", separator = ",")
    private String examItems;

    /**
     * 检查项目数量
     */
    private BigDecimal examItemsCount;

    /**
     * 护士工号
     */
    @Excel(name = "护士工号")
    private String nurseEmployeeNo;

    /**
     * 护士姓名
     */
    @Excel(name = "护士姓名")
    private String nurseName;

    /**
     * 护士手机号
     */
    @Excel(name = "护士手机号")
    private String nurseMobile;

    private Long nurseDeptId;

    /**
     * 陪检工号
     */
    @Excel(name = "陪检工号")
    private String carerEmployeeNo;

    /**
     * 陪检姓名
     */
    @Excel(name = "陪检姓名")
    private String carerName;

    /**
     * 陪检手机号
     */
    @Excel(name = "陪检手机号")
    private String carerMobile;

    /**
     * 状态：0=待分配陪检，1=待陪检接单，2=服务已就绪，3=正在服务，4=待护士验收，99=服务完成
     */
    @Excel(name = "状态", dictType = "psc_order_status")
    private Integer status;

    /**
     * 派发时间
     */
    @Excel(name = "派单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    /**
     * 接单时间
     */
    @Excel(name = "接单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date takeTime;

    /**
     * 开始时间
     */
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 验收时间
     */
    @Excel(name = "验收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date confirmTime;

    /**
     * 评价
     */
    @Excel(name = "评价", dictType = "psc_order_score")
    private Integer score;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setAdmissionNumber(String admissionNumber) {
        this.admissionNumber = admissionNumber;
    }

    public String getAdmissionNumber() {
        return admissionNumber;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getPatientId() {
        return patientId;
    }

    public String getPatientIdCardNo() {
        return patientIdCardNo;
    }

    public void setPatientIdCardNo(String patientIdCardNo) {
        this.patientIdCardNo = patientIdCardNo;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientSex(String patientSex) {
        this.patientSex = patientSex;
    }

    public String getPatientSex() {
        return patientSex;
    }

    public void setPatientNation(String patientNation) {
        this.patientNation = patientNation;
    }

    public String getPatientNation() {
        return patientNation;
    }

    public LocalDate getPatientBirthday() {
        return patientBirthday;
    }

    public void setPatientBirthday(LocalDate patientBirthday) {
        this.patientBirthday = patientBirthday;
    }

    public void setPatientMobile(String patientMobile) {
        this.patientMobile = patientMobile;
    }

    public String getPatientMobile() {
        return patientMobile;
    }

    public String getPatientDepartment() {
        return patientDepartment;
    }

    public void setPatientDepartment(String patientDepartment) {
        this.patientDepartment = patientDepartment;
    }

    public String getPatientBedNumber() {
        return patientBedNumber;
    }

    public void setPatientBedNumber(String patientBedNumber) {
        this.patientBedNumber = patientBedNumber;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setExamItems(String examItems) {
        this.examItems = examItems;
    }

    public String getExamItems() {
        return examItems;
    }

    public BigDecimal getExamItemsCount() {
        return examItemsCount;
    }

    public void setExamItemsCount(BigDecimal examItemsCount) {
        this.examItemsCount = examItemsCount;
    }

    public void setNurseEmployeeNo(String nurseEmployeeNo) {
        this.nurseEmployeeNo = nurseEmployeeNo;
    }

    public String getNurseEmployeeNo() {
        return nurseEmployeeNo;
    }

    public void setNurseName(String nurseName) {
        this.nurseName = nurseName;
    }

    public String getNurseName() {
        return nurseName;
    }

    public void setNurseMobile(String nurseMobile) {
        this.nurseMobile = nurseMobile;
    }

    public String getNurseMobile() {
        return nurseMobile;
    }

    public Long getNurseDeptId() {
        return nurseDeptId;
    }

    public void setNurseDeptId(Long nurseDeptId) {
        this.nurseDeptId = nurseDeptId;
    }

    public void setCarerEmployeeNo(String carerEmployeeNo) {
        this.carerEmployeeNo = carerEmployeeNo;
    }

    public String getCarerEmployeeNo() {
        return carerEmployeeNo;
    }

    public void setCarerName(String carerName) {
        this.carerName = carerName;
    }

    public String getCarerName() {
        return carerName;
    }

    public void setCarerMobile(String carerMobile) {
        this.carerMobile = carerMobile;
    }

    public String getCarerMobile() {
        return carerMobile;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setAssignTime(Date assignTime) {
        this.assignTime = assignTime;
    }

    public Date getAssignTime() {
        return assignTime;
    }

    public void setTakeTime(Date takeTime) {
        this.takeTime = takeTime;
    }

    public Date getTakeTime() {
        return takeTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public BigDecimal calculateExamItemCount() {
        List<String> examItemCodes = SplitUtil.split(this.examItems, ",");

        if ("4".equals(serviceType)) {
            // 手术：只能有一个检查项目，按0.5计算
            return new BigDecimal(0.5);
        }

        BigDecimal result = BigDecimal.ZERO;
        List<String> examItemTypes = DictUtils.getDictCache("psc_exam_item").stream()
                .filter((it) -> examItemCodes.contains(it.getDictValue()))
                .map(it -> StrUtil.subBefore(it.getDictLabel(), "-", false))
                .collect(Collectors.toList());

        if ("2".equals(serviceType)) {
            // 病床：非功能科的，按检查项目数量计算
            result = result.add(new BigDecimal(examItemTypes.stream().filter(it -> !it.startsWith("功能科")).count()));
            examItemTypes.removeIf(it -> !it.startsWith("功能科"));
        }

        // 放射科1和放射科2的项目：任意项目组合大于等于1且小于等于3，总体绩效计作1；若4个项目全部出现，总体绩效计作2。
        Long fangshekeCount = examItemTypes.stream().filter(it -> "放射科1".equals(it) || "放射科2".equals(it)).count();
        if (fangshekeCount >= 1 && fangshekeCount <= 3) {
            result = result.add(BigDecimal.ONE);
        } else if (fangshekeCount > 3) {
            result = result.add(new BigDecimal(2));
        }
        examItemTypes.removeIf(it -> "放射科1".equals(it) || "放射科2".equals(it));

        // 内镜室1和内镜室2的项目：任意项目组合大于等于1，总体绩效计作0.5。
        Long neijingshiCount = examItemTypes.stream().filter(it -> "内镜室1".equals(it) || "内镜室2".equals(it)).count();
        if (neijingshiCount >= 1) {
            result = result.add(new BigDecimal(0.5));
        }
        examItemTypes.removeIf(it -> "内镜室1".equals(it) || "内镜室2".equals(it));

        // 计算其它项目
        long otherCount = examItemTypes.stream().distinct().count();
        result = result.add(new BigDecimal(otherCount));

        return result;
    }

    public boolean canEdit() {
        return this.status == 0 || this.status == 1 || this.status == 2;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("admissionNumber", getAdmissionNumber())
                .append("patientId", getPatientId())
                .append("patientName", getPatientName())
                .append("patientSex", getPatientSex())
                .append("patientNation", getPatientNation())
                .append("patientBirthday", getPatientBirthday())
                .append("patientMobile", getPatientMobile())
                .append("patientDepartment", getPatientDepartment())
                .append("patientBedNumber", getPatientBedNumber())
                .append("serviceType", getServiceType())
                .append("examItems", getExamItems())
                .append("examItemsCount", getExamItemsCount())
                .append("nurseEmployeeNo", getNurseEmployeeNo())
                .append("nurseName", getNurseName())
                .append("nurseMobile", getNurseMobile())
                .append("carerEmployeeNo", getCarerEmployeeNo())
                .append("carerName", getCarerName())
                .append("carerMobile", getCarerMobile())
                .append("status", getStatus())
                .append("createTime", getCreateTime())
                .append("assignTime", getAssignTime())
                .append("takeTime", getTakeTime())
                .append("startTime", getStartTime())
                .append("endTime", getEndTime())
                .append("confirmTime", getConfirmTime())
                .append("score", getScore())
                .toString();
    }
}
