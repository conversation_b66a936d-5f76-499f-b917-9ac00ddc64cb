package space.lzhq.ph.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.Set;

/**
 * 性别验证注解
 * 验证性别必须为"男"或"女"
 */
@Constraint(validatedBy = ValidGender.GenderValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidGender {

    String message() default "患者性别必须是男或女";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    class GenderValidator implements ConstraintValidator<ValidGender, String> {

        private static final Set<String> VALID_GENDERS = Set.of("男", "女");

        @Override
        public boolean isValid(String gender, ConstraintValidatorContext context) {
            if (gender == null) {
                return true; // null值由@NotBlank处理
            }
            return VALID_GENDERS.contains(gender.trim());
        }
    }
} 