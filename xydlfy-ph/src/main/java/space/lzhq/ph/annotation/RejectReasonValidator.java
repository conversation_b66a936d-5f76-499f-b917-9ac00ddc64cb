package space.lzhq.ph.annotation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

/**
 * 驳回原因验证器
 * 验证当审批状态为REJECTED时，驳回原因必须提供且不能为空
 * 支持所有实现 ApprovalRequestDto 接口的审批请求DTO
 */
public class RejectReasonValidator implements ConstraintValidator<ValidRejectReason, ApprovalRequestDto> {

    @Override
    public void initialize(ValidRejectReason constraintAnnotation) {
        // 初始化方法，可以在此处获取注解参数
    }

    @Override
    public boolean isValid(ApprovalRequestDto value, ConstraintValidatorContext context) {
        // 如果对象为null，让其他验证器处理
        if (value == null) {
            return true;
        }

        // 如果状态为REJECTED，验证驳回原因是否提供
        if (value.isRejectedStatus()) {
            String rejectReason = value.getRejectReason();

            // 驳回原因不能为null且去除空白字符后不能为空
            if (StringUtils.isBlank(rejectReason)) {
                // 自定义错误消息
                context.disableDefaultConstraintViolation();
                context.buildConstraintViolationWithTemplate("当审批状态为驳回时，驳回原因不能为空")
                        .addPropertyNode("rejectReason")
                        .addConstraintViolation();
                return false;
            }
        }

        return true;
    }
} 