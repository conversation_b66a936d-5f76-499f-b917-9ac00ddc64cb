package space.lzhq.ph.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * 验证驳回原因的自定义注解
 * 当审批状态为REJECTED时，驳回原因必须提供且不能为空
 */
@Documented
@Constraint(validatedBy = RejectReasonValidator.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidRejectReason {

    /**
     * 验证失败时的错误消息
     */
    String message() default "当审批状态为驳回时，必须提供驳回原因";

    /**
     * 验证分组
     */
    Class<?>[] groups() default {};

    /**
     * 负载
     */
    Class<? extends Payload>[] payload() default {};
} 