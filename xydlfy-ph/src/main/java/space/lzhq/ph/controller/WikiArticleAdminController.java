package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.WikiArticle;
import space.lzhq.ph.service.IWikiArticleService;
import space.lzhq.ph.service.IWikiCategoryService;

import java.util.List;

/**
 * 文章Controller
 *
 * <AUTHOR>
 * @date 2020-05-30
 */
@Controller
@RequestMapping("/ph/wikiArticle")
public class WikiArticleAdminController extends BaseController {

    private final IWikiArticleService wikiArticleService;

    private final IWikiCategoryService wikiCategoryService;

    public WikiArticleAdminController(IWikiArticleService wikiArticleService, IWikiCategoryService wikiCategoryService) {
        this.wikiArticleService = wikiArticleService;
        this.wikiCategoryService = wikiCategoryService;
    }

    @RequiresPermissions("ph:wikiArticle:view")
    @GetMapping()
    public String article(ModelMap attrs) {
        attrs.put("categories", wikiCategoryService.selectAll());
        return "ph/wikiArticle/wikiArticle";
    }

    /**
     * 查询文章列表
     */
    @RequiresPermissions("ph:wikiArticle:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(WikiArticle wikiArticle) {
        startPage();
        List<WikiArticle> list = wikiArticleService.selectWikiArticleList(wikiArticle);
        return getDataTable(list);
    }

    /**
     * 导出文章列表
     */
    @RequiresPermissions("ph:wikiArticle:export")
    @Log(title = "文章", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(WikiArticle wikiArticle) {
        List<WikiArticle> list = wikiArticleService.selectWikiArticleList(wikiArticle);
        ExcelUtil<WikiArticle> util = new ExcelUtil<>(WikiArticle.class);
        return util.exportExcel(list, "wikiArticle");
    }

    /**
     * 新增文章
     */
    @GetMapping("/add")
    public String add(ModelMap attrs) {
        attrs.put("categories", wikiCategoryService.selectAll());
        return "ph/wikiArticle/add";
    }

    /**
     * 新增保存文章
     */
    @RequiresPermissions("ph:wikiArticle:add")
    @Log(title = "文章", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(WikiArticle wikiArticle) {
        return toAjax(wikiArticleService.insertWikiArticle(wikiArticle));
    }

    /**
     * 修改文章
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, ModelMap attrs) {
        WikiArticle wikiArticle = wikiArticleService.selectWikiArticleById(id);
        attrs.put("article", wikiArticle);
        attrs.put("categories", wikiCategoryService.selectAll());
        return "ph/wikiArticle/edit";
    }

    /**
     * 修改保存文章
     */
    @RequiresPermissions("ph:wikiArticle:edit")
    @Log(title = "文章", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(WikiArticle wikiArticle) {
        return toAjax(wikiArticleService.updateWikiArticle(wikiArticle));
    }

    /**
     * 删除文章
     */
    @RequiresPermissions("ph:wikiArticle:remove")
    @Log(title = "文章", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(wikiArticleService.deleteWikiArticleByIds(ids));
    }

}
