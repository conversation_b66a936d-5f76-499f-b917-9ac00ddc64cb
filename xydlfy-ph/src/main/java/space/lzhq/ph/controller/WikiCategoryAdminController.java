package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.WikiCategory;
import space.lzhq.ph.service.IWikiCategoryService;

import java.util.List;

/**
 * 知识分类Controller
 */
@Controller
@RequestMapping("/ph/wikiCategory")
public class WikiCategoryAdminController extends BaseController {

    private final IWikiCategoryService wikiCategoryService;

    public WikiCategoryAdminController(IWikiCategoryService wikiCategoryService) {
        this.wikiCategoryService = wikiCategoryService;
    }

    @RequiresPermissions("ph:wikiCategory:view")
    @GetMapping()
    public String wikiCategory() {
        return "ph/wikiCategory/wikiCategory";
    }

    /**
     * 查询知识分类列表
     */
    @RequiresPermissions("ph:wikiCategory:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(WikiCategory wikiCategory) {
        startPage("sort_no asc, id desc");
        List<WikiCategory> list = wikiCategoryService.selectWikiCategoryList(wikiCategory);
        return getDataTable(list);
    }

    /**
     * 导出知识分类列表
     */
    @RequiresPermissions("ph:wikiCategory:export")
    @Log(title = "知识分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(WikiCategory wikiCategory) {
        startPage("sort_no asc, id desc");
        List<WikiCategory> list = wikiCategoryService.selectWikiCategoryList(wikiCategory);
        ExcelUtil<WikiCategory> util = new ExcelUtil<>(WikiCategory.class);
        return util.exportExcel(list, "知识分类数据");
    }

    /**
     * 新增知识分类
     */
    @GetMapping("/add")
    public String add() {
        return "ph/wikiCategory/add";
    }

    /**
     * 新增保存知识分类
     */
    @RequiresPermissions("ph:wikiCategory:add")
    @Log(title = "知识分类", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(WikiCategory wikiCategory) {
        return toAjax(wikiCategoryService.insertWikiCategory(wikiCategory));
    }

    /**
     * 修改知识分类
     */
    @RequiresPermissions("ph:wikiCategory:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, ModelMap mmap) {
        WikiCategory wikiCategory = wikiCategoryService.selectWikiCategoryById(id);
        mmap.put("wikiCategory", wikiCategory);
        return "ph/wikiCategory/edit";
    }

    /**
     * 修改保存知识分类
     */
    @RequiresPermissions("ph:wikiCategory:edit")
    @Log(title = "知识分类", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(WikiCategory wikiCategory) {
        return toAjax(wikiCategoryService.updateWikiCategory(wikiCategory));
    }

    /**
     * 删除知识分类
     */
    @RequiresPermissions("ph:wikiCategory:remove")
    @Log(title = "知识分类", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        if (ids.split(",").length > 1) {
            return error("一次只能删除一个分类");
        }

        Long id = Long.parseLong(ids);
        return toAjax(wikiCategoryService.deleteWikiCategoryById(id));
    }
}
