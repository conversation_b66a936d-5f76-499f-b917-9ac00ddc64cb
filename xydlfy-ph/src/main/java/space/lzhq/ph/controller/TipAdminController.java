package space.lzhq.ph.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.Tip;
import space.lzhq.ph.service.ITipService;

import java.util.List;

/**
 * 提示信息Controller
 *
 * <AUTHOR>
 * @date 2023-07-17
 */
@Controller
@RequestMapping("/ph/tip")
public class TipAdminController extends BaseController {
    private String prefix = "ph/tip";

    @Autowired
    private ITipService tipService;

    @RequiresPermissions("ph:tip:view")
    @GetMapping()
    public String tip() {
        return prefix + "/tip";
    }

    /**
     * 查询提示信息列表
     */
    @RequiresPermissions("ph:tip:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Tip tip) {
        startPage();
        List<Tip> list = tipService.selectTipList(tip);
        return getDataTable(list);
    }

    /**
     * 导出提示信息列表
     */
    @RequiresPermissions("ph:tip:export")
    @Log(title = "提示信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Tip tip) {
        List<Tip> list = tipService.selectTipList(tip);
        ExcelUtil<Tip> util = new ExcelUtil<Tip>(Tip.class);
        return util.exportExcel(list, "提示信息数据");
    }

    /**
     * 新增提示信息
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存提示信息
     */
    @RequiresPermissions("ph:tip:add")
    @Log(title = "提示信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Tip tip) {
        return toAjax(tipService.insertTip(tip));
    }

    /**
     * 修改提示信息
     */
    @RequiresPermissions("ph:tip:edit")
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, ModelMap mmap) {
        Tip tip = tipService.selectTipById(id);
        mmap.put("tip", tip);
        return prefix + "/edit";
    }

    /**
     * 修改保存提示信息
     */
    @RequiresPermissions("ph:tip:edit")
    @Log(title = "提示信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Tip tip) {
        return toAjax(tipService.updateTip(tip));
    }

    /**
     * 删除提示信息
     */
    @RequiresPermissions("ph:tip:remove")
    @Log(title = "提示信息", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(tipService.deleteTipByIds(ids));
    }
}
