package space.lzhq.ph.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.PscOrderSummaryByCarer;
import space.lzhq.ph.domain.PscOrderSummaryByCarerAndDepartment;
import space.lzhq.ph.service.IPscOrderSummaryByCarerService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/ph/pscOrderSummaryByCarer")
public class PscOrderSummaryByCarerAdminController extends BaseController {

    private final IPscOrderSummaryByCarerService service;

    public PscOrderSummaryByCarerAdminController(IPscOrderSummaryByCarerService service) {
        this.service = service;
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:view")
    @GetMapping
    public String page() {
        return "ph/pscOrderSummaryByCarer/index";
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate minDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate maxDate
    ) {
        LocalDateTime minConfirmTime = minDate.atStartOfDay();
        LocalDateTime maxConfirmTime = maxDate.atTime(LocalTime.MAX);
        List<PscOrderSummaryByCarer> list = service.summary(minConfirmTime, maxConfirmTime);
        return getDataTable(list);
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate minDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate maxDate
    ) {
        LocalDateTime minConfirmTime = minDate.atStartOfDay();
        LocalDateTime maxConfirmTime = maxDate.atTime(LocalTime.MAX);
        List<PscOrderSummaryByCarer> list = service.summary(minConfirmTime, maxConfirmTime);
        ExcelUtil<PscOrderSummaryByCarer> util = new ExcelUtil<>(PscOrderSummaryByCarer.class);
        return util.exportFastExcel(list, "订单统计表");
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:view")
    @GetMapping("/carerPage")
    public String carerPage(
            @RequestParam String carerEmployeeNo,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate minDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate maxDate,
            ModelMap modelMap
    ) {
        modelMap.put("carerEmployeeNo", carerEmployeeNo);
        modelMap.put("minDate", minDate);
        modelMap.put("maxDate", maxDate);
        return "ph/pscOrderSummaryByCarer/carer";
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:view")
    @PostMapping("/carerData")
    @ResponseBody
    public TableDataInfo carerData(
            @RequestParam String carerEmployeeNo,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate minDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate maxDate
    ) {
        LocalDateTime minConfirmTime = minDate.atStartOfDay();
        LocalDateTime maxConfirmTime = maxDate.atTime(LocalTime.MAX);
        List<PscOrderSummaryByCarerAndDepartment> list = service.summaryByCarerAndDepartment(
                minConfirmTime,
                maxConfirmTime,
                carerEmployeeNo
        );
        return getDataTable(list);
    }

    @RequiresPermissions("ph:pscOrderSummaryByCarer:view")
    @PostMapping("/exportCarerData")
    @ResponseBody
    public AjaxResult export(
            @RequestParam String carerEmployeeNo,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate minDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate maxDate
    ) {
        LocalDateTime minConfirmTime = minDate.atStartOfDay();
        LocalDateTime maxConfirmTime = maxDate.atTime(LocalTime.MAX);
        List<PscOrderSummaryByCarerAndDepartment> list = service.summaryByCarerAndDepartment(
                minConfirmTime,
                maxConfirmTime,
                carerEmployeeNo
        );
        ExcelUtil<PscOrderSummaryByCarerAndDepartment> util =
                new ExcelUtil<>(PscOrderSummaryByCarerAndDepartment.class);
        return util.exportFastExcel(list, "工作量统计表");
    }

}
