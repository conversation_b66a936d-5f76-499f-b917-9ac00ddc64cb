package space.lzhq.ph.controller;

import org.dromara.hutool.core.array.ArrayUtil;
import org.dromara.hutool.core.data.IdcardUtil;
import org.dromara.hutool.core.text.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.util.ShiroUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.mospital.bsoft.BSoftService;
import org.mospital.bsoft.Result;
import org.mospital.bsoft.ZhuyuanPatient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.PscCarer;
import space.lzhq.ph.domain.PscOrder;
import space.lzhq.ph.ext.HisExtKt;
import space.lzhq.ph.service.IPscCarerService;
import space.lzhq.ph.service.IPscOrderService;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

/**
 * 订单Controller
 *
 * <AUTHOR>
 * @date 2022-06-23
 */
@Controller
@RequestMapping("/ph/pscOrder")
public class PscOrderAdminController extends BaseController {
    private String prefix = "ph/pscOrder";

    @Autowired
    private IPscOrderService pscOrderService;

    @Autowired
    private IPscCarerService pscCarerService;

    @RequiresPermissions("ph:pscOrder:view")
    @GetMapping()
    public String pscOrder(ModelMap modelMap) {
        modelMap.put("departments", pscOrderService.selectDepartments());
        return prefix + "/pscOrder";
    }

    @RequiresPermissions("ph:pscOrder:viewByNurse")
    @GetMapping("/byNurse")
    public String pscOrderByNurse() {
        return prefix + "/pscOrderByNurse";
    }

    private void handleParams(PscOrder pscOrder) {
        Map<String, Object> params = pscOrder.getParams();
        if (params != null) {
            Object filterCenterOrder = params.get("filterCenterOrder");
            if (filterCenterOrder != null && !StrUtil.EMPTY.equals(filterCenterOrder)) {
                params.put("nurseDeptId", 160); // 160:病友服务中心
            }
        }
    }

    /**
     * 查询订单列表
     */
    @RequiresPermissions("ph:pscOrder:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(PscOrder pscOrder) {
        handleParams(pscOrder);

        startPage("id desc");
        List<PscOrder> list = pscOrderService.selectPscOrderList(pscOrder);
        return getDataTable(list);
    }

    @RequiresPermissions("ph:pscOrder:listByNurse")
    @PostMapping("/listByNurse")
    @ResponseBody
    public TableDataInfo listByNurse(PscOrder pscOrder) {
        SysUser user = ShiroUtils.getSysUser();
        pscOrder.setNurseDeptId(user.getDeptId());
        startPage("id desc");
        List<PscOrder> list = pscOrderService.selectPscOrderList(pscOrder);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    @RequiresPermissions("ph:pscOrder:export")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(PscOrder pscOrder) {
        handleParams(pscOrder);

        List<PscOrder> list = pscOrderService.selectPscOrderList(pscOrder);
        ExcelUtil<PscOrder> util = new ExcelUtil<PscOrder>(PscOrder.class);
        return util.exportExcel(list, "pscOrder");
    }

    @RequiresPermissions("ph:pscOrder:exportByNurse")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @PostMapping("/exportByNurse")
    @ResponseBody
    public AjaxResult exportByNurse(PscOrder pscOrder) {
        SysUser user = ShiroUtils.getSysUser();
        pscOrder.setNurseEmployeeNo(StrUtil.removePrefix(user.getLoginName(), "N"));
        List<PscOrder> list = pscOrderService.selectPscOrderList(pscOrder);
        ExcelUtil<PscOrder> util = new ExcelUtil<PscOrder>(PscOrder.class);
        return util.exportExcel(list, "pscOrder");
    }

    /**
     * 新增订单
     */
    @RequiresPermissions("ph:pscOrder:centerAdd")
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    @GetMapping("/addByNurse")
    public String addByNurse() {
        return prefix + "/addByNurse";
    }

    @GetMapping("/queryZhuyuanPatient")
    @ResponseBody
    public AjaxResult queryZhuyuanPatient(@RequestParam String admissionNumber) {
        Result<ZhuyuanPatient> zhuyuanPatientResult =
                BSoftService.Companion.getZhuyuanPatientByAdmissionNumber(admissionNumber);
        return HisExtKt.toAjaxResult(zhuyuanPatientResult);
    }

    /**
     * 新增保存订单
     */
    @RequiresPermissions("ph:pscOrder:centerAdd")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(PscOrder pscOrder) {
        return addByNurseSave(pscOrder);
    }

    @RequiresPermissions("ph:pscOrder:addByNurse")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping("/addByNurse")
    @ResponseBody
    public AjaxResult addByNurseSave(PscOrder pscOrder) {
        if (!IdcardUtil.isValidCard(pscOrder.getPatientIdCardNo())) {
            return AjaxResult.error("患者身份证号不正确");
        }

        if ("4".equals(pscOrder.getServiceType())) {
            if (!"08-01".equals(pscOrder.getExamItems())) {
                return AjaxResult.error("服务类型为【手术】时，检查项目只能是【其他-手术】");
            }
        }

        if (pscOrder.getExamItems().contains("08-01")) {
            if (pscOrder.getExamItems().length() > 5) {
                return AjaxResult.error("检查项目【其他-手术】不能和其他检查项目同时选择");
            }
            if (!pscOrder.getServiceType().equals("4")) {
                return AjaxResult.error("检查项目为【其他-手术】时，服务类型只能是【手术】");
            }
        }

        synchronized (pscOrder.getPatientId().intern()) {
            List<PscOrder> unfinishedOrders = pscOrderService.selectUnfinishedOrderList(pscOrder.getPatientId());
            if (unfinishedOrders.stream().anyMatch(it -> it.getExamItems().equals(pscOrder.getExamItems()))) {
                return AjaxResult.error("该患者已有相同检查项目的未完成订单，请勿重复添加");
            }

            pscOrder.setPatientSex(String.valueOf(1 - IdcardUtil.getGender(pscOrder.getPatientIdCardNo())));
            pscOrder.setPatientBirthday(LocalDate.parse(IdcardUtil.getBirth(pscOrder.getPatientIdCardNo()),
                    DateTimeFormatter.ofPattern("yyyyMMdd")));

            SysUser user = ShiroUtils.getSysUser();
            pscOrder.setNurseEmployeeNo(StrUtil.removePrefix(user.getLoginName(), "N"));
            pscOrder.setNurseName(user.getUserName());
            pscOrder.setNurseMobile(StrUtil.defaultIfBlank(user.getPhonenumber(), ""));
            pscOrder.setNurseDeptId(user.getDeptId());
            pscOrder.setExamItemsCount(pscOrder.calculateExamItemCount());
            pscOrder.setCreateTime(new Date());
            pscOrder.setStatus(PscOrder.STATUS_INIT);
            return toAjax(pscOrderService.insertPscOrder(pscOrder));
        }
    }

    /**
     * 修改订单
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable Long id, ModelMap mmap) {
        PscOrder pscOrder = pscOrderService.selectPscOrderById(id);
        mmap.put("pscOrder", pscOrder);
        return prefix + "/edit";
    }

    @GetMapping("/editByNurse/{id}")
    public String editByNurse(@PathVariable Long id, ModelMap mmap) {
        PscOrder pscOrder = pscOrderService.selectPscOrderById(id);
        mmap.put("pscOrder", pscOrder);
        return prefix + "/editByNurse";
    }

    /**
     * 修改保存订单
     */
    @RequiresPermissions("ph:pscOrder:edit")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(PscOrder pscOrder) {
        return toAjax(pscOrderService.updatePscOrder(pscOrder));
    }

    @RequiresPermissions("ph:pscOrder:editByNurse")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PostMapping("/editByNurse")
    @ResponseBody
    public AjaxResult editByNurseSave(PscOrder pscOrder) {
        PscOrder order = pscOrderService.selectPscOrderById(pscOrder.getId());
        if (!order.canEdit()) {
            return AjaxResult.error("此订单不允许修改");
        }

        if ("4".equals(pscOrder.getServiceType())) {
            if (!"08-01".equals(pscOrder.getExamItems())) {
                return AjaxResult.error("服务类型为【手术】时，检查项目只能是【其他-手术】");
            }
        }

        if (pscOrder.getExamItems().contains("08-01")) {
            if (pscOrder.getExamItems().length() > 5) {
                return AjaxResult.error("检查项目【其他-手术】不能和其他检查项目同时选择");
            }
            if (!pscOrder.getServiceType().equals("4")) {
                return AjaxResult.error("检查项目为【其他-手术】时，服务类型只能是【手术】");
            }
        }

        order.setPatientMobile(pscOrder.getPatientMobile());
        order.setServiceType(pscOrder.getServiceType());
        order.setExamItems(pscOrder.getExamItems());
        order.setExamItemsCount(pscOrder.calculateExamItemCount());
        return toAjax(pscOrderService.updatePscOrder(order));
    }

    /**
     * 删除订单
     */
    @RequiresPermissions(value = {"ph:pscOrder:remove", "ph:pscOrder:removeByNurse"}, logical = Logical.OR)
    @Log(title = "订单", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(pscOrderService.deletePscOrderByIds(ids));
    }

    @RequiresPermissions("ph:pscOrder:assign")
    @GetMapping("/assign")
    public String assign(@RequestParam String ids, ModelMap modelMap) {
        modelMap.put("ids", ids);
        return prefix + "/assign";
    }

    @RequiresPermissions("ph:pscOrder:assign")
    @PostMapping("/assign")
    @ResponseBody
    public AjaxResult doAssign(
            @RequestParam String ids,
            @RequestParam Long carerId
    ) {
        PscCarer pscCarer = pscCarerService.selectPscCarerById(carerId);
        if (pscCarer == null) {
            return AjaxResult.error("陪检不存在或已删除");
        }

        pscOrderService.assignCarer(ids, pscCarer);
        return AjaxResult.success();
    }

    @RequiresPermissions("ph:pscOrder:confirm")
    @GetMapping("/confirm/{id}")
    public String confirm(@PathVariable Long id, ModelMap modelMap) {
        PscOrder pscOrder = pscOrderService.selectPscOrderById(id);
        modelMap.put("pscOrder", pscOrder);
        return prefix + "/confirm";
    }

    /**
     * 注意：入参pscOrder对象中只有id、examItems、score，其它字段不可用
     *
     * @param pscOrder
     * @return
     */
    @RequiresPermissions("ph:pscOrder:confirm")
    @PostMapping("/confirm")
    @ResponseBody
    public AjaxResult doConfirm(PscOrder pscOrder) {
        PscOrder oldOrder = pscOrderService.selectPscOrderById(pscOrder.getId());
        if (oldOrder == null) {
            return AjaxResult.error("工单不存在或已删除");
        }
        if (oldOrder.getStatus() != PscOrder.STATUS_CONFIRMING) {
            return AjaxResult.error("当前工单状态不可验收");
        }

        String[] newExamItems = pscOrder.getExamItems().split(StrUtil.COMMA);
        String[] oldExamItems = oldOrder.getExamItems().split(StrUtil.COMMA);

        oldOrder.setExamItems(pscOrder.getExamItems());
        oldOrder.setScore(pscOrder.getScore());
        oldOrder.setStatus(PscOrder.STATUS_COMPLETE);
        oldOrder.setConfirmTime(new Date());
        oldOrder.setExamItemsCount(oldOrder.calculateExamItemCount());
        pscOrderService.updatePscOrder(oldOrder);

        String[] canceledExamItems = ArrayUtil.filter(oldExamItems, s -> !ArrayUtil.contains(newExamItems, s));
        if (canceledExamItems.length > 0) {
            oldOrder.setId(null);
            oldOrder.setExamItems(ArrayUtil.join(canceledExamItems, StrUtil.COMMA));
            oldOrder.setExamItemsCount(oldOrder.calculateExamItemCount());
            oldOrder.setCarerEmployeeNo("");
            oldOrder.setCarerName("");
            oldOrder.setCarerMobile("");
            oldOrder.setStatus(PscOrder.STATUS_INIT);
            oldOrder.setEndTime(null);
            oldOrder.setConfirmTime(null);
            oldOrder.setScore(null);
            pscOrderService.insertPscOrder(oldOrder);
        }

        return AjaxResult.success();
    }

}
