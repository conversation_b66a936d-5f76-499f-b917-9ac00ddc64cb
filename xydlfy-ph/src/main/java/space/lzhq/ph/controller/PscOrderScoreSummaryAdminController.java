package space.lzhq.ph.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.PscOrderScoreSummary;
import space.lzhq.ph.service.IPscOrderScoreSummaryService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/ph/pscOrderScoreSummary")
public class PscOrderScoreSummaryAdminController extends BaseController {

    private final IPscOrderScoreSummaryService service;

    public PscOrderScoreSummaryAdminController(IPscOrderScoreSummaryService service) {
        this.service = service;
    }

    @RequiresPermissions("ph:pscOrderScoreSummary:view")
    @GetMapping
    public String page() {
        return "ph/pscOrderScoreSummary/index";
    }

    @RequiresPermissions("ph:pscOrderScoreSummary:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate minDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate maxDate
    ) {
        LocalDateTime minConfirmTime = minDate.atStartOfDay();
        LocalDateTime maxConfirmTime = maxDate.atTime(LocalTime.MAX);
        List<PscOrderScoreSummary> list = service.summary(minConfirmTime, maxConfirmTime);
        return getDataTable(list);
    }


    @RequiresPermissions("ph:pscOrderScoreSummary:export")
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate minDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate maxDate
    ) {
        LocalDateTime minConfirmTime = minDate.atStartOfDay();
        LocalDateTime maxConfirmTime = maxDate.atTime(LocalTime.MAX);
        List<PscOrderScoreSummary> list = service.summary(minConfirmTime, maxConfirmTime);
        ExcelUtil<PscOrderScoreSummary> util = new ExcelUtil<>(PscOrderScoreSummary.class);
        return util.exportFastExcel(list, "满意度统计表");
    }

}
