package space.lzhq.ph.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayOrderQueryResult;
import com.github.binarywang.wxpay.bean.result.WxInsurancePayRefundResult;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.dromara.hutool.core.exception.ExceptionUtil;
import org.dromara.hutool.core.text.StrValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import space.lzhq.ph.domain.MipWxOrder;
import space.lzhq.ph.ext.WeixinExt;
import space.lzhq.ph.service.IMipWxOrderService;

import java.util.List;


@Controller
@RequestMapping("/ph/mipWxOrder")
public class MipWxOrderController extends BaseController {
    private String prefix = "ph/mipWxOrder";

    private IMipWxOrderService mipWxOrderService;

    @Autowired
    public MipWxOrderController(IMipWxOrderService mipWxOrderService) {
        this.mipWxOrderService = mipWxOrderService;
    }

    @RequiresPermissions("ph:mipWxOrder:view")
    @GetMapping()
    public String mipWxOrder() {
        return prefix + "/mipWxOrder";
    }

    /**
     * 查询微信医保订单列表
     */
    @RequiresPermissions("ph:mipWxOrder:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(MipWxOrder mipWxOrder) {
        startPage("id desc");
        List<MipWxOrder> list = mipWxOrderService.list(new QueryWrapper<>(mipWxOrder));
        return getDataTable(list);
    }

    /**
     * 导出微信医保订单列表
     */
    @RequiresPermissions("ph:mipWxOrder:export")
    @Log(title = "微信医保订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(MipWxOrder mipWxOrder) {
        List<MipWxOrder> list = mipWxOrderService.list(new QueryWrapper<>(mipWxOrder));
        ExcelUtil<MipWxOrder> util = new ExcelUtil<>(MipWxOrder.class);
        return util.exportExcel(list, "mipWxOrder");
    }

    @RequiresPermissions("ph:mipWxOrder:queryTransaction")
    @GetMapping("/queryTransaction/{id}")
    @ResponseBody
    public AjaxResult queryTransaction(@PathVariable Long id) {
        MipWxOrder mipWxOrder = mipWxOrderService.getById(id);
        if (mipWxOrder == null) {
            return AjaxResult.error("订单不存在或已删除");
        }
        if (StrValidator.isBlank(mipWxOrder.getMedTransactionId())) {
            return AjaxResult.error("诊疗单ID为空，说明此单下单失败");
        }

        try {
            WxInsurancePayOrderQueryResult wxInsurancePayOrderQueryResult = WeixinExt.INSTANCE.getWxInsurancePayService().queryOrder(
                    mipWxOrder.getMedTransactionId(),
                    ""
            );
            return AjaxResult.success(wxInsurancePayOrderQueryResult);
        } catch (Exception e) {
            return AjaxResult.error(ExceptionUtil.stacktraceToString(e));
        }
    }

    @RequiresPermissions("ph:mipWxOrder:refundWeixin")
    @PostMapping("/refundWeixin/{id}")
    @ResponseBody
    public AjaxResult refundWeixin(@PathVariable Long id) {
        MipWxOrder mipWxOrder = mipWxOrderService.getById(id);
        if (mipWxOrder == null) {
            return AjaxResult.error("订单不存在");
        }

        try {
            WxInsurancePayRefundResult refundResult = WeixinExt.INSTANCE.refundWeixin(mipWxOrder);
            return AjaxResult.success(refundResult);
        } catch (Exception e) {
            return AjaxResult.error(ExceptionUtil.stacktraceToString(e));
        }
    }
}
