package space.lzhq.ph.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.dto.response.attachment.AttachmentDto;
import space.lzhq.ph.enums.MedicalRecordApplicationStatus;
import space.lzhq.ph.enums.MedicalRecordCopyPurpose;
import space.lzhq.ph.enums.Relationship;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MedicalRecordApplicationDetailResponseDto {

    /**
     * ID
     */
    private String id;

    /**
     * 用户OpenID
     */
    private String openId;

    /**
     * 住院号
     */
    private String admissionId;

    /**
     * 患者身份证附件
     */
    private AttachmentDto patientIdCardAttachment;

    /**
     * 患者出生证附件
     */
    private AttachmentDto patientBirthCertificateAttachment;

    /**
     * 申报患者身份证号
     */
    private String patientIdCardNo;

    /**
     * 申报患者姓名
     */
    private String patientName;

    /**
     * 申报患者性别
     */
    private String patientGender;

    /**
     * 申报患者出生日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private LocalDate patientBirthDate;

    /**
     * 患者手机号
     */
    private String patientPhone;

    /**
     * 入院时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime admissionTime;

    /**
     * 出院时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dischargeTime;

    /**
     * 复印用途
     */
    private MedicalRecordCopyPurpose copyPurpose;

    /**
     * 复印份数
     */
    private Integer copyCount;

    /**
     * 病区
     */
    private String ward;

    /**
     * 是否代办
     */
    private Integer agentFlag;

    /**
     * 代办人姓名
     */
    private String agentName;

    /**
     * 代办人身份证号
     */
    private String agentIdCardNo;

    /**
     * 代办人身份证附件
     */
    private AttachmentDto agentIdCardAttachment;

    /**
     * 代办人手机号
     */
    private String agentMobile;

    /**
     * 代办人与患者关系
     */
    private Relationship agentRelation;

    /**
     * 申报状态
     */
    private MedicalRecordApplicationStatus status;

    /**
     * 提交时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    /**
     * 审批时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 整改意见
     */
    private String correctionAdvice;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 收件人姓名
     */
    private String recipientName;

    /**
     * 收件人手机号
     */
    private String recipientMobile;

    /**
     * 收件人地址
     */
    private String recipientAddress;

    /**
     * 复印费用（元）
     */
    private java.math.BigDecimal copyFee;

    /**
     * 快递费用（元）
     */
    private java.math.BigDecimal shippingFee;

    /**
     * 总费用（元）
     */
    private java.math.BigDecimal totalFee;

    /**
     * 快递单号
     */
    private String trackingNumber;

    /**
     * 掌医支付订单号
     */
    private String zyPayNo;

    /**
     * 微信支付订单号
     */
    private String wxPayNo;

    /**
     * 支付时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    /**
     * 邮寄时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shippingTime;

    /**
     * 是否可以审批
     */
    private Boolean canApprove;

    /**
     * 是否可以邮寄
     */
    private Boolean canShip;

    /**
     * 是否可以删除
     */
    private Boolean canDelete;

}
