package space.lzhq.ph.dto.response.attachment;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.enums.AttachmentType;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttachmentDto {

    /**
     * 附件ID
     */
    private String id;

    /**
     * 用户OpenID
     */
    private String openId;

    /**
     * 关联的主表ID
     */
    private String masterId;

    /**
     * 附件类型
     */
    private AttachmentType type;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
