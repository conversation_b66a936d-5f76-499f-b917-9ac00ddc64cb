package space.lzhq.ph.dto.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import space.lzhq.ph.domain.MedicalRecordApplication;
import space.lzhq.ph.dto.response.MedicalRecordApplicationDetailResponseDto;
import space.lzhq.ph.dto.response.MedicalRecordApplicationListResponseDto;

@Mapper
public interface MedicalRecordApplicationMapper {

    MedicalRecordApplicationMapper INSTANCE = Mappers.getMapper(MedicalRecordApplicationMapper.class);

    @Mapping(target = "patientIdCardAttachment", ignore = true)
    @Mapping(target = "patientBirthCertificateAttachment", ignore = true)
    @Mapping(target = "agentIdCardAttachment", ignore = true)
    @Mapping(target = "canApprove", ignore = true)
    @Mapping(target = "canShip", ignore = true)
    @Mapping(target = "canDelete", ignore = true)
    MedicalRecordApplicationDetailResponseDto toDetailResponseDto(MedicalRecordApplication application);

    MedicalRecordApplicationListResponseDto toListResponseDto(MedicalRecordApplication application);

}
