package space.lzhq.ph.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.xss.Xss;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import space.lzhq.ph.enums.MedicalRecordCopyPurpose;

import java.io.Serial;
import java.io.Serializable;

@Data
public class MedicalRecordApplicationPatientFormDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 患者身份证附件ID
     */
    private String patientIdCardAttachmentId;

    /**
     * 患者出生证附件ID
     */
    private String patientBirthCertificateAttachmentId;

    /**
     * 患者身份证号
     */
    private String patientIdCardNo;

    /**
     * 申报患者姓名
     */
    @NotBlank(message = "申报患者姓名不能为空")
    @Xss(message = "申报患者姓名不能包含特殊字符")
    private String patientName;

    /**
     * 患者token
     */
    @NotBlank(message = "患者token不能为空")
    private String patientToken;

    /**
     * 复印用途
     */
    @NotNull(message = "复印用途不能为空")
    private MedicalRecordCopyPurpose copyPurpose;

    /**
     * 复印份数
     */
    @NotNull(message = "复印份数不能为空")
    @Min(value = 1, message = "复印份数不能少于1份")
    @Max(value = 10, message = "复印份数不能超过10份")
    private Integer copyCount;

    @JsonIgnore
    @AssertTrue(message = "患者出生证附件ID和身份证附件ID不能同时为空")
    public boolean isPatientAttachmentIdValid() {
        return StringUtils.isNotBlank(patientIdCardAttachmentId) || StringUtils.isNotBlank(patientBirthCertificateAttachmentId);
    }

}
