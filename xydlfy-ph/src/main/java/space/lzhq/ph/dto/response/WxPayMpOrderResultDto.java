package space.lzhq.ph.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class WxPayMpOrderResultDto {

    private String appId;
    private String timeStamp;
    private String nonceStr;
    /**
     * 由于package为java保留关键字，因此改为packageValue. 前端使用时记得要更改为package
     */
    @JsonProperty("package")
    private String packageValue;
    private String signType;
    private String paySign;
    private String zyOrderNo;

    public WxPayMpOrderResultDto(@NotNull WxPayMpOrderResult wxPayMpOrderResult, @NotEmpty String zyOrderNo) {
        this.appId = wxPayMpOrderResult.getAppId();
        this.timeStamp = wxPayMpOrderResult.getTimeStamp();
        this.nonceStr = wxPayMpOrderResult.getNonceStr();
        this.packageValue = wxPayMpOrderResult.getPackageValue();
        this.signType = wxPayMpOrderResult.getSignType();
        this.paySign = wxPayMpOrderResult.getPaySign();
        this.zyOrderNo = zyOrderNo;
    }

}
