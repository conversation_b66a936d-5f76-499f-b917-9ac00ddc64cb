package space.lzhq.ph.dto.response;

import com.fasterxml.jackson.annotation.JsonAnyGetter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 枚举定义响应DTO
 * 用于返回多个枚举类型的完整定义信息
 */
public class EnumDefinitionResponseDto {

    @JsonAnyGetter
    private final Map<String, List<EnumItemDto>> enums = new HashMap<>();

    /**
     * 添加枚举定义
     *
     * @param enumName 枚举类型简名
     * @param items    枚举项列表
     */
    public void addEnum(String enumName, List<EnumItemDto> items) {
        enums.put(enumName, items);
    }

} 