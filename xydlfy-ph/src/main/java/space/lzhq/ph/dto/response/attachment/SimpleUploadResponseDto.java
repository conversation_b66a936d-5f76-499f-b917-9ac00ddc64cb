package space.lzhq.ph.dto.response.attachment;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import space.lzhq.ph.domain.Attachment;
import space.lzhq.ph.dto.mapper.AttachmentMapper;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SimpleUploadResponseDto {

    private AttachmentDto attachment;

    /**
     * 从域实体创建响应 DTO
     */
    public static SimpleUploadResponseDto fromDomain(Attachment domain) {
        if (domain == null) {
            throw new IllegalArgumentException("Domain entity cannot be null");
        }

        AttachmentDto attachmentDto = AttachmentMapper.INSTANCE.toDto(domain);
        return new SimpleUploadResponseDto(attachmentDto);
    }

}
