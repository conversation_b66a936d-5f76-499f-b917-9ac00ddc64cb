package space.lzhq.ph.ext

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class HisExtTest {

    /**
     * 测试 HisExt.encryptForQrCode 方法
     * 测试就诊卡和虚拟卡的加密结果是否符合预期
     */
    @ParameterizedTest
    @CsvSource(
        value = [
            "**********, ETlLfbP9EzQl33DhkR/tmU6g==",
            "**********, ETDdVYb3lsXeYuz7Itw23PPg==",
            "XN25040, ETpM0Z0sBZxpj+0NniUm6LeA==",
            "XN23782, ET9h1zbougNhfVm7jpS6Fptg=="
        ]
    )
    fun testEncryptForQrCode(input: String, expectedOutput: String) {
        // 执行加密操作
        val result = HisExt.encryptForQrCode(input)

        // 验证结果是否符合预期
        assertEquals(expectedOutput, result, "加密结果与预期不符")
    }

    /**
     * 测试就诊卡加密
     */
    @Test
    fun testEncryptJiuZhenCard() {
        // 测试就诊卡号 **********
        val cardNo = "**********"
        val expected = "ETlLfbP9EzQl33DhkR/tmU6g=="
        val result = HisExt.encryptForQrCode(cardNo)
        assertEquals(expected, result, "就诊卡 $cardNo 加密结果与预期不符")

        // 测试就诊卡号 **********
        val cardNo2 = "**********"
        val expected2 = "ETDdVYb3lsXeYuz7Itw23PPg=="
        val result2 = HisExt.encryptForQrCode(cardNo2)
        assertEquals(expected2, result2, "就诊卡 $cardNo2 加密结果与预期不符")
    }

    /**
     * 测试虚拟卡加密
     */
    @Test
    fun testEncryptVirtualCard() {
        // 测试虚拟卡号 XN25040
        val cardNo = "XN25040"
        val expected = "ETpM0Z0sBZxpj+0NniUm6LeA=="
        val result = HisExt.encryptForQrCode(cardNo)
        assertEquals(expected, result, "虚拟卡 $cardNo 加密结果与预期不符")

        // 测试虚拟卡号 XN23782
        val cardNo2 = "XN23782"
        val expected2 = "ET9h1zbougNhfVm7jpS6Fptg=="
        val result2 = HisExt.encryptForQrCode(cardNo2)
        assertEquals(expected2, result2, "虚拟卡 $cardNo2 加密结果与预期不符")
    }
}