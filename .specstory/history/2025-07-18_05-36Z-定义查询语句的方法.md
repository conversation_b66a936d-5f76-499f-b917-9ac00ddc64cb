<!-- Generated by SpecStory -->

# 定义查询语句的方法 (2025-07-18 05:36Z)

_**User**_

为以下查询语句定义方法：
```
SELECT DISTINCT
报告链接
FROM medexmemrstemp.dbo. Report_2Y
where 申请单号='822738'
```

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户提供了一个SQL查询语句，需要为这个查询在PacsReportMapper.java中定义相应的方法。

查看SQL语句：
```sql
SELECT DISTINCT
报告链接
FROM medexmemrstemp.dbo. Report_2Y
where 申请单号='822738'
```

这是一个查询语句，从`medexmemrstemp.dbo.Report_2Y`表中查询不重复的`报告链接`，条件是`申请单号='822738'`。

我需要：
1. 在PacsReportMapper接口中添加一个方法
2. 方法应该接受申请单号作为参数
3. 返回类型应该是List<String>，因为查询的是报告链接字段，而且使用了DISTINCT
4. 使用MyBatis的@Select注解来定义查询

让我查看当前的PacsReportMapper.java文件，看起来是一个空的Mapper接口。我需要添加相应的方法。

另外，我还看到有一个PacsReport.java的domain类文件是untracked的，可能需要先了解一下这个domain类的结构。

让我先添加查询方法到Mapper中。</details></think>

---

我将为您在PacsReportMapper.java中添加对应的查询方法。根据您提供的SQL语句，这是一个根据申请单号查询报告链接的方法。

---

```diff
  
  import org.apache.ibatis.annotations.Mapper;
+ import org.apache.ibatis.annotations.Select;
+ import org.apache.ibatis.annotations.Param;
+ import java.util.List;
  
  @Mapper
  public interface PacsReportMapper {
  
-     
+     /**
+      * 根据申请单号查询报告链接
+      * @param applicationNo 申请单号
+      * @return 报告链接列表
+      */
+     @Select("SELECT DISTINCT 报告链接 FROM medexmemrstemp.dbo.Report_2Y WHERE 申请单号 = #{applicationNo}")
+     List<String> selectReportLinksByApplicationNo(@Param("applicationNo") String applicationNo);
  
  }
```

---

<think><details><summary>Thought Process</summary>很好，我成功为PacsReportMapper添加了对应的查询方法。让我总结一下我做了什么：

1. 添加了必要的import语句：
   - `@Select` 注解用于直接在接口方法上定义SQL
   - `@Param` 注解用于指定参数名称
   - `List` 接口用于返回类型

2. 添加了方法 `selectReportLinksByApplicationNo`：
   - 接受一个String类型的参数 `applicationNo`（申请单号）
   - 返回 `List<String>` 类型，因为查询结果是报告链接的列表
   - 使用了DISTINCT确保不重复
   - 使用`@Param`注解指定参数名称为"applicationNo"

3. 添加了完整的JavaDoc注释说明方法用途

SQL语句已经正确映射，参数使用MyBatis的 `#{applicationNo}` 语法进行参数绑定。

现在用户可以在Service层中注入这个Mapper并调用这个方法来查询报告链接了。</details></think>

---

已成功为您在PacsReportMapper中添加了对应的查询方法。具体实现如下：

## 添加的方法说明

**方法名**: `selectReportLinksByApplicationNo`

**功能**: 根据申请单号查询不重复的报告链接列表

**参数**: 
- `applicationNo` (String): 申请单号

**返回值**: 
- `List<String>`: 报告链接列表

## 技术实现细节

1. **使用@Select注解**: 直接在接口方法上定义SQL查询，避免了创建额外的XML映射文件
2. **参数绑定**: 使用`@Param("applicationNo")`注解确保参数正确传递到SQL中
3. **返回类型**: 使用`List<String>`因为查询结果是字符串列表，且使用了DISTINCT去重

现在您可以在Service层中注入这个Mapper并调用此方法：

```java
@Autowired
private PacsReportMapper pacsReportMapper;

public List<String> getReportLinks(String applicationNo) {
    return pacsReportMapper.selectReportLinksByApplicationNo(applicationNo);
}
```

---

_**User**_

为申请单号添加校验规则，必须是4-10位数字

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要为申请单号添加校验规则，要求必须是4-10位数字。我看到他们选择了service接口中的方法，该方法已经有一个@NotBlank的校验，现在需要添加数字长度的校验。

我需要为申请单号参数添加正则表达式校验，使用@Pattern注解来确保申请单号是4-10位数字。

我需要先看看当前的文件内容，然后修改参数的校验注解。</details></think>

---

我将为申请单号参数添加数字长度校验规则。让我先查看当前的Service接口文件：

---

Read file: xydlfy-ph/src/main/java/space/lzhq/ph/service/IPacsReportService.java

---

<think><details><summary>Thought Process</summary>现在我看到了完整的Service接口文件。我需要：

1. 添加@Pattern注解的导入
2. 修改方法参数，添加@Pattern校验，确保申请单号是4-10位数字

正则表达式应该是：`^\d{4,10}$`
- `^` - 字符串开始
- `\d{4,10}` - 4到10位数字
- `$` - 字符串结束</details></think>

---

```diff
  import com.baomidou.mybatisplus.extension.service.IService;
  import jakarta.validation.constraints.NotBlank;
+ import jakarta.validation.constraints.Pattern;
  import space.lzhq.ph.domain.PacsReport;
  import java.util.List;
  
  public interface IPacsReportService extends IService<PacsReport> {
  
-     List<String> selectReportLinksByApplicationNo(@NotBlank(message = "申请单号不能为空") String applicationNo);
+     List<String> selectReportLinksByApplicationNo(
+             @NotBlank(message = "申请单号不能为空") 
+             @Pattern(regexp = "^\\d{4,10}$", message = "申请单号必须是4-10位数字") 
+             String applicationNo);
  
  }
```

---

<think><details><summary>Thought Process</summary>现在我还应该检查Mapper接口是否也需要添加同样的校验规则，以保持一致性。让我检查一下Mapper文件：</details></think>

---

现在让我也为Mapper接口添加相同的校验规则，保持一致性：

---

```diff
  import org.apache.ibatis.annotations.Select;
  import org.apache.ibatis.annotations.Param;
+ import jakarta.validation.constraints.NotBlank;
+ import jakarta.validation.constraints.Pattern;
  import java.util.List;
  
```

```diff
       */
      @Select("SELECT DISTINCT 报告链接 FROM medexmemrstemp.dbo.Report_2Y WHERE 申请单号 = #{applicationNo}")
-     List<String> selectReportLinksByApplicationNo(@Param("applicationNo") String applicationNo);
+     List<String> selectReportLinksByApplicationNo(
+             @Param("applicationNo") 
+             @NotBlank(message = "申请单号不能为空")
+             @Pattern(regexp = "^\\d{4,10}$", message = "申请单号必须是4-10位数字")
+             String applicationNo);
  
  }
```

---

<think><details><summary>Thought Process</summary>完成了校验规则的添加。让我总结一下我做了什么：

1. 为Service接口添加了@Pattern校验注解
2. 为Mapper接口也添加了相同的校验注解
3. 添加了必要的import语句

校验规则使用正则表达式`^\\d{4,10}$`确保申请单号必须是4-10位数字。</details></think>

---

已成功为申请单号添加了校验规则。具体修改如下：

## 添加的校验规则

**正则表达式**: `^\\d{4,10}$`
- `^` - 字符串开始 
- `\\d{4,10}` - 4到10位数字
- `$` - 字符串结束

**错误提示**: "申请单号必须是4-10位数字"

## 修改的文件

### 1. IPacsReportService.java
- 添加了 `@Pattern` 注解导入
- 为方法参数添加了数字长度校验

### 2. PacsReportMapper.java  
- 添加了 `@NotBlank` 和 `@Pattern` 注解导入
- 为Mapper方法参数添加了相同的校验规则，保持一致性

## 校验效果

现在当调用这些方法时，如果申请单号不符合以下条件之一，将抛出校验异常：

1. **不能为空** - 由 `@NotBlank` 校验
2. **必须是4-10位数字** - 由 `@Pattern` 校验

例如：
- ✅ 有效: "1234", "822738", "1234567890"
- ❌ 无效: "123" (少于4位), "12345678901" (超过10位), "abc123" (包含非数字), "" (空字符串)

这样可以确保数据的完整性和一致性，防止无效的申请单号进入查询流程。

---

