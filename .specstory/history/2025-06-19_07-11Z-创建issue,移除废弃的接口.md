<!-- Generated by SpecStory -->

# 创建Issue，移除废弃的接口 (2025-06-19 07:11Z)

_**User**_

创建Issue，移除废弃的接口

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要创建一个GitHub Issue来移除废弃的接口。从附件文件中，我可以看到在ZhenjianController.kt中有几个被标记为@Deprecated的方法：

1. `createWxpayOrder` - 已废弃，请使用 checkBalanceAndCreateWeixinRechargeOrder 方法
2. `createAlipayOrder` - 已废弃，请使用 checkBalanceAndCreateAlipayRechargeOrder 方法  
3. `refund` - 已废弃，留待以后删除

从文档中也可以看到这些废弃接口的信息。

我需要：
1. 创建一个优美易读的Issue内容，包含废弃接口的详细信息
2. 使用临时文件的方式创建Issue（根据memory）
3. 使用适当的markdown格式和emoji图标

让我先创建Issue内容，然后使用gh issue create命令创建。</details></think>

---

我来帮您创建一个GitHub Issue来跟踪移除废弃接口的任务。首先让我检查一下当前代码中的废弃接口情况。

---

<details>
            <summary>Grep search for "@Deprecated" • **4** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/DictApiController.kt` | L15 | `@Deprecated("此接口返回值不是标准的 AjaxResult，使用 findByType 替代")` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/PatientApiController.kt` | L396 | `* @deprecated 此接口已废弃，请使用 switchZhuyuanNoV2 接口，该接口使用姓名参数替代身份证号，更适合儿童患者群体` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/PatientApiController.kt` | L400 | `@Deprecated("使用 switchZhuyuanNoV2 接口替代，该接口使用姓名参数替代身份证号")` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhenjianController.kt` | L74 | `* @deprecated 已废弃，请使用 checkBalanceAndCreateWeixinRechargeOrder 方法` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhenjianController.kt` | L76 | `@Deprecated("已废弃，请使用 checkBalanceAndCreateWeixinRechargeOrder 方法")` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhenjianController.kt` | L351 | `* @deprecated 已废弃，请使用 checkBalanceAndCreateAlipayRechargeOrder 方法` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhenjianController.kt` | L353 | `@Deprecated("已废弃，请使用 checkBalanceAndCreateAlipayRechargeOrder 方法")` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhenjianController.kt` | L412 | `* @deprecated 已废弃，留待以后删除` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhenjianController.kt` | L414 | `@Deprecated("已废弃，留待以后删除")` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhenjianController.kt` | L74 | `* @deprecated 已废弃，请使用 checkBalanceAndCreateWeixinRechargeOrder 方法` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhenjianController.kt` | L76 | `@Deprecated("已废弃，请使用 checkBalanceAndCreateWeixinRechargeOrder 方法")` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhenjianController.kt` | L351 | `* @deprecated 已废弃，请使用 checkBalanceAndCreateAlipayRechargeOrder 方法` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhenjianController.kt` | L353 | `@Deprecated("已废弃，请使用 checkBalanceAndCreateAlipayRechargeOrder 方法")` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhenjianController.kt` | L412 | `* @deprecated 已废弃，留待以后删除` |
| `file:///Users/<USER>/Developer/Projects/zhangyi-xjetyy/xydlfy-ph/src/main/kotlin/space/lzhq/ph/controller/ZhenjianController.kt` | L414 | `@Deprecated("已废弃，留待以后删除")` |

</details>

---

现在我来创建一个优美易读的GitHub Issue。我会[按照记忆中的规范，使用临时文件的方式创建Issue][[memory:6253006901143721458]]。

---

```diff
- 
+ # 🧹 移除代码中的废弃接口
+ 
+ ## 📋 概述
+ 
+ 随着系统的持续迭代和改进，代码中积累了一些已标记为 `@Deprecated` 的废弃接口。为了保持代码库的整洁和可维护性，需要清理这些废弃的接口和方法。
+ 
+ ## 🎯 目标
+ 
+ - 移除所有标记为 `@Deprecated` 的废弃接口
+ - 确保没有其他代码仍在使用这些废弃接口
+ - 更新相关文档，移除对废弃接口的引用
+ 
+ ## 📝 待移除的废弃接口清单
+ 
+ ### 1. 诊间支付相关接口 (ZhenjianController.kt)
+ 
+ #### 🔄 已有替代方案的接口
+ - **`createWxpayOrder`** 
+   - 📍 位置: `/api/zhenjian/createWxpayOrder`
+   - 🔄 替代方案: `checkBalanceAndCreateWeixinRechargeOrder`
+   - 📝 废弃原因: 已有更安全的充值机制替代
+ 
+ - **`createAlipayOrder`**
+   - 📍 位置: `/api/zhenjian/createAlipayOrder`  
+   - 🔄 替代方案: `checkBalanceAndCreateAlipayRechargeOrder`
+   - 📝 废弃原因: 已有更安全的充值机制替代
+ 
+ #### ❌ 无替代方案的接口
+ - **`refund`**
+   - 📍 位置: `/api/zhenjian/refund`
+   - 🗑️ 处理方式: 直接删除
+   - 📝 废弃原因: 功能不再需要
+ 
+ ### 2. 患者相关接口 (PatientApiController.kt)
+ 
+ - **`switchZhuyuanNo`**
+   - 📍 位置: 患者切换住院号接口
+   - 🔄 替代方案: `switchZhuyuanNoV2`
+   - 📝 废弃原因: 新版本使用姓名参数替代身份证号，更适合儿童患者
+ 
+ ### 3. 字典接口 (DictApiController.kt)
+ 
+ - **未命名废弃接口**
+   - 📍 位置: 第15行
+   - 🔄 替代方案: `findByType`
+   - 📝 废弃原因: 返回值不是标准的 AjaxResult
+ 
+ ## ✅ 执行步骤
+ 
+ ### 第一阶段：影响分析
+ - [ ] 检查是否有其他代码仍在调用这些废弃接口
+ - [ ] 检查前端代码中是否有对这些接口的调用
+ - [ ] 确认所有调用方已迁移到新接口
+ 
+ ### 第二阶段：代码清理
+ - [ ] 移除 `ZhenjianController.kt` 中的废弃方法
+   - [ ] `createWxpayOrder`
+   - [ ] `createAlipayOrder` 
+   - [ ] `refund`
+ - [ ] 移除 `PatientApiController.kt` 中的废弃方法
+   - [ ] `switchZhuyuanNo`
+ - [ ] 移除 `DictApiController.kt` 中的废弃方法
+ 
+ ### 第三阶段：文档更新
+ - [ ] 更新 API 文档，移除废弃接口的描述
+ - [ ] 更新迁移指南文档
+ - [ ] 检查是否有其他文档需要更新
+ 
+ ### 第四阶段：测试验证
+ - [ ] 运行单元测试确保没有破坏现有功能
+ - [ ] 进行集成测试验证系统正常运行
+ - [ ] 确认前端应用仍能正常工作
+ 
+ ## ⚠️ 注意事项
+ 
+ 1. **向前兼容性**: 在移除接口前，务必确认没有客户端仍在使用这些接口
+ 2. **分批执行**: 可以考虑分批移除，先移除确认无人使用的接口
+ 3. **版本记录**: 在版本说明中记录接口的移除信息
+ 4. **回滚准备**: 准备回滚计划，以防出现意外问题
+ 
+ ## 🔗 相关文档
+ 
+ - [诊间充值接口文档](docs/zhenjian-recharge-api.md)
+ - API 迁移指南（如有）
+ 
+ ## 💡 建议
+ 
+ 建议按优先级分批执行：
+ 1. **高优先级**: 已有明确替代方案的接口
+ 2. **中优先级**: 功能不再需要的接口  
+ 3. **低优先级**: 需要进一步确认影响范围的接口
+ 
+ ---
+ 
+ **标签**: `enhancement`, `cleanup`, `api`, `deprecated` 
```

---

现在使用GitHub CLI创建Issue：

---

```bash
gh issue create --title "🧹 移除代码中的废弃接口" --body-file issue_content.md --label "enhancement,cleanup,api,deprecated"
```

```
rochuukyou@luzhongqiangdeiMac ~/D/P/zhangyi-xjetyy (master)> gh issue create --title "🧹 移除代码中的废弃接
口" --body-file issue_content.md --label "enhancement,cleanup,api,deprecated"

                                                             gh issue create --title "🧹 移除代码中的废弃接
口" --body-file issue_content.md --label "enhancement,cleanup,api,deprecated"



Creating issue in htgylzhq/zhangyi-xjetyy

could not add label: 'api' not found
rochuukyou@luzhongqiangdeiMac ~/D/P/zhangyi-xjetyy (master) [1]>                                     (base)
```

---

看到标签 'api' 不存在的警告。让我[按照记忆中的做法，先创建缺失的标签][[memory:7815919221601323017]]，然后重新创建Issue：

---

```bash
gh label create api --description "API相关的问题和改进" --color "0052CC"
```

```
rochuukyou@luzhongqiangdeiMac ~/D/P/zhangyi-xjetyy (master) [1]> gh label create api --description "API相关

✓ Label "api" created in htgylzhq/zhangyi-xjetyy
rochuukyou@luzhongqiangdeiMac ~/D/P/zhangyi-xjetyy (master)>                                         (base)
```

---

```bash
gh label create deprecated --description "废弃功能相关" --color "D93F0B"
```

```
rochuukyou@luzhongqiangdeiMac ~/D/P/zhangyi-xjetyy (master)> gh label create deprecated --description "废弃
功能相关" --color "D93F0B"
                                                             gh label create deprecated --description "废弃
功能相关" --color "D93F0B"

✓ Label "deprecated" created in htgylzhq/zhangyi-xjetyy
rochuukyou@luzhongqiangdeiMac ~/D/P/zhangyi-xjetyy (master)>                                         (base)
```

---

```bash
gh label create cleanup --description "代码清理和重构" --color "5319E7"
```

```
rochuukyou@luzhongqiangdeiMac ~/D/P/zhangyi-xjetyy (master)> gh label create cleanup --description "代码清理
和重构" --color "5319E7"
                                                             gh label create cleanup --description "代码清理
和重构" --color "5319E7"

label with name "cleanup" already exists; use `--force` to update its color and description
rochuukyou@luzhongqiangdeiMac ~/D/P/zhangyi-xjetyy (master) [1]>                                     (base)
```

---

很好，现在标签都已创建。让我重新创建Issue：

---

```bash
gh issue create --title "🧹 移除代码中的废弃接口" --body-file issue_content.md --label "enhancement,cleanup,api,deprecated"
```

```
rochuukyou@luzhongqiangdeiMac ~/D/P/zhangyi-xjetyy (master) [1]> gh issue create --title "🧹 移除代码中的废
弃接口" --body-file issue_content.md --label "enhancement,cleanup,api,deprecated"

                                                                 gh issue create --title "🧹 移除代码中的废
弃接口" --body-file issue_content.md --label "enhancement,cleanup,api,deprecated"


Creating issue in htgylzhq/zhangyi-xjetyy

https://github.com/htgylzhq/zhangyi-xjetyy/issues/13
rochuukyou@luzhongqiangdeiMac ~/D/P/zhangyi-xjetyy (master)>                                         (base)
```

---

<think><details><summary>Thought Process</summary>用户想要创建一个GitHub Issue来移除废弃的接口。从附件文件中，我可以看到在ZhenjianController.kt中有几个被标记为@Deprecated的方法：

1. `createWxpayOrder` - 已废弃，请使用 checkBalanceAndCreateWeixinRechargeOrder 方法
2. `createAlipayOrder` - 已废弃，请使用 checkBalanceAndCreateAlipayRechargeOrder 方法  
3. `refund` - 已废弃，留待以后删除

从文档中也可以看到这些废弃接口的信息。

我需要：
1. 创建一个优美易读的Issue内容，包含废弃接口的详细信息
2. 使用临时文件的方式创建Issue（根据memory）
3. 使用适当的markdown格式和emoji图标

让我先创建Issue内容，然后使用gh issue create命令创建。</details></think>

---

现在清理临时文件：

---

删除创建Issue时使用的临时文件

---

<think><details><summary>Thought Process</summary>用户想要创建一个GitHub Issue来移除废弃的接口。从附件文件中，我可以看到在ZhenjianController.kt中有几个被标记为@Deprecated的方法：

1. `createWxpayOrder` - 已废弃，请使用 checkBalanceAndCreateWeixinRechargeOrder 方法
2. `createAlipayOrder` - 已废弃，请使用 checkBalanceAndCreateAlipayRechargeOrder 方法  
3. `refund` - 已废弃，留待以后删除

从文档中也可以看到这些废弃接口的信息。

我需要：
1. 创建一个优美易读的Issue内容，包含废弃接口的详细信息
2. 使用临时文件的方式创建Issue（根据memory）
3. 使用适当的markdown格式和emoji图标

让我先创建Issue内容，然后使用gh issue create命令创建。</details></think>

---

✅ **GitHub Issue 创建成功！**

我已经成功创建了一个关于移除废弃接口的Issue：

🔗 **Issue链接**: [#13 - 🧹 移除代码中的废弃接口](https://github.com/htgylzhq/zhangyi-xjetyy/issues/13)

## 📋 Issue内容概要

这个Issue详细列出了所有需要移除的废弃接口，包括：

### 🎯 主要的废弃接口
1. **诊间支付相关** (`ZhenjianController.kt`)
   - `createWxpayOrder` → 替换为 `checkBalanceAndCreateWeixinRechargeOrder`
   - `createAlipayOrder` → 替换为 `checkBalanceAndCreateAlipayRechargeOrder`
   - `refund` → 直接删除

2. **患者相关** (`PatientApiController.kt`)
   - `switchZhuyuanNo` → 替换为 `switchZhuyuanNoV2`

3. **字典接口** (`DictApiController.kt`)
   - 废弃接口 → 替换为 `findByType`

### 🏷️ 标签设置
- `enhancement` - 功能改进
- `cleanup` - 代码清理
- `api` - API相关
- `deprecated` - 废弃功能

### 📝 执行计划
Issue中包含了详细的四阶段执行计划：
1. **影响分析** - 检查依赖和调用情况
2. **代码清理** - 移除废弃方法
3. **文档更新** - 更新相关文档
4. **测试验证** - 确保功能正常

这个Issue将帮助团队有序地移除这些废弃接口，保持代码库的整洁和可维护性。

---

