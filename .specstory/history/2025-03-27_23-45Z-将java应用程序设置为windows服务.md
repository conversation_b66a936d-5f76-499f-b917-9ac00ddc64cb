<!-- Generated by SpecStory -->

# 将Java应用程序设置为Windows服务 (2025-03-27 23:45Z)

_**User**_

详细说明把这个脚本制作成Windows服务并开机自动启动的操作步骤

---

_**Assistant**_

我来帮您说明如何将这个Java应用程序设置为Windows服务并实现开机自动启动。以下是详细步骤：

1. 首先，我们需要使用NSSM (Non-Sucking Service Manager) 工具，这是一个很好的Windows服务管理工具：
   - 从官网下载NSSM: https://nssm.cc/download
   - 解压下载的文件，根据系统选择32位或64位版本的nssm.exe

2. 安装服务步骤：
   - 以管理员身份打开命令提示符(CMD)
   - 进入nssm.exe所在目录
   - 执行以下命令打开NSSM服务安装界面：
   ```batch
   nssm install YourServiceName
   ```

3. 在NSSM图形界面中配置：
   - Application标签页：
     - Path: 填入java.exe的完整路径（如：C:\Program Files\Java\jdk1.8.0_xxx\bin\java.exe）
     - Startup directory: 填入你的jar包所在目录
     - Arguments: 填入：`-jar -Dspring.profiles.active=prod -Dlog4j2.formatMsgNoLookups=true zyxcx.jar`

   - Details标签页：
     - Display name: 填入服务显示名称
     - Description: 填入服务描述
     - Startup type: Automatic (开机自动启动)

4. 服务管理命令：
   ```batch
   # 启动服务
   nssm start YourServiceName
   
   # 停止服务
   nssm stop YourServiceName
   
   # 重启服务
   nssm restart YourServiceName
   
   # 删除服务
   nssm remove YourServiceName
   ```

5. 也可以通过Windows服务管理器管理：
   - 按Win+R，输入services.msc
   - 找到你创建的服务
   - 右键可以进行启动、停止、重启等操作

注意事项：
1. 确保使用管理员权限执行上述操作
2. 记得设置正确的Java路径
3. 建议在服务安装前先测试命令是否能正常运行
4. 可以在NSSM的Log标签页配置日志输出，方便排查问题
5. 建议设置服务的恢复选项，在服务失败时自动重启

如果遇到问题，可以查看Windows事件查看器中的系统日志来排查原因。

---

_**User**_

使用 NSSM 工具，需要额外安装软件。不能直接在 Windows 系统上安装服务吗？

---

_**Assistant**_

是的，我们可以直接使用 Windows 自带的 `sc` 命令或者使用 Windows Service Wrapper (WinSW) 来创建 Windows 服务，无需安装额外软件。我来介绍这两种方法：

方法一：使用 sc 命令（最原生的方式）
```batch
sc create "YourServiceName" binPath= "java -jar -Dspring.profiles.active=prod -Dlog4j2.formatMsgNoLookups=true D:\path\to\your\zyxcx.jar" start= auto
```

但是这种方式有一些局限性：
1. 服务异常退出后不会自动重启
2. 没有日志记录
3. 配置选项较少

方法二：使用 WinSW（推荐）
这种方式只需要下载一个 .NET 可执行文件，配置更灵活：

1. 下载 WinSW：
   - 从 GitHub 下载最新版本的 WinSW.exe
   - 将其重命名为你想要的服务名称，例如 `myapp-service.exe`

2. 在同目录创建配置文件 `myapp-service.xml`：
```xml
<service>
  <id>MyApp</id>
  <name>My Java Application</name>
  <description>Java Application Windows Service</description>
  <executable>java</executable>
  <arguments>-jar -Dspring.profiles.active=prod -Dlog4j2.formatMsgNoLookups=true zyxcx.jar</arguments>
  <logmode>rotate</logmode>
  <logpath>logs</logpath>
  <startmode>Automatic</startmode>
  <workingdirectory>%BASE%</workingdirectory>
  <!-- 失败自动重启 -->
  <onfailure action="restart" delay="10 sec"/>
  <onfailure action="restart" delay="20 sec"/>
  <onfailure action="none"/>
</service>
```

3. 安装服务（以管理员身份运行命令提示符）：
```batch
myapp-service.exe install
```

4. 管理服务：
```batch
# 启动服务
myapp-service.exe start

# 停止服务
myapp-service.exe stop

# 卸载服务
myapp-service.exe uninstall
```

WinSW 的优点：
1. 配置灵活，支持丰富的选项
2. 自动记录标准输出和错误日志
3. 支持服务失败自动重启
4. 支持优雅关闭
5. 支持环境变量配置

注意事项：
1. 确保 Java 环境变量正确配置
2. 路径中最好不要包含中文和空格
3. 建议使用绝对路径
4. 服务安装和卸载需要管理员权限
5. 可以通过 services.msc 查看和管理服务

如果你的应用比较简单，可以使用 `sc` 命令；如果需要更多功能（如日志记录、自动重启等），建议使用 WinSW。

---

