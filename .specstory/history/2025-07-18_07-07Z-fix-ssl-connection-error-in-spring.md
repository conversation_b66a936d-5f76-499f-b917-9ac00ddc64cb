<!-- Generated by SpecStory -->

# Fix SSL connection error in Spring (2025-07-18 07:07Z)

_**User**_

修复以下错误：
```
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'pacsDataSource' defined in class path resource [com/ruoyi/framework/config/DruidConfig.class]: “Encrypt”属性设置为“true”且 “trustServerCertificate”属性设置为“false”，但驱动程序无法使用安全套接字层 (SSL) 加密与 SQL Server 建立安全连接:错误:PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target。 ClientConnectionId:aa2a5875-ea7b-4d99-9496-26fc32a75d15
```

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户遇到了一个Spring Boot应用中数据源配置的错误。根据错误信息，这是一个SQL Server连接的SSL证书验证问题。

错误信息显示：
1. "Encrypt"属性设置为"true"
2. "trustServerCertificate"属性设置为"false" 
3. 驱动程序无法使用SSL与SQL Server建立安全连接
4. PKIX path building failed - 无法找到有效的证书路径

看一下配置文件，在application-prod.yml中有pacs数据源的配置：
```yaml
pacs:
  url: **********************************************************************
  driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
  username: ZY
  password: Xjetvm@2021
  enabled: true
```

这个SQL Server连接字符串没有显式设置SSL相关参数，但是默认情况下新版本的SQL Server JDBC驱动会要求SSL连接。

有几种解决方案：
1. 在连接字符串中添加`trustServerCertificate=true`来信任服务器证书
2. 在连接字符串中添加`encrypt=false`来禁用加密
3. 配置正确的SSL证书

最简单的解决方案是修改连接字符串，添加SSL相关参数。

我需要修改application-prod.yml文件中的pacs数据源URL配置。

让我看看当前的配置：
```yaml
pacs:
  url: **********************************************************************
</details></think>

---

我来分析一下这个错误。这是一个SQL Server连接的SSL证书验证问题。错误显示：

1. SQL Server JDBC驱动默认启用了SSL加密（`Encrypt=true`）
2. 不信任服务器证书（`trustServerCertificate=false`）
3. 无法找到有效的证书路径来验证服务器证书

查看配置文件，我发现PACS数据源的连接字符串缺少SSL相关参数。我来修复这个问题：

---



---

<think><details><summary>Thought Process</summary>我已经修复了SQL Server连接字符串，添加了两个重要参数：
1. `trustServerCertificate=true` - 信任服务器证书，跳过证书验证
2. `encrypt=true` - 保持加密连接

这样既保持了安全性，又解决了证书验证问题。这种配置适合内网环境，因为内网服务器通常使用自签名证书或没有正确配置证书链。

修复已经完成，用户可以重启应用测试连接。</details></think>

---

修复完成！我在PACS数据源的连接字符串中添加了两个关键参数：

**修改内容：**
- 添加了 `trustServerCertificate=true` - 信任服务器证书，跳过证书验证
- 添加了 `encrypt=true` - 保持加密连接

**解决方案说明：**
这个错误是因为SQL Server JDBC驱动的新版本默认要求SSL连接，但无法验证服务器证书。通过设置 `trustServerCertificate=true`，我们告诉驱动程序信任服务器证书，这在内网环境中是常见的做法。

**修复后的连接字符串：**
```
**********************************************************************;trustServerCertificate=true;encrypt=true
```

现在可以重启应用程序，PACS数据源应该能够正常连接了。这种配置既保持了连接的安全性，又解决了证书验证问题，适合企业内网环境使用。

---

_**User**_

这个用于验证数据库连接的查询语句`validationQuery: SELECT 1 FROM DUAL`对于SQL Server无效

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户指出了一个问题，`validationQuery: SELECT 1 FROM DUAL` 这个验证查询语句是 Oracle 数据库的语法，对于 SQL Server 来说无效。

让我检查一下配置文件中 validationQuery 的设置情况。从之前的文件内容来看，在 application-prod.yml 中有一个全局的 validationQuery 设置：

```yaml
validationQuery: SELECT 1 FROM DUAL
```

这个设置在 druid 的配置下，会影响所有数据源。但是项目中有多个数据源：
1. master - MySQL 数据库
2. slave - MySQL 数据库  
3. pacs - SQL Server 数据库

`SELECT 1 FROM DUAL` 是 Oracle 的语法，在 MySQL 中也可以工作（MySQL 兼容这个语法），但在 SQL Server 中无效。

SQL Server 的正确验证查询应该是 `SELECT 1`。

但是这里有个问题，因为是全局配置，我们需要为不同的数据源设置不同的验证查询，或者使用一个通用的查询。

最简单的解决方案是：
1. 把全局的 validationQuery 改为 `SELECT 1`，这个在 MySQL 和 SQL Server 中都有效
2. 或者为每个数据源单独配置 validationQuery

我建议使用第一种方案，因为 `SELECT 1` 在大多数数据库中都是有效的。</details></think>

---

您说得对！`SELECT 1 FROM DUAL` 是 Oracle 数据库的语法，对于 SQL Server 无效。而且您的项目中有多个数据源（MySQL 和 SQL Server），需要使用通用的验证查询语句。

让我修复这个问题：

---



---

<think><details><summary>Thought Process</summary>好的，我已经成功修复了验证查询语句。现在使用 `SELECT 1` 替代 `SELECT 1 FROM DUAL`。

让我解释一下这个修改：

1. `SELECT 1 FROM DUAL` - Oracle 数据库专用语法
2. `SELECT 1` - 这是一个更通用的查询，在 MySQL、SQL Server、PostgreSQL 等大多数数据库中都有效

这样修改后，所有数据源（master MySQL、slave MySQL、pacs SQL Server）都能正确使用这个验证查询来检测连接是否有效。</details></think>

---

修复完成！我将验证查询从 `SELECT 1 FROM DUAL` 改为 `SELECT 1`。

**修改说明：**

- **原查询：** `SELECT 1 FROM DUAL` - 这是Oracle数据库的特有语法
- **新查询：** `SELECT 1` - 这是通用的SQL语法，支持多种数据库

**兼容性对比：**

| 数据库类型 | `SELECT 1 FROM DUAL` | `SELECT 1` |
|------------|---------------------|------------|
| MySQL      | ✅ 支持（兼容模式）    | ✅ 支持     |
| SQL Server | ❌ 不支持           | ✅ 支持     |
| Oracle     | ✅ 支持             | ✅ 支持     |
| PostgreSQL | ❌ 不支持           | ✅ 支持     |

现在您的项目中的所有数据源（master MySQL、slave MySQL、pacs SQL Server）都能正确使用 `SELECT 1` 来验证数据库连接的有效性了。这是一个更通用、更兼容的解决方案。

---

