<!-- Generated by SpecStory -->

# MySQL doctor_category 表建表语句 (2025-03-28 11:49Z)

_**User**_

为 doctor_category 表生成适用于 MySQL 的建表语句

---

_**Assistant**_

根据提供的 MyBatis Mapper 文件，我可以为您生成对应的 MySQL 建表语句。从 Mapper 文件中可以看出表的结构，我会包含所有相关字段：

```sql
CREATE TABLE `doctor_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '科室名称',
  `sort_no` int DEFAULT NULL COMMENT '排序号',
  `parent_id` bigint DEFAULT NULL COMMENT '父级ID',
  `category_code` varchar(50) DEFAULT NULL COMMENT '科室编码',
  `name_pinyin` varchar(200) DEFAULT NULL COMMENT '名称拼音',
  `name_pinyin_first` varchar(50) DEFAULT NULL COMMENT '名称拼音首字母',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `is_subspecialty` tinyint(1) DEFAULT NULL COMMENT '是否为亚专科 0-否 1-是',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_category_code` (`category_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医生科室分类表';

-- 关联表（用于医生和科室多对多关系）
CREATE TABLE `doctor_category_relationship` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `doctor_category_id` bigint NOT NULL COMMENT '科室ID',
  `doctor_id` bigint NOT NULL COMMENT '医生ID',
  PRIMARY KEY (`id`),
  KEY `idx_doctor_id` (`doctor_id`),
  KEY `idx_category_id` (`doctor_category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医生科室关联表';
```

我为表添加了以下特性：

1. 使用 `utf8mb4` 字符集和 `utf8mb4_unicode_ci` 排序规则，支持完整的 Unicode 字符
2. 为主要查询字段添加了索引
3. 添加了适当的字段注释
4. 设置了合适的字段类型和长度
5. 创建了医生和科室的关联表，支持多对多关系
6. 主键使用 `AUTO_INCREMENT` 自增

如果您需要调整任何字段的类型或长度，请告诉我。

---

_**User**_

为 ph_zhuyuan_patient 表生成适用于 MySQL 的建表语句

---

_**Assistant**_

根据提供的 MyBatis Mapper 文件，我可以为您生成 `ph_zhuyuan_patient` 表的 MySQL 建表语句：

```sql
CREATE TABLE `ph_zhuyuan_patient` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `id_card_no` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `patient_no` varchar(50) DEFAULT NULL COMMENT '患者编号',
  `zhuyuan_no` varchar(50) DEFAULT NULL COMMENT '住院号',
  `name` varchar(100) NOT NULL COMMENT '患者姓名',
  `gender` tinyint DEFAULT NULL COMMENT '性别',
  `department_code` varchar(50) DEFAULT NULL COMMENT '科室编码',
  `department_name` varchar(100) DEFAULT NULL COMMENT '科室名称',
  `bed_no` varchar(20) DEFAULT NULL COMMENT '床位号',
  `come_time` datetime DEFAULT NULL COMMENT '入院时间',
  `leave_time` datetime DEFAULT NULL COMMENT '出院时间',
  `state` varchar(20) DEFAULT NULL COMMENT '状态',
  `open_id` varchar(64) DEFAULT NULL COMMENT '微信OpenID',
  `union_id` varchar(64) DEFAULT NULL COMMENT '微信UnionID',
  `admission_number` varchar(50) DEFAULT NULL COMMENT '住院流水号',
  PRIMARY KEY (`id`),
  KEY `idx_id_card_no` (`id_card_no`),
  KEY `idx_patient_no` (`patient_no`),
  KEY `idx_zhuyuan_no` (`zhuyuan_no`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_admission_number` (`admission_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='住院患者信息表';
```

我为表添加了以下特性：

1. 使用 `utf8mb4` 字符集和 `utf8mb4_unicode_ci` 排序规则，支持完整的 Unicode 字符
2. 为主要查询字段添加了索引，包括：
   - 身份证号
   - 患者编号
   - 住院号
   - 微信OpenID
   - 住院流水号
3. 添加了适当的字段注释
4. 设置了合适的字段类型和长度：
   - 使用 `bigint` 作为主键
   - 身份证号使用 `varchar(18)`
   - 微信相关ID使用 `varchar(64)`
   - 时间字段使用 `datetime`
   - 性别使用 `tinyint`
5. 主键使用 `AUTO_INCREMENT` 自增

如果您需要调整任何字段的类型或长度，请告诉我。

---

_**User**_

为 ph_medical_record_mailing 表生成适用于 MySQL 的建表语句

---

_**Assistant**_

根据提供的 MyBatis Mapper 文件，我可以为您生成 `ph_medical_record_mailing` 表的 MySQL 建表语句：

```sql
CREATE TABLE `ph_medical_record_mailing` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `patient_no` varchar(50) DEFAULT NULL COMMENT '患者编号',
  `jz_card_no` varchar(50) DEFAULT NULL COMMENT '就诊卡号',
  `zhuyuan_no` varchar(50) DEFAULT NULL COMMENT '住院号',
  `name` varchar(100) NOT NULL COMMENT '患者姓名',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `id_card_no` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `openid` varchar(64) DEFAULT NULL COMMENT '微信openid',
  `address` varchar(500) DEFAULT NULL COMMENT '邮寄地址',
  `status` tinyint DEFAULT NULL COMMENT '状态',
  `medical_record_pages` int DEFAULT NULL COMMENT '病历页数',
  `copying_fee` decimal(10,2) DEFAULT NULL COMMENT '复印费用',
  `express_fee` decimal(10,2) DEFAULT NULL COMMENT '快递费用',
  `zy_pay_no` varchar(64) DEFAULT NULL COMMENT '住院支付单号',
  `wx_pay_no` varchar(64) DEFAULT NULL COMMENT '微信支付单号',
  `courier_number` varchar(50) DEFAULT NULL COMMENT '快递单号',
  `shutdown_reason` varchar(500) DEFAULT NULL COMMENT '关闭原因',
  `operator_id` bigint DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_patient_no` (`patient_no`),
  KEY `idx_jz_card_no` (`jz_card_no`),
  KEY `idx_zhuyuan_no` (`zhuyuan_no`),
  KEY `idx_id_card_no` (`id_card_no`),
  KEY `idx_openid` (`openid`),
  KEY `idx_wx_pay_no` (`wx_pay_no`),
  KEY `idx_courier_number` (`courier_number`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='病历邮寄记录表';
```

我为表添加了以下特性：

1. 使用 `utf8mb4` 字符集和 `utf8mb4_unicode_ci` 排序规则，支持完整的 Unicode 字符
2. 为主要查询字段添加了索引，包括：
   - 患者编号
   - 就诊卡号
   - 住院号
   - 身份证号
   - 微信openid
   - 微信支付单号
   - 快递单号
   - 创建时间
3. 设置了合适的字段类型和长度：
   - 使用 `decimal(10,2)` 存储金额
   - 使用 `datetime` 存储时间，并设置默认值
   - 使用 `tinyint` 存储状态
   - 地址和关闭原因使用 `varchar(500)` 以存储较长文本
4. 时间字段的特殊处理：
   - `create_time` 默认为记录创建时的时间戳
   - `update_time` 在记录更新时自动更新为当前时间戳
5. 主键使用 `AUTO_INCREMENT` 自增

如果您需要调整任何字段的类型或长度，请告诉我。

---

