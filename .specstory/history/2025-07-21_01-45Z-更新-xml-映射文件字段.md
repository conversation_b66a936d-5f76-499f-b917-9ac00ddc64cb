<!-- Generated by SpecStory -->

# 更新 XML 映射文件字段 (2025-07-21 01:45Z)

_**User**_

根据 @MedicalRecordMailing.java 更新 @MedicalRecordMailingMapper.xml ，把缺失的字段更新进去

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求我根据 MedicalRecordMailing.java 实体类更新 MedicalRecordMailingMapper.xml 文件，把缺失的字段更新进去。

我需要仔细比较这两个文件，看看实体类中有哪些字段在 Mapper.xml 文件中没有对应的映射。

首先看看 MedicalRecordMailing.java 中的字段：
1. id - 有
2. patientNo - 有
3. jzCardNo - 有  
4. zhuyuanNo - 有
5. name - 有
6. mobile - 有
7. idCardNo - 有
8. openid - 有
9. address - 有
10. status - 有
11. medicalRecordPages - 有
12. copyingFee - 有
13. expressFee - 有
14. zyPayNo - 有
15. wxPayNo - 有
16. courierNumber - 有
17. shutdownReason - 有
18. operatorId - 有
19. operatorName - 有
20. confirmedTime - **缺失**
21. mailedTime - **缺失**

从继承的 BaseEntity 中还有：
- createTime - 有
- updateTime - 有

看起来 MedicalRecordMailingMapper.xml 中缺少了两个字段：
1. confirmedTime (确认病历时间)
2. mailedTime (邮寄病历时间)

我需要在以下几个地方添加这两个字段：
1. resultMap 中的映射
2. selectMedicalRecordMailingVo 中的 SQL 查询字段
3. insertMedicalRecordMailing 中的插入字段
</details></think>

---

我来分析一下 `MedicalRecordMailing.java` 实体类和 `MedicalRecordMailingMapper.xml` 文件，找出缺失的字段。

通过对比，我发现 Mapper.xml 文件中缺少了两个字段：
1. `confirmedTime` (确认病历时间)
2. `mailedTime` (邮寄病历时间)

现在我来更新 Mapper.xml 文件，添加这两个缺失的字段：

---

```diff
          <result property="operatorId" column="operator_id"/>
          <result property="operatorName" column="operator_name"/>
+         <result property="confirmedTime" column="confirmed_time"/>
+         <result property="mailedTime" column="mailed_time"/>
          <result property="createTime" column="create_time"/>
          <result property="updateTime" column="update_time"/>
```

```diff
                 operator_id,
                 operator_name,
+                confirmed_time,
+                mailed_time,
                 create_time,
                 update_time
```

```diff
              <if test="operatorId != null">operator_id,</if>
              <if test="operatorName != null">operator_name,</if>
+             <if test="confirmedTime != null">confirmed_time,</if>
+             <if test="mailedTime != null">mailed_time,</if>
              <if test="createTime != null">create_time,</if>
              <if test="updateTime != null">update_time,</if>
```

```diff
              <if test="operatorId != null">#{operatorId},</if>
              <if test="operatorName != null">#{operatorName},</if>
+             <if test="confirmedTime != null">#{confirmedTime},</if>
+             <if test="mailedTime != null">#{mailedTime},</if>
              <if test="createTime != null">#{createTime},</if>
              <if test="updateTime != null">#{updateTime},</if>
```

```diff
              <if test="operatorId != null">operator_id = #{operatorId},</if>
              <if test="operatorName != null">operator_name = #{operatorName},</if>
+             <if test="confirmedTime != null">confirmed_time = #{confirmedTime},</if>
+             <if test="mailedTime != null">mailed_time = #{mailedTime},</if>
              <if test="createTime != null">create_time = #{createTime},</if>
              <if test="updateTime != null">update_time = #{updateTime},</if>
```

---

<think><details><summary>Thought Process</summary>完成了更新。我已经成功地在 MedicalRecordMailingMapper.xml 文件中添加了两个缺失的字段：

1. `confirmedTime` (确认病历时间) - 映射到数据库字段 `confirmed_time`
2. `mailedTime` (邮寄病历时间) - 映射到数据库字段 `mailed_time`

这些字段已经被添加到了：
1. resultMap 中的字段映射
2. selectMedicalRecordMailingVo 中的 SQL 查询字段列表
3. insertMedicalRecordMailing 中的插入字段和值
4. updateMedicalRecordMailing 中的更新字段

现在 Mapper.xml 文件与 MedicalRecordMailing.java 实体类中的所有字段保持一致了。</details></think>

---

已成功更新 `MedicalRecordMailingMapper.xml` 文件，添加了缺失的字段！

## 更新内容总结

我在 Mapper.xml 文件中添加了两个在 Java 实体类中存在但在 Mapper 中缺失的字段：

1. **`confirmedTime`** (确认病历时间) - 映射到数据库字段 `confirmed_time`
2. **`mailedTime`** (邮寄病历时间) - 映射到数据库字段 `mailed_time`

## 具体更新位置

✅ **resultMap 映射**：添加了字段到数据库列的映射关系
✅ **SQL 查询字段**：在 `selectMedicalRecordMailingVo` 中添加了查询字段
✅ **插入语句**：在 `insertMedicalRecordMailing` 中添加了插入字段和对应的值
✅ **更新语句**：在 `updateMedicalRecordMailing` 中添加了更新字段

现在 `MedicalRecordMailingMapper.xml` 文件与 `MedicalRecordMailing.java` 实体类中的所有字段完全一致，支持这两个时间字段的数据库操作。

---

