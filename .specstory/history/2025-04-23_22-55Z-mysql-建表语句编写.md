<!-- Generated by SpecStory -->

# MySQL 建表语句编写 (2025-04-23 22:55Z)

_**User**_

编写适用于 MySQL 数据库的建表语句

---

_**Assistant**_

<details>
            <summary>Searched codebase "编写适用于 MySQL 数据库的建表语句" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `ddl/ph/gen_table_column.sql` | L1-29 |
| `sql/ry_20200911.sql` | L684-713 |
| `ddl/ph/ph_article.sql` | L1-17 |
| `sql/ry_20200911.sql` | L713-737 |
| `ddl/ph/alipay_user_info.sql` | L1-22 |
| `ddl/ph/ph_tijian_package.sql` | L1-13 |
| `ddl/ph/gen_table.sql` | L1-27 |
| `ddl/ph/sys_file_info.sql` | L1-10 |
| `ddl/ph/ph_tijian_package_mapping.sql` | L1-9 |
| `ddl/ph/sys_user.sql` | L1-27 |
| `ddl/ph/ph_tijian_yuyue.sql` | L1-26 |
| `ddl/ph/ph_wx_subscribe_message.sql` | L1-16 |
| `ddl/ph/sys_logininfor.sql` | L1-16 |
| `ddl/ph/ph_category.sql` | L1-13 |
| `ddl/ph/sys_oper_log.sql` | L1-23 |
| `ddl/ph/sys_menu.sql` | L1-22 |
| `ddl/ph/mip_wx_order.sql` | L1-25 |
| `ddl/ph/ph_material.sql` | L1-18 |
| `sql/quartz.sql` | L1-48 |
| `ddl/ph/ph_yuyue.sql` | L1-21 |
| `ddl/ph/sys_dict_data.sql` | L1-21 |
| `ddl/ph/ph_wx_session.sql` | L1-15 |
| `sql/quartz.sql` | L48-90 |
| `ddl/ph/ph_tijian_package_item.sql` | L1-14 |
| `ddl/ph/ph_drug.sql` | L1-17 |

</details>

---

根据您提供的`MenzhenSettlement.java`文件，我将为您编写适用于MySQL数据库的建表语句：

```sql
CREATE TABLE `ph_menzhen_settlement` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `user_id` varchar(64) DEFAULT NULL COMMENT '用户ID',
  `patient_id` varchar(64) DEFAULT NULL COMMENT '患者ID',
  `card_no` varchar(64) DEFAULT NULL COMMENT '就诊卡号',
  `settlement_nos` varchar(64) DEFAULT NULL COMMENT '结算单号',
  `amount` decimal(12,2) DEFAULT NULL COMMENT '结算金额',
  `zy_order_no` varchar(64) DEFAULT NULL COMMENT '掌医订单号',
  `his_order_no` varchar(64) DEFAULT NULL COMMENT 'HIS订单号',
  `success` tinyint(1) DEFAULT NULL COMMENT '结算是否成功',
  `status_message` varchar(255) DEFAULT NULL COMMENT '结算状态消息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门诊结算';
```

这个建表语句包含了以下特点：
- 表名与Java类的`@TableName`注解保持一致
- 主键设置为自增长
- 根据Java类中的字段类型选择了对应的MySQL数据类型
- 添加了与Java类中相同的注释
- 使用了InnoDB引擎和utf8mb4字符集以支持完整的Unicode字符

---

