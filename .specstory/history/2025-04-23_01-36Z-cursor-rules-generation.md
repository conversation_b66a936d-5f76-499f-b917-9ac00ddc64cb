<!-- Generated by SpecStory -->

# Cursor Rules Generation (2025-04-23 01:36Z)

_**User**_

/Generate Cursor Rules 

---

_**Assistant**_

<think><details><summary>Thought Process</summary>I need to create Cursor Rules for the project to help with codebase navigation and understanding. Let me explore the project structure to understand what rules would be most useful.

First, let me check the top-level directories:</details></think>

---

我需要为您的项目创建Cursor规则。首先，让我查看项目结构。

---

<details>
            <summary>Listed current directory • **31** results</summary>
        
| Name |
|-------|
| 📁 `.idea` |
| 📁 `.git` |
| 📁 `.vscode` |
| 📁 `ruoyi-admin` |
| 📁 `xydlfy-ph` |
| 📁 `ruoyi-framework` |
| 📁 `ph-common` |
| 📁 `ruoyi-generator` |
| 📁 `ruoyi-quartz` |
| 📁 `ruoyi-system` |
| 📁 `ruoyi-common` |
| 📄 `pom.xml` |
| 📄 `README.md` |
| 📄 `mvn_install_jar.sh` |
| 📄 `prod-setting.patch` |
| 📄 `deploy.bat` |
| 📄 `start_prod.bat` |
| 📄 `package.sh` |
| 📄 `stop.sh` |
| 📁 `sql` |
| 📄 `start.sh` |
| 📄 `ry.sh` |
| 📁 `lib` |
| 📁 `doc` |
| 📁 `apidoc` |
| 📁 `avatar` |
| 📄 `backup_mysql.bat` |
| 📁 `bin` |
| 📁 `ddl` |
| 📄 `.gitignore` |
| 📄 `LICENSE` |

</details>

---

现在我来查看README文件，以了解项目的概述：

---

Read file: README.md

---

让我查看一下项目的pom.xml以了解项目结构和依赖关系：

---

Read file: pom.xml

---

我需要继续了解主要模块的结构，让我查看一些主要模块的结构：

---

<details>
            <summary>Listed directory ruoyi-admin • **4** results</summary>
        
| Name |
|-------|
| 📁 `target` |
| 📁 `logs` |
| 📄 `pom.xml` |
| 📁 `src` |

</details>

---

<details>
            <summary>Listed directory xydlfy-ph • **4** results</summary>
        
| Name |
|-------|
| 📁 `target` |
| 📄 `pom.xml` |
| 📄 `lombok.config` |
| 📁 `src` |

</details>

---

让我查看一下各个源代码目录：

---

<details>
            <summary>Listed directory ruoyi-admin/src • **2** results</summary>
        
| Name |
|-------|
| 📁 `test` |
| 📁 `main` |

</details>

---

<details>
            <summary>Listed directory ruoyi-admin/src/main • **2** results</summary>
        
| Name |
|-------|
| 📁 `resources` |
| 📁 `java` |

</details>

---

<details>
            <summary>Listed directory xydlfy-ph/src • **1** results</summary>
        
| Name |
|-------|
| 📁 `main` |

</details>

---

<details>
            <summary>Listed directory xydlfy-ph/src/main • **3** results</summary>
        
| Name |
|-------|
| 📁 `resources` |
| 📁 `kotlin` |
| 📁 `java` |

</details>

---

现在让我创建Cursor规则文件：

---

<details>
            <summary>Searched codebase "main application class" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `ruoyi-admin/src/main/java/com/ruoyi/RuoYiApplication.java` | L1-23 |
| `ruoyi-admin/src/main/java/com/ruoyi/RuoYiServletInitializer.java` | L1-16 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/config/ApplicationConfig.java` | L1-19 |
| `ry.sh` | L1-87 |
| `xydlfy-ph/pom.xml` | L76-134 |
| `xydlfy-ph/pom.xml` | L23-76 |
| `ruoyi-admin/src/main/java/com/ruoyi/RuoYiApplication.java` | L23-61 |
| `pom.xml` | L265-316 |
| `pom.xml` | L1-63 |
| `ruoyi-framework/src/main/java/com/ruoyi/framework/manager/ShutdownManager.java` | L1-59 |
| `ruoyi-admin/pom.xml` | L64-121 |
| `ruoyi-admin/src/main/resources/application.yml` | L1-62 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/webservice/impl/BSXmlWsEntryClassImpl.java` | L24-61 |
| `bin/run.bat` | L1-14 |
| `xydlfy-ph/pom.xml` | L174-219 |
| `start.sh` | L1-3 |
| `ruoyi-admin/src/main/resources/application.yml` | L167-195 |
| `xydlfy-ph/src/main/java/space/lzhq/ph/webservice/impl/BSXmlWsEntryClassImpl.java` | L1-24 |
| `deploy.bat` | L1-93 |
| `ruoyi-admin/src/main/resources/application.yml` | L62-118 |
| `xydlfy-ph/pom.xml` | L134-174 |
| `xydlfy-ph/src/main/resources/static/js/<EMAIL>` | L4571-4591 |
| `ruoyi-quartz/src/main/java/com/ruoyi/quartz/util/AbstractQuartzJob.java` | L1-64 |
| `.gitignore` | L1-44 |
| `ruoyi-framework/pom.xml` | L1-67 |

</details>

---

现在我已经对项目结构有了大致的了解，接下来我将创建Cursor规则文件。首先需要创建规则目录：

---

Could not resolve URI: Error: Unable to resolve nonexistent file '/Users/<USER>/Developer/Projects/zhangyi-xjetyy/.cursor'

---

让我创建规则目录：

---

```bash
mkdir -p .cursor/rules
```

```
rochuukyou@luzhongngdeiMac ~/D/P/zhangyi-xjetyy (master)> mkdir -p .cursor/rules
                                                          mkdir -p .cursor/rules
```

---

现在我将创建若干个规则文件：

1. 首先创建项目结构规则:

---

```diff
- 
+ # 项目结构
+ 
+ 这是一个基于若依(RuoYi)框架开发的新疆维吾尔自治区儿童医院掌医系统。
+ 
+ ## 主要模块
+ 
+ - [ruoyi-admin](mdc:ruoyi-admin/pom.xml): 系统启动模块，包含系统的入口应用 [RuoYiApplication.java](mdc:ruoyi-admin/src/main/java/com/ruoyi/RuoYiApplication.java)
+ - [ruoyi-framework](mdc:ruoyi-framework/pom.xml): 核心框架模块
+ - [ruoyi-system](mdc:ruoyi-system/pom.xml): 系统管理模块
+ - [ruoyi-quartz](mdc:ruoyi-quartz/pom.xml): 定时任务模块
+ - [ruoyi-generator](mdc:ruoyi-generator/pom.xml): 代码生成模块
+ - [ruoyi-common](mdc:ruoyi-common/pom.xml): 通用工具模块
+ - [xydlfy-ph](mdc:xydlfy-ph/pom.xml): 儿童医院掌医平台模块
+ - [ph-common](mdc:ph-common/pom.xml): 掌医平台通用模块
+ 
+ ## 技术栈
+ 
+ 项目使用了以下主要技术:
+ 
+ - 后端: Spring Boot 3.4.4、Shiro安全框架、MyBatis/MyBatis-Plus
+ - 前端: 未明确(可能使用微信小程序等)
+ - 数据库: MySQL(主要)和Oracle
+ - 缓存: Ehcache
+ - 定时任务: Quartz
+ 
+ ## 项目部署
+ 
+ 项目可以通过以下方式部署:
+ 
+ - 开发环境: Spring Boot 内嵌Tomcat运行
+ - 生产环境: 使用 [ry.sh](mdc:ry.sh) 或 [start.sh](mdc:start.sh) 脚本启动
+ 
```

---

2. 接下来创建应用架构规则:

---

```diff
- 
+ # 应用架构
+ 
+ 本项目基于若依(RuoYi)管理系统开发，并扩展了儿童医院掌医平台相关功能。
+ 
+ ## 架构层次
+ 
+ 系统整体采用经典的多层架构:
+ 
+ 1. **表示层**: 接口层，处理前端请求和返回响应
+ 2. **业务层**: 实现业务逻辑
+ 3. **数据访问层**: 处理数据库操作
+ 4. **通用层**: 提供工具类和通用功能
+ 
+ ## 关键组件
+ 
+ ### 核心启动类
+ [RuoYiApplication.java](mdc:ruoyi-admin/src/main/java/com/ruoyi/RuoYiApplication.java) - 系统启动入口
+ 
+ ### 主要配置类
+ - ApplicationConfig - 应用配置
+ - ShiroConfig - 安全框架配置
+ - DruidConfig - 数据库连接池配置
+ - MyBatisConfig - MyBatis配置
+ 
+ ### 安全框架
+ 系统使用Shiro作为安全框架，提供身份认证、授权等功能。
+ 
+ ### 定时任务
+ 系统使用Quartz框架管理定时任务，相关功能在[ruoyi-quartz](mdc:ruoyi-quartz/pom.xml)模块中实现。
+ 
+ ### 异步任务
+ 系统使用Spring的异步任务处理功能，由[AsyncManager](mdc:ruoyi-framework/src/main/java/com/ruoyi/framework/manager/AsyncManager.java)管理。
+ 
+ ### 掌医平台集成
+ 掌医平台功能主要在[xydlfy-ph](mdc:xydlfy-ph/pom.xml)模块中实现，集成了微信小程序、支付等功能。
+ 
```

---

3. 创建一个开发指南规则:

---

```diff
- 
+ # 开发指南
+ 
+ 本项目是新疆维吾尔自治区儿童医院掌医系统，基于若依(RuoYi)框架开发。
+ 
+ ## 开发环境
+ 
+ - JDK 21
+ - Maven 3.x
+ - MySQL 8.x 或 Oracle 19.x
+ - IDE: IntelliJ IDEA 或 Eclipse
+ 
+ ## 代码结构规范
+ 
+ ### 包命名规范
+ 
+ - 若依框架相关代码: `com.ruoyi.*`
+ - 掌医平台相关代码: `space.lzhq.ph.*`
+ 
+ ### 代码开发规范
+ 
+ 1. 类和方法应当有清晰的注释
+ 2. 方法名应当遵循驼峰命名法，并反映其功能
+ 3. 建议使用统一的日志记录方式
+ 4. 数据库访问推荐使用MyBatis映射
+ 
+ ## 常见任务指南
+ 
+ ### 添加新页面/功能
+ 
+ 1. 在xydlfy-ph模块中创建对应Controller
+ 2. 实现相关Service和Repository
+ 3. 如需，添加对应的前端视图
+ 
+ ### 集成第三方系统
+ 
+ 目前系统已集成:
+ - 微信小程序和支付
+ - HIS医院信息系统
+ - 医保移动支付
+ - 电子发票
+ 
+ 集成新的第三方系统时，建议参考现有的集成方式。
+ 
+ ### 部署应用
+ 
+ 开发环境:
+ ```bash
+ mvn clean package -DskipTests
+ java -jar ruoyi-admin/target/ruoyi-admin.jar
+ ```
+ 
+ 生产环境:
+ ```bash
+ sh ry.sh start
+ ```
+ 
+ 或者:
+ ```bash
+ sh start.sh
+ ```
+ 
```

---

4. 创建一个医院业务规则:

---

```diff
- 
+ # 医院业务指南
+ 
+ 本规则文件描述新疆维吾尔自治区儿童医院掌医系统的业务功能和实现。
+ 
+ ## 已实现功能
+ 
+ 系统目前实现了以下主要业务功能:
+ 
+ - 医生信息查询与展示
+ - 预约挂号
+ - 体检报告查询
+ - 检查报告查询
+ - 微信支付对接
+ - 医保支付对接
+ - 电子发票生成与查询
+ - 健康卡管理
+ - 儿童卡管理
+ 
+ ## 待实现功能
+ 
+ 根据README.md文件，系统仍需完善的功能包括:
+ 
+ - 检查报告接口
+ - 体检相关接口
+ - 儿童卡注册his监护人信息
+ - 预约成功后历史记录查询
+ - 支付宝小程序对接
+ 
+ ## 系统集成
+ 
+ ### HIS系统集成
+ - 内部地址: ***************:8090
+ - 通过网闸访问: 138.139.100.100:8090
+ - API文档地址: http://127.0.0.1:20001/doc.html
+ 
+ ### 医保移动支付
+ - 测试环境: 192.168.250.61:8082
+ - 生产环境: 192.168.250.60:8080
+ 
+ ### 电子发票系统
+ - 服务地址: http://192.168.250.92:8082/einv_forward/api/i
+ - 单位代码: 302032
+ - 单位名称: 新疆维吾尔自治区儿童医院
+ 
+ ## 开发注意事项
+ 
+ 1. HIS接口调用需通过网闸转发
+ 2. 对接医保移动支付时注意生产环境与测试环境的区分
+ 3. 电子发票功能需使用正确的单位代码和APPID
+ 4. 微信支付和小程序功能实现在xydlfy-ph模块中
+ 
```

---

5. 最后创建一个部署指南规则:

---

```diff
- 
+ # 部署指南
+ 
+ 本文档提供新疆维吾尔自治区儿童医院掌医系统的部署说明。
+ 
+ ## 系统环境要求
+ 
+ - JDK 21
+ - MySQL 8.x / Oracle 19.x
+ - 服务器: Linux 或 Windows Server
+ - 内存: ≥4GB
+ - 磁盘空间: ≥20GB
+ 
+ ## 部署步骤
+ 
+ ### 1. 打包应用
+ 
+ ```bash
+ mvn clean package -DskipTests
+ ```
+ 
+ ### 2. 配置数据库
+ 
+ 在生产环境中配置数据库连接，可修改application-prod.yml文件。
+ 
+ ### 3. 启动应用
+ 
+ #### Linux环境
+ 
+ 使用[ry.sh](mdc:ry.sh)脚本:
+ ```bash
+ # 启动应用
+ sh ry.sh start
+ # 停止应用
+ sh ry.sh stop
+ # 重启应用
+ sh ry.sh restart
+ # 查看状态
+ sh ry.sh status
+ ```
+ 
+ 或者使用[start.sh](mdc:start.sh)和[stop.sh](mdc:stop.sh):
+ ```bash
+ # 启动应用
+ sh start.sh
+ # 停止应用
+ sh stop.sh
+ ```
+ 
+ #### Windows环境
+ 
+ 使用[deploy.bat](mdc:deploy.bat)或[start_prod.bat](mdc:start_prod.bat)脚本启动应用。
+ 
+ ### 4. 服务器配置
+ 
+ #### 时钟同步
+ 时钟同步服务器: ***************
+ 
+ #### 端口映射
+ 外网IP: **************:8888
+ 内网地址: *************:8888
+ 域名: zy.xj-etyy.com
+ 
+ ## 服务器访问
+ 
+ ### 堡垒机访问
+ 访问地址: https://**************:6677/
+ 用户名: 掌医
+ 密码: 8.4mw!FGi9un!sJ6qAzV
+ 
+ ### 服务器信息
+ 服务器IP: *************
+ RDP远程连接用户名: administrator
+ 
+ ## 备份策略
+ 
+ 可以使用[backup_mysql.bat](mdc:backup_mysql.bat)脚本进行数据库备份。
+ 
```

---

我已经为您的项目创建了5个Cursor规则文件，放置在`.cursor/rules`目录下：

1. **project-structure.mdc**: 描述项目的整体结构和主要模块
2. **application-architecture.mdc**: 描述应用的架构设计和关键组件
3. **development-guide.mdc**: 提供开发环境配置和代码规范指南
4. **hospital-business-guide.mdc**: 描述医院业务功能和系统集成情况
5. **deployment-guide.mdc**: 提供部署步骤和服务器配置信息

这些规则文件使用Markdown格式编写，并通过`mdc:`链接引用了项目中的相关文件，可以帮助开发者更好地理解和导航项目代码。

---

