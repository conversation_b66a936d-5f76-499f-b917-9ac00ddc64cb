<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ruoyi</groupId>
    <artifactId>ruoyi</artifactId>
    <version>4.7.7</version>

    <name>zhangyi-xjetyy</name>
    <url>http://www.ruoyi.vip</url>
    <description>若依管理系统</description>

    <properties>
        <ruoyi.version>4.7.7</ruoyi.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>21</java.version>
        <kotlin.compiler.jvmTarget>${java.version}</kotlin.compiler.jvmTarget>
        <kotlin.compiler.javaParameters>true</kotlin.compiler.javaParameters>
        <kotlin.code.style>official</kotlin.code.style>

        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <shiro.version>2.0.5</shiro.version>
        <thymeleaf.extras.shiro.version>2.1.0</thymeleaf.extras.shiro.version>
        <druid.version>1.2.27</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>2.1.0</pagehelper.boot.version>
        <fastjson.version>1.2.83</fastjson.version>
        <oshi.version>6.8.0</oshi.version>
        <jna.version>5.5.0</jna.version>
        <commons.io.version>2.18.0</commons.io.version>
        <commons.beanutils.version>1.11.0</commons.beanutils.version>
        <commons.collections.version>4.5.0</commons.collections.version>
        <poi.version>5.4.0</poi.version>
        <velocity.version>2.3</velocity.version>
        <kotlin.version>2.2.0</kotlin.version>
        <kotlin.coroutines.version>1.10.2</kotlin.coroutines.version>
        <jetbrains.annotations.version>26.0.2</jetbrains.annotations.version>
        <jakarta.annotation.version>3.0.0</jakarta.annotation.version>
        <woodstox.version>7.1.1</woodstox.version>
        <cxf.version>4.1.2</cxf.version>
        <hutool.version>6.0.0-M17</hutool.version>
        <tinypinyin.version>2.0.3.RELEASE</tinypinyin.version>
        <weixin.version>4.7.3.B</weixin.version>
        <joox.version>1.6.2</joox.version>
        <thumbnailator.version>0.4.9</thumbnailator.version>
        <zxing.version>3.4.1</zxing.version>
        <xydlfy.version>1.0.0</xydlfy.version>
        <mospital.version>1.0.0</mospital.version>
        <spring-boot.version>3.5.3</spring-boot.version>
        <okhttp.version>4.12.0</okhttp.version>

        <fastexcel.version>1.2.0</fastexcel.version>

        <bc.version>1.81</bc.version>

        <mybatis-plus.version>3.5.11</mybatis-plus.version>

        <gelf.version>6.1.0</gelf.version>

        <mapstruct.version>1.6.3</mapstruct.version>
        <lombok.version>1.18.38</lombok.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <!-- https://mvnrepository.com/artifact/org.jetbrains.kotlin/kotlin-bom -->
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-bom</artifactId>
                <version>${kotlin.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.jetbrains.kotlinx/kotlinx-coroutines-bom -->
            <dependency>
                <groupId>org.jetbrains.kotlinx</groupId>
                <artifactId>kotlinx-coroutines-bom</artifactId>
                <version>${kotlin.coroutines.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.jetbrains/annotations -->
            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>${jetbrains.annotations.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.fasterxml.woodstox/woodstox-core -->
            <dependency>
                <groupId>com.fasterxml.woodstox</groupId>
                <artifactId>woodstox-core</artifactId>
                <version>${woodstox.version}</version>
            </dependency>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-3-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!--验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-bom</artifactId>
                <version>${shiro.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <!-- Shiro使用EhCache缓存框架 -->
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-ehcache</artifactId>
                <version>${shiro.version}</version>
            </dependency>

            <!-- thymeleaf模板引擎和shiro框架的整合 -->
            <dependency>
                <groupId>com.github.theborakompanioni</groupId>
                <artifactId>thymeleaf-extras-shiro</artifactId>
                <version>${thymeleaf.extras.shiro.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <!--io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons.beanutils.version}</version>
                <exclusions>
                    <!-- 排除过时的commons-collections版本 -->
                    <exclusion>
                        <groupId>commons-collections</groupId>
                        <artifactId>commons-collections</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-collections4 -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/jakarta.annotation/jakarta.annotation-api -->
            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>${jakarta.annotation.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-quartz</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-generator</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-framework</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-system</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.ruoyi</groupId>
                <artifactId>ruoyi-common</artifactId>
                <version>${ruoyi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.biezhi</groupId>
                <artifactId>TinyPinyin</artifactId>
                <version>${tinypinyin.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
                <version>${weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>wx-java-pay-spring-boot-starter</artifactId>
                <version>${weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${weixin.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jooq</groupId>
                <artifactId>joox-java-6</artifactId>
                <version>${joox.version}</version>
            </dependency>
            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>${thumbnailator.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/cn.idev.excel/fastexcel -->
            <dependency>
                <groupId>cn.idev.excel</groupId>
                <artifactId>fastexcel</artifactId>
                <version>${fastexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk18on</artifactId>
                <version>${bc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mospital</groupId>
                <artifactId>mospital-alipay</artifactId>
                <version>${mospital.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>de.siegmar</groupId>
                <artifactId>logback-gelf</artifactId>
                <version>${gelf.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.projectlombok/lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.mapstruct/mapstruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.projectlombok/lombok-mapstruct-binding -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok-mapstruct-binding.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>ruoyi-admin</module>
        <module>ruoyi-framework</module>
        <module>ruoyi-system</module>
        <module>ruoyi-quartz</module>
        <module>ruoyi-generator</module>
        <module>ruoyi-common</module>
        <module>xydlfy-ph</module>
        <module>ph-common</module>
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.14.0</version>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <release>${java.version}</release>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <!-- Maven Enforcer Plugin - 强制执行项目规则 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.6.1</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <id>enforce-rules</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <!-- 禁止冲突的日志实现 -->
                                <bannedDependencies>
                                    <searchTransitive>true</searchTransitive>
                                    <excludes>
                                        <!-- 禁止同时存在 log4j-slf4j2-impl 和 log4j-to-slf4j -->
                                        <exclude>*:log4j-slf4j2-impl</exclude>
                                        <!-- 禁止其他严重的日志冲突，但允许commons-logging -->
                                        <exclude>org.slf4j:slf4j-log4j12</exclude>
                                        <exclude>org.slf4j:slf4j-jdk14</exclude>
                                        <exclude>org.slf4j:slf4j-nop</exclude>
                                        <exclude>org.slf4j:slf4j-simple</exclude>
                                        <exclude>log4j:log4j</exclude>

                                        <!-- ========== 过时依赖检测 ========== -->
                                        <!-- 禁止使用过时的commons-collections版本（存在反序列化漏洞） -->
                                        <exclude>commons-collections:commons-collections:(,3.2.2]</exclude>
                                        <!-- 禁止使用过时的XStream版本（存在反序列化漏洞） -->
                                        <exclude>com.thoughtworks.xstream:xstream:(,1.4.20)</exclude>
                                        <!-- 禁止使用过时的HttpClient版本 -->
                                        <exclude>commons-httpclient:commons-httpclient</exclude>
                                        <!-- 禁止使用过时的Commons-IO版本 -->
                                        <exclude>commons-io:commons-io:(,2.7)</exclude>
                                        <!-- 禁止使用过时的Commons FileUpload版本 -->
                                        <exclude>commons-fileupload:commons-fileupload:(,1.5)</exclude>
                                        <!-- 禁止使用过时的Apache POI版本 -->
                                        <exclude>org.apache.poi:poi:(,5.0.0)</exclude>

                                        <!-- ========== 测试依赖泄露检测 ========== -->
                                        <!-- 确保测试依赖不会泄露到生产代码中 -->
                                        <exclude>junit:junit:*:jar:compile</exclude>
                                        <exclude>org.junit.jupiter:*:*:jar:compile</exclude>
                                        <exclude>org.mockito:*:*:jar:compile</exclude>
                                        <exclude>org.testng:testng:*:jar:compile</exclude>
                                        <exclude>org.hamcrest:*:*:jar:compile</exclude>
                                        <exclude>org.powermock:*:*:jar:compile</exclude>
                                        <exclude>org.easymock:*:*:jar:compile</exclude>
                                        <exclude>org.springframework:spring-test:*:jar:compile</exclude>
                                        <exclude>org.springframework.boot:spring-boot-test:*:jar:compile</exclude>
                                        <exclude>org.springframework.boot:spring-boot-test-autoconfigure:*:jar:compile
                                        </exclude>
                                        <exclude>com.h2database:h2:*:jar:compile</exclude>
                                        <exclude>org.testcontainers:*:*:jar:compile</exclude>

                                        <!-- ========== 安全漏洞版本检测 ========== -->
                                        <!-- 禁止使用过时的 Spring Boot 版本 -->
                                        <exclude>org.springframework.boot:*:(,3.0.0)</exclude>
                                        <!-- 禁止使用存在安全漏洞的 Jackson 版本 -->
                                        <exclude>com.fasterxml.jackson.core:*:(,2.15.0)</exclude>
                                        <!-- 禁止使用存在安全漏洞的 Log4j 版本 -->
                                        <exclude>org.apache.logging.log4j:*:(,2.17.0)</exclude>
                                    </excludes>
                                    <message>🚫 检测到禁用的依赖！

                                        【日志冲突】请使用 Spring Boot 默认的 Logback 作为日志实现

                                        【过时依赖】检测到过时的依赖版本，这些依赖可能存在安全漏洞：
                                        - commons-collections &lt;= 3.2.2 (反序列化漏洞)
                                        - xstream &lt; 1.4.20 (反序列化漏洞)
                                        - commons-httpclient (已废弃，请使用 Apache HttpClient)
                                        - commons-io &lt; 2.7 (安全漏洞)
                                        - commons-fileupload &lt; 1.5 (安全漏洞)
                                        - poi &lt; 5.0.0 (安全漏洞)

                                        【测试依赖泄露】以下测试框架不应出现在compile scope中：
                                        - JUnit (junit:junit, org.junit.jupiter:*)
                                        - Mockito (org.mockito:*)
                                        - TestNG (org.testng:testng)
                                        - Hamcrest (org.hamcrest:*)
                                        - PowerMock (org.powermock:*)
                                        - EasyMock (org.easymock:*)
                                        - Spring Test (org.springframework:spring-test)
                                        - Spring Boot Test (org.springframework.boot:spring-boot-test*)
                                        - H2 Database (com.h2database:h2)
                                        - TestContainers (org.testcontainers:*)

                                        【安全漏洞版本】检测到存在安全漏洞的依赖版本：
                                        - Spring Boot &lt; 3.0.0 (安全漏洞)
                                        - Jackson &lt; 2.15.0 (安全漏洞)
                                        - Log4j &lt; 2.17.0 (严重安全漏洞 CVE-2021-44228)

                                        解决方案：
                                        1. 升级到最新安全版本
                                        2. 确保测试依赖使用test scope
                                        3. 使用 mvn dependency:tree 分析依赖关系
                                        4. 对于安全漏洞，立即升级到安全版本
                                    </message>
                                </bannedDependencies>

                                <!-- ========== ClassPath冲突检测 ========== -->
                                <!-- 注意：banDuplicateClasses规则在Maven Enforcer Plugin 3.4.1中不可用 -->
                                <!-- 可以使用以下命令手动检测重复类：mvn dependency:tree -Dverbose -->
                                <!-- 或使用Maven Duplicate Finder Plugin进行更详细的重复类检测 -->

                                <!-- 使用requireUpperBoundDeps作为替代方案检测版本冲突 -->
                                <requireUpperBoundDeps>
                                    <excludes>
                                        <!-- 某些已知的兼容性排除 -->
                                        <exclude>org.slf4j:jcl-over-slf4j</exclude>
                                        <exclude>org.springframework.boot:spring-boot-starter-logging</exclude>
                                    </excludes>
                                    <message>🚫 检测到依赖版本冲突，可能导致ClassPath问题！

                                        版本冲突可能导致：
                                        - 运行时ClassNotFoundException
                                        - 方法不兼容异常
                                        - 不可预测的行为

                                        解决方案：
                                        1. 使用 mvn dependency:tree 分析依赖树
                                        2. 在 &lt;dependencyManagement&gt; 中统一版本
                                        3. 使用 &lt;exclusions&gt; 排除冲突的传递依赖
                                        4. 升级到兼容的版本

                                        注意：要检测重复类，推荐使用：
                                        mvn org.basepom.maven:duplicate-finder-maven-plugin:check
                                    </message>
                                </requireUpperBoundDeps>

                                <!-- 要求 Maven 版本 -->
                                <requireMavenVersion>
                                    <version>[3.6.0,)</version>
                                    <message>🔧 此项目需要 Maven 3.6.1 或更高版本</message>
                                </requireMavenVersion>

                                <!-- 要求 Java 版本 -->
                                <requireJavaVersion>
                                    <version>[21,)</version>
                                    <message>☕ 此项目需要 Java 21 或更高版本</message>
                                </requireJavaVersion>
                            </rules>
                            <fail>true</fail>
                            <failFast>false</failFast>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
