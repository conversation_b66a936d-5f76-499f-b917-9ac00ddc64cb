# API变更文档

## switchZhuyuanNo 接口升级

### 背景
本项目是为儿童医院建设的，面向的患者主要是儿童群体，而儿童通常没有身份证。因此需要调整`switchZhuyuanNo`接口的参数设计，将身份证号参数替换为姓名参数。

### 变更内容

#### 1. 原有接口 (已标记为 @Deprecated)
- **路径**: `POST /api/patient/switchZhuyuanNo`
- **状态**: 已废弃，但保持向后兼容
- **参数**:
  - `admissionNumber`: 住院号 (必填)
  - `idCardNo`: 身份证号 (必填)
  - `mobile`: 手机号 (必填)
  - `mode`: 操作模式，默认为"switch" (可选)

#### 2. 新版接口 (推荐使用)
- **路径**: `POST /api/patient/switchZhuyuanNoV2`
- **状态**: 新增，推荐使用
- **参数**:
  - `admissionNumber`: 住院号 (必填)
  - `patientName`: 患者姓名 (必填) - **替代身份证号**
  - `mobile`: 手机号 (必填)
  - `mode`: 操作模式，默认为"switch" (可选)

### 核心变更

| 项目 | 原有接口 | 新版接口 |
|------|----------|----------|
| 身份验证参数 | 身份证号 + 手机号 | 患者姓名 + 手机号 |
| 适用场景 | 有身份证的成人患者 | 儿童患者 (通常无身份证) |
| 匹配方式 | 精确匹配身份证号 | 中文姓名模糊匹配 |

### 使用示例

#### 原有接口示例
```bash
curl -X POST /api/patient/switchZhuyuanNo \
  -d "admissionNumber=202312001" \
  -d "idCardNo=110101199001011234" \
  -d "mobile=13800138000" \
  -d "mode=switch"
```

#### 新版接口示例
```bash
curl -X POST /api/patient/switchZhuyuanNoV2 \
  -d "admissionNumber=202312001" \
  -d "patientName=张小明" \
  -d "mobile=13800138000" \
  -d "mode=switch"
```

### 迁移建议

1. **立即行动**: 新的开发请使用 `switchZhuyuanNoV2` 接口
2. **渐进迁移**: 现有使用 `switchZhuyuanNo` 的代码可以继续使用，但建议逐步迁移
3. **废弃计划**: 待新版本稳定上线并确认无问题后，将删除废弃的旧版本接口

### 注意事项

1. **姓名匹配**: 新接口使用 `HisExt.matchesChinese` 进行中文姓名匹配，支持一定程度的模糊匹配
2. **手机号验证**: 两个接口的手机号验证逻辑保持一致
3. **返回格式**: 两个接口的返回数据格式完全一致
4. **权限要求**: 两个接口都需要 `@RequireSession` 权限

### 错误信息对比

| 场景 | 原有接口错误信息 | 新版接口错误信息 |
|------|------------------|------------------|
| 身份验证失败 | "您的身份证号或手机号与当前住院号信息不匹配" | "您的姓名或手机号与当前住院号信息不匹配" |
| 参数为空 | "身份证号不正确" | "患者姓名不能为空" |
| 手机号错误 | "手机号不正确" | "手机号不正确" | 