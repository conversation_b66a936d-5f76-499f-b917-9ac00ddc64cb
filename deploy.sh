#!/bin/bash

# =============================================================================
# Java应用部署脚本 (Windows Git Bash版本)
# 功能：自动构建、备份、部署Java应用
# 作者：自动生成
# 版本：2.0.0
# =============================================================================

set -euo pipefail  # exit on error, undefined var, or pipeline failure

# 配置路径 (Windows Git Bash格式)
readonly MOSPITAL_PATH="/d/zyxcx/projects/mospital"
readonly ZHANGYI_PATH="/d/zyxcx/projects/zhangyi-xjetyy"
readonly ZYXCX_JAR_PATH="/d/zyxcx/zyxcx.jar"
readonly BACKUP_DIR="/d/zyxcx/backup"
readonly MAX_BACKUPS=10

# 颜色配置
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly BOLD='\033[1m'
readonly NC='\033[0m' # No Color

# 全局变量
SKIP_MOSPITAL=false

# =============================================================================
# 实用函数
# =============================================================================

# 彩色日志输出
log_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "\n${BLUE}${BOLD}==== $1 ====${NC}\n"
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}${BOLD}Java应用部署脚本${NC}"
    echo ""
    echo -e "${BOLD}用法:${NC}"
    echo "  $0 [选项]"
    echo ""
    echo -e "${BOLD}选项:${NC}"
    echo "  --skip-mospital     跳过mospital项目构建"
    echo "  -h, --help          显示此帮助信息"
    echo ""
    echo -e "${BOLD}说明:${NC}"
    echo "  • 自动从Git拉取最新代码并构建项目"
    echo "  • 备份当前jar文件（保留最近${MAX_BACKUPS}个版本）"
    echo "  • 停止服务、替换jar、重启服务"
    echo "  • 支持回退操作（使用 ./rollback.sh）"
    echo ""
    echo -e "${BOLD}备份位置:${NC} ${BACKUP_DIR}"
    echo -e "${BOLD}目标文件:${NC} ${ZYXCX_JAR_PATH}"
    echo ""
}

# 检查必要的工具
check_prerequisites() {
    log_step "检查环境依赖"
    
    local missing_tools=()
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        missing_tools+=("git")
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        missing_tools+=("maven")
    fi
    
    # 检查sc命令（Windows服务控制）
    if ! command -v sc &> /dev/null; then
        missing_tools+=("sc (Windows Service Control)")
    fi
    
    # 检查date命令（用于时间戳生成，通常在Git Bash中已包含）
    if ! command -v date &> /dev/null; then
        missing_tools+=("date")
    fi
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        log_error "缺少必要工具："
        for tool in "${missing_tools[@]}"; do
            echo "  - $tool"
        done
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 停止Windows服务
stop_service() {
    local service_name="$1"
    local retry_count=0
    local max_retries=30
    
    log_info "检查服务状态: ${service_name}"
    
    # 首先检查服务是否存在并获取状态
    local service_status
    service_status=$(sc query "$service_name" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        log_warning "服务不存在或无法查询: $service_name"
        return 0  # 服务不存在时返回成功，允许继续部署
    fi
    
    # 检查服务是否正在运行
    if echo "$service_status" | grep -q "STOPPED"; then
        log_info "服务已处于停止状态，无需停止操作"
        return 0
    elif echo "$service_status" | grep -q "RUNNING"; then
        log_info "服务正在运行，开始停止操作"
        
        # 停止服务
        if ! sc stop "$service_name" > /dev/null 2>&1; then
            log_error "无法停止服务: $service_name"
            return 1
        fi
        
        # 等待服务完全停止
        while [ $retry_count -lt $max_retries ]; do
            if sc query "$service_name" | grep -q "STOPPED"; then
                log_success "服务 $service_name 已停止"
                return 0
            fi
            
            sleep 2
            ((retry_count++))
            
            if [ $((retry_count % 5)) -eq 0 ]; then
                log_info "等待服务停止... ($retry_count/${max_retries})"
            fi
        done
        
        log_error "服务停止超时"
        return 1
    else
        log_warning "服务状态未知，尝试直接停止"
        sc stop "$service_name" > /dev/null 2>&1
        log_info "已发送停止命令，继续部署流程"
        return 0
    fi
}

# 启动Windows服务
start_service() {
    local service_name="$1"
    
    log_info "检查服务状态: ${service_name}"
    
    # 首先检查服务是否存在并获取状态
    local service_status
    service_status=$(sc query "$service_name" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        log_error "服务不存在或无法查询: $service_name"
        return 1
    fi
    
    # 检查服务是否已经在运行
    if echo "$service_status" | grep -q "RUNNING"; then
        log_info "服务已处于运行状态，无需启动操作"
        return 0
    elif echo "$service_status" | grep -q "STOPPED"; then
        log_info "服务已停止，开始启动操作"
        
        if sc start "$service_name" > /dev/null 2>&1; then
            log_success "服务 $service_name 启动成功"
            return 0
        else
            log_error "服务启动失败"
            return 1
        fi
    else
        log_warning "服务状态未知，尝试启动"
        if sc start "$service_name" > /dev/null 2>&1; then
            log_success "服务 $service_name 启动成功"
            return 0
        else
            log_error "服务启动失败"
            return 1
        fi
    fi
}

# 生成时间戳
generate_timestamp() {
    date +"%Y%m%d_%H%M%S"
}

# 备份jar文件
backup_jar() {
    log_step "备份当前jar文件"
    
    # 检查当前jar文件是否存在
    if [ ! -f "$ZYXCX_JAR_PATH" ]; then
        log_warning "当前jar文件不存在，跳过备份"
        return 0
    fi
    
    # 创建备份目录
    if [ ! -d "$BACKUP_DIR" ]; then
        log_info "创建备份目录: $BACKUP_DIR"
        mkdir -p "$BACKUP_DIR" || {
            log_error "无法创建备份目录: $BACKUP_DIR"
            return 1
        }
    fi
    
    # 生成时间戳
    local timestamp
    timestamp=$(generate_timestamp)
    if [ -z "$timestamp" ]; then
        log_error "无法生成时间戳"
        return 1
    fi
    
    # 备份当前jar文件
    local backup_file="${BACKUP_DIR}/zyxcx_${timestamp}.jar"
    log_info "备份文件: $(basename "$backup_file")"
    
    if cp "$ZYXCX_JAR_PATH" "$backup_file"; then
        log_success "jar文件已备份至: $backup_file"
    else
        log_error "备份jar文件失败"
        return 1
    fi
    
    # 清理旧备份
    cleanup_old_backups
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份文件（保留最近${MAX_BACKUPS}个）"
    
    # 检查备份目录是否存在
    if [ ! -d "$BACKUP_DIR" ]; then
        return 0
    fi
    
    # 使用安全的文件枚举和排序方式
    local backup_files=()
    local temp_files=()
    
    # 启用nullglob避免glob模式无匹配时的问题
    shopt -s nullglob
    for file in "$BACKUP_DIR"/zyxcx_*.jar; do
        local basename_file
        basename_file=$(basename "$file")
        # 使用POSIX兼容的正则表达式匹配时间戳格式
        if [[ "$basename_file" =~ ^zyxcx_[0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]_[0-9][0-9][0-9][0-9][0-9][0-9]\.jar$ ]]; then
            temp_files+=("$file")
        fi
    done
    shopt -u nullglob
    
    # 按文件名排序（降序，最新的时间戳在前）
    # 由于文件名格式为zyxcx_YYYYMMDD_HHMMSS.jar，按文件名排序等同于按时间戳排序
    if [ ${#temp_files[@]} -gt 0 ]; then
        IFS=$'\n' backup_files=($(printf '%s\n' "${temp_files[@]}" | sort -r))
    fi
    
    # 删除超出保留数量的备份
    if [ ${#backup_files[@]} -gt $MAX_BACKUPS ]; then
        local files_to_delete=("${backup_files[@]:$MAX_BACKUPS}")
        for file in "${files_to_delete[@]}"; do
            if rm "$file" 2>/dev/null; then
                log_info "删除旧备份: $(basename "$file")"
            else
                log_warning "无法删除旧备份: $(basename "$file")"
            fi
        done
    fi
}

# 构建mospital项目
build_mospital() {
    log_step "构建 mospital 项目"
    
    cd "$MOSPITAL_PATH" || {
        log_error "无法进入mospital目录: $MOSPITAL_PATH"
        return 1
    }
    
    log_info "拉取最新代码..."
    if ! git fetch --all; then
        log_error "Git fetch失败"
        return 1
    fi
    
    if ! git checkout master; then
        log_error "Git checkout失败"
        return 1
    fi
    
    if ! git reset --hard origin/master; then
        log_error "Git reset失败"
        return 1
    fi
    
    log_info "开始Maven构建..."
    if mvn clean install -T 2C -Dmaven.test.skip=true; then
        log_success "Mospital构建完成"
    else
        log_error "Maven构建失败"
        return 1
    fi
}

# 构建zhangyi项目
build_zhangyi() {
    log_step "构建 zhangyi 项目"
    
    cd "$ZHANGYI_PATH" || {
        log_error "无法进入zhangyi目录: $ZHANGYI_PATH"
        return 1
    }
    
    log_info "拉取最新代码..."
    if ! git fetch --all; then
        log_error "Git fetch失败"
        return 1
    fi
    
    if ! git checkout master; then
        log_error "Git checkout失败"
        return 1
    fi
    
    if ! git reset --hard origin/master; then
        log_error "Git reset失败"
        return 1
    fi
    
    log_info "开始Maven构建..."
    if mvn clean package -T 2C -Dmaven.test.skip=true; then
        log_success "Zhangyi构建完成"
    else
        log_error "Maven构建失败"
        return 1
    fi
}

# 部署jar文件
deploy_jar() {
    log_step "部署jar文件"
    
    local source_jar="${ZHANGYI_PATH}/ruoyi-admin/target/zyxcx.jar"
    
    # 检查源文件是否存在
    if [ ! -f "$source_jar" ]; then
        log_error "源jar文件不存在: $source_jar"
        return 1
    fi
    
    log_info "复制jar文件..."
    log_info "源文件: $source_jar"
    log_info "目标文件: $ZYXCX_JAR_PATH"
    
    if cp "$source_jar" "$ZYXCX_JAR_PATH"; then
        log_success "jar文件复制成功"
    else
        log_error "jar文件复制失败"
        return 1
    fi
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-mospital)
                SKIP_MOSPITAL=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                echo "使用 $0 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    # 显示脚本标题
    echo -e "\n${PURPLE}${BOLD}========================================${NC}"
    echo -e "${PURPLE}${BOLD}        🚀 Java应用部署脚本${NC}"
    echo -e "${PURPLE}${BOLD}========================================${NC}\n"
    
    # 解析参数
    parse_arguments "$@"
    
    # 检查环境
    check_prerequisites
    
    # 构建项目
    if [ "$SKIP_MOSPITAL" = false ]; then
        build_mospital || exit 1
    else
        log_warning "跳过mospital项目构建"
    fi
    
    build_zhangyi || exit 1
    
    # 停止服务
    stop_service "zyxcx" || exit 1
    
    # 备份当前jar文件
    backup_jar || exit 1
    
    # 部署新jar文件
    deploy_jar || exit 1
    
    # 启动服务
    if start_service "zyxcx"; then
        log_step "部署完成"
        log_success "🎉 部署操作成功完成！"
        echo -e "\n${YELLOW}${BOLD}温馨提示：${NC}"
        echo -e "• 如果新版本出现问题，可以使用回退脚本：${CYAN}./rollback.sh${NC}"
        echo -e "• 备份文件位置：${CYAN}${BACKUP_DIR}${NC}"
        echo -e "• 保留备份数量：${CYAN}${MAX_BACKUPS}${NC} 个版本\n"
    else
        log_error "服务启动失败，新版本可能有问题"
        log_warning "建议使用回退脚本恢复：./rollback.sh"
        exit 1
    fi
}

# 错误处理
trap 'log_error "脚本执行过程中发生错误，退出码: $?"' ERR

# 执行主函数
main "$@" 